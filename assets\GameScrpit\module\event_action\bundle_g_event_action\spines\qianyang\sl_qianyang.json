{"skeleton": {"hash": "+6FS7+ZEPW5ExwdfPRJ9eeJaogM", "spine": "3.8.75", "x": -130.11, "y": -59.48, "width": 234.2, "height": 262.22, "images": "./images/", "audio": "D:/spine导出/灵兽动画/羬羊"}, "bones": [{"name": "root"}, {"name": "allbone", "parent": "root", "length": 494.99, "rotation": -179.83, "x": 10.8, "y": -12.99, "scaleX": 0.3431, "scaleY": 0.3431}, {"name": "allbone2", "parent": "allbone", "length": 69.57, "rotation": -175.31, "x": 15.84, "y": -229.9, "color": "ff006eff"}, {"name": "allbone3", "parent": "allbone2", "length": 56.16, "rotation": 55.2, "x": 69.57, "color": "ff006eff"}, {"name": "allbone4", "parent": "allbone3", "length": 47.94, "rotation": 29.05, "x": 56.16, "color": "ff006eff"}, {"name": "allbone5", "parent": "allbone", "length": 77.11, "rotation": -10.08, "x": 30.59, "y": -230.69, "color": "ff006eff"}, {"name": "allbone6", "parent": "allbone5", "length": 59.19, "rotation": -20.81, "x": 77.11, "color": "ff006eff"}, {"name": "allbone7", "parent": "allbone6", "length": 129.21, "rotation": 84.31, "x": 88.66, "y": -0.51}, {"name": "allbone8", "parent": "allbone7", "length": 109.59, "rotation": -96.41, "x": 129.21}, {"name": "allbone9", "parent": "allbone8", "length": 122.95, "rotation": -81.92, "x": 109.59}, {"name": "allbone10", "parent": "allbone9", "length": 101.69, "rotation": 80.03, "x": 126.29, "y": -2.23}, {"name": "allbone11", "parent": "allbone10", "length": 106.29, "rotation": 51.88, "x": 101.69}, {"name": "allbone12", "parent": "allbone2", "length": 137.26, "rotation": -126.42, "x": 81.97, "y": -13.39}, {"name": "allbone13", "parent": "allbone12", "length": 55.06, "rotation": 58.12, "x": 137.26}, {"name": "allbone14", "parent": "allbone13", "length": 51.4, "rotation": 36.1, "x": 55.06}, {"name": "allbone15", "parent": "allbone2", "length": 153.19, "rotation": -135.38, "x": 77.28, "y": -14.02}, {"name": "allbone16", "parent": "allbone15", "length": 72.68, "rotation": 88.95, "x": 153.19, "color": "abe323ff"}, {"name": "a1", "parent": "root", "x": 8.88, "y": 29.79, "color": "ff3f00ff"}, {"name": "allbone17", "parent": "allbone", "length": 99.27, "rotation": 179.54, "x": -64.17, "y": -32.8, "color": "35ff00ff"}, {"name": "a2", "parent": "allbone17", "rotation": 0.29, "x": -45.17, "y": 24.1, "color": "ff3f00ff"}, {"name": "a3", "parent": "a2", "x": 15.66, "y": -8, "transform": "noScale", "color": "ff3f00ff"}, {"name": "allbone18", "parent": "allbone5", "length": 128.62, "rotation": 123.77, "x": 80.22, "y": 13.24}, {"name": "allbone19", "parent": "allbone18", "length": 67.69, "rotation": -54.5, "x": 128.62}, {"name": "allbone20", "parent": "allbone19", "length": 57.92, "rotation": 94.57, "x": 67.69}, {"name": "allbone21", "parent": "allbone5", "length": 138.11, "rotation": 131.52, "x": 74.65, "y": 12.26}, {"name": "allbone22", "parent": "allbone21", "length": 82.78, "rotation": -75.78, "x": 138.11, "color": "abe323ff"}, {"name": "b1", "parent": "root", "x": -10.26, "y": 32.3, "color": "ff3f00ff"}, {"name": "allbone23", "parent": "allbone", "length": 68.95, "rotation": 179.12, "x": 38.87, "y": -30.38, "color": "13ff00ff"}, {"name": "b2", "parent": "allbone23", "rotation": 0.71, "x": -57.59, "y": 23.78, "color": "ff3f00ff"}, {"name": "b3", "parent": "b2", "x": 17.89, "y": -8.74, "transform": "noScale", "color": "ff3f00ff"}, {"name": "allbone24", "parent": "allbone5", "length": 112.87, "rotation": 98, "x": 115.75, "y": 13.8}, {"name": "allbone25", "parent": "allbone24", "length": 83.01, "rotation": -46.46, "x": 112.87}, {"name": "allbone26", "parent": "allbone25", "length": 54.94, "rotation": 105.95, "x": 83.01}, {"name": "c1", "parent": "root", "x": -40.94, "y": 35.68, "color": "ff3f00ff"}, {"name": "allbone29", "parent": "allbone", "length": 65.46, "rotation": 178.73, "x": 158.62, "y": -41.22, "color": "1eff00ff"}, {"name": "c2", "parent": "allbone29", "rotation": 1.09, "x": -55.11, "y": 26.05, "color": "ff3f00ff"}, {"name": "c3", "parent": "c2", "x": 16.3, "y": -9.72, "transform": "noScale", "color": "ff3f00ff"}, {"name": "allbone27", "parent": "allbone5", "length": 127.21, "rotation": 110.49, "x": 111.54, "y": 15.03}, {"name": "allbone28", "parent": "allbone27", "length": 98.49, "rotation": -76.07, "x": 127.21, "color": "abe323ff"}, {"name": "allbone30", "parent": "allbone2", "length": 141.43, "rotation": -29.07, "x": 69.12, "y": -49.58}, {"name": "allbone31", "parent": "allbone30", "length": 86, "rotation": -151.85, "x": 141.43}, {"name": "allbone32", "parent": "allbone31", "length": 50.47, "rotation": 81.35, "x": 86}, {"name": "allbone33", "parent": "allbone2", "length": 227.09, "rotation": -22.97, "x": 70.68, "y": -46.59}, {"name": "allbone34", "parent": "allbone33", "length": 172.29, "rotation": -162.93, "x": 227.09, "color": "abe323ff"}, {"name": "d1", "parent": "root", "x": 74.35, "y": 37.19, "color": "ff3f00ff"}, {"name": "allbone35", "parent": "allbone", "length": 112.22, "rotation": 133.13, "x": -95.58, "y": -73.18, "color": "1eff00ff"}, {"name": "d2", "parent": "allbone35", "rotation": 46.7, "x": -32.67, "y": 36.5, "color": "ff3f00ff"}, {"name": "d3", "parent": "d2", "x": -1.66, "y": -17.54, "transform": "noScale", "color": "ff3f00ff"}, {"name": "allbone36", "parent": "allbone4", "x": -16.55, "y": -47.31}, {"name": "allbone37", "parent": "allbone4", "x": 34.15, "y": 76.61}, {"name": "allbone38", "parent": "allbone2", "rotation": 55.2, "x": -12.33, "y": 53.08}, {"name": "allbone39", "parent": "allbone3", "x": 19.1, "y": -70.46}, {"name": "allbone40", "parent": "allbone2", "x": 83.54, "y": -52.44}, {"name": "allbone41", "parent": "allbone2", "x": -13.38, "y": 18.72}, {"name": "allbone42", "parent": "allbone4", "x": 83.77, "y": -63.08}, {"name": "allbone43", "parent": "allbone4", "x": 40.12, "y": -91.67, "color": "abe323ff"}, {"name": "allbone44", "parent": "allbone4", "x": 111.74, "y": -43.74, "color": "abe323ff"}, {"name": "allbone45", "parent": "allbone4", "x": 166.31, "y": -20.62}, {"name": "allbone46", "parent": "allbone45", "length": 54.73, "rotation": -120.55, "x": -4.69, "y": -7.09}, {"name": "allbone47", "parent": "allbone46", "length": 46.74, "rotation": 8.56, "x": 54.73}, {"name": "allbone48", "parent": "allbone47", "length": 49.32, "rotation": 21.06, "x": 46.74}, {"name": "allbone49", "parent": "allbone45", "length": 60.81, "rotation": 116.13, "x": -7.52, "y": 41.65}, {"name": "allbone50", "parent": "allbone49", "length": 53.33, "rotation": -5.94, "x": 60.09, "y": 0.91}, {"name": "allbone51", "parent": "allbone50", "length": 51.89, "rotation": -19.32, "x": 53.33}, {"name": "<PERSON><PERSON><PERSON>", "parent": "root", "rotation": -51.66, "x": -9.34, "y": 58.75, "scaleX": 1.1426, "scaleY": 1.1426}, {"name": "shouji2", "parent": "root", "rotation": -117.6, "x": -9.34, "y": 58.75, "scaleX": 1.1426, "scaleY": 1.1426}, {"name": "shouji3", "parent": "root", "rotation": 48.66, "x": 7.69, "y": 65.84, "scaleX": 0.8213, "scaleY": 0.8213}, {"name": "shouji4", "parent": "root", "rotation": 114.61, "x": 7.69, "y": 65.84, "scaleX": 0.8213, "scaleY": 0.8213}, {"name": "allbone52", "parent": "allbone2", "length": 127.38, "rotation": -76.5, "x": 85.52, "y": 1.98, "color": "ff006eff"}, {"name": "allbone53", "parent": "allbone52", "length": 72.02, "rotation": -44.43, "x": 127.38, "color": "ff006eff"}, {"name": "allbone54", "parent": "allbone53", "length": 46.89, "rotation": 88.46, "x": 71.81, "y": 0.42, "color": "ff006eff"}, {"name": "allbone55", "parent": "allbone2", "length": 139.94, "rotation": -65.79, "x": 91.63, "y": 1.46, "color": "ff006eff"}, {"name": "allbone56", "parent": "allbone55", "length": 87.16, "rotation": -74.73, "x": 139.94, "color": "ff006eff"}, {"name": "allbone57", "parent": "allbone", "length": 90, "rotation": 179.83, "x": -121.95, "y": -30.04, "color": "50ff00ff"}, {"name": "target1", "parent": "allbone57", "x": -14.11, "y": 87.69, "color": "ff3f00ff"}, {"name": "target2", "parent": "allbone57", "x": -46.29, "y": 22.86, "color": "ff3f00ff"}, {"name": "target3", "parent": "target2", "x": 14.39, "y": -7.46, "transform": "noScale", "color": "ff3f00ff"}], "slots": [{"name": "Shadow", "bone": "root", "attachment": "Shadow"}, {"name": "qianyang07", "bone": "allbone", "attachment": "qianyang07"}, {"name": "qianyang13", "bone": "allbone", "attachment": "qianyang011"}, {"name": "qianyang08", "bone": "allbone"}, {"name": "qianyang09", "bone": "allbone"}, {"name": "qianyang010", "bone": "allbone", "attachment": "qianyang010"}, {"name": "qianyang11", "bone": "allbone", "attachment": "qianyang011"}, {"name": "qianyang12", "bone": "allbone", "attachment": "qianyang012"}, {"name": "qianyang013", "bone": "allbone", "attachment": "qianyang013"}, {"name": "qianyang014", "bone": "allbone", "attachment": "qianyang014"}, {"name": "qianyang015", "bone": "allbone", "attachment": "qianyang015"}, {"name": "qianyang016", "bone": "allbone4", "attachment": "qianyang016"}, {"name": "qianyang017", "bone": "allbone42", "attachment": "qianyang018"}, {"name": "qianyang019", "bone": "allbone45", "attachment": "qianyang019"}, {"name": "qianyang020", "bone": "allbone45", "attachment": "qianyang020"}, {"name": "qianyang021", "bone": "allbone", "attachment": "qianyang021"}, {"name": "qianyang024", "bone": "allbone45"}, {"name": "qianyang026", "bone": "allbone"}, {"name": "shouji/tx_baodian02", "bone": "<PERSON><PERSON><PERSON>"}, {"name": "shouji/tx_baodian4", "bone": "shouji4"}, {"name": "shouji/tx_baodian2", "bone": "shouji2"}, {"name": "shouji/tx_baodian3", "bone": "shouji3"}], "ik": [{"name": "a1", "order": 2, "bones": ["allbone12"], "target": "a1", "compress": true, "stretch": true}, {"name": "a2", "order": 3, "bones": ["allbone13"], "target": "a2", "compress": true, "stretch": true}, {"name": "a3", "order": 4, "bones": ["allbone14"], "target": "a3"}, {"name": "a4", "bones": ["allbone15", "allbone16"], "target": "a2"}, {"name": "b1", "order": 7, "bones": ["allbone18"], "target": "b1", "compress": true, "stretch": true}, {"name": "b2", "order": 8, "bones": ["allbone19"], "target": "b2", "compress": true, "stretch": true}, {"name": "b3", "order": 9, "bones": ["allbone20"], "target": "b3"}, {"name": "b5", "order": 5, "bones": ["allbone21", "allbone22"], "target": "b2", "bendPositive": false}, {"name": "c1", "order": 12, "bones": ["allbone24"], "target": "c1", "compress": true, "stretch": true}, {"name": "c2", "order": 13, "bones": ["allbone25"], "target": "c2", "compress": true, "stretch": true}, {"name": "c3", "order": 14, "bones": ["allbone26"], "target": "c3"}, {"name": "c4", "order": 10, "bones": ["allbone27", "allbone28"], "target": "c2", "bendPositive": false}, {"name": "d1", "order": 17, "bones": ["allbone30"], "target": "d1", "compress": true, "stretch": true}, {"name": "d2", "order": 18, "bones": ["allbone31"], "target": "d2", "compress": true, "stretch": true}, {"name": "d3", "order": 19, "bones": ["allbone32"], "target": "d3"}, {"name": "d4", "order": 15, "bones": ["allbone33", "allbone34"], "target": "d2", "bendPositive": false}, {"name": "target1", "order": 22, "bones": ["allbone52"], "target": "target1", "compress": true, "stretch": true}, {"name": "target2", "order": 23, "bones": ["allbone53"], "target": "target2", "compress": true, "stretch": true}, {"name": "target3", "order": 24, "bones": ["allbone54"], "target": "target3"}, {"name": "target4", "order": 20, "bones": ["allbone55", "allbone56"], "target": "target2", "bendPositive": false}], "transform": [{"name": "a5", "order": 1, "bones": ["a1"], "target": "allbone16", "rotation": 40.55, "x": 22.01, "y": 21.39, "scaleX": 0.6569, "scaleY": 0.6569, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "b4", "order": 6, "bones": ["b1"], "target": "allbone22", "rotation": 135.89, "x": 16.6, "y": -18.18, "scaleX": 0.6569, "scaleY": 0.6569, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "c5", "order": 11, "bones": ["c1"], "target": "allbone28", "rotation": 155.09, "x": 18.26, "y": -24.33, "scaleX": 0.6569, "scaleY": 0.6569, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "d5", "order": 16, "bones": ["d1"], "target": "allbone34", "rotation": -176.77, "x": 87.01, "y": -10.65, "scaleX": 0.6569, "scaleY": 0.6569, "shearY": 360, "rotateMix": 0.8, "translateMix": 0.8, "scaleMix": 0.8, "shearMix": 0.8}, {"name": "e1", "order": 27, "bones": ["allbone46"], "target": "allbone44", "rotation": -120.55, "x": 49.88, "y": 16.03, "rotateMix": 0.372, "translateMix": 0.372, "scaleMix": 0.372, "shearMix": 0.372}, {"name": "e2", "order": 28, "bones": ["allbone49"], "target": "allbone44", "rotation": 116.13, "x": 47.06, "y": 64.77, "rotateMix": 0.33, "translateMix": 0.33, "scaleMix": 0.33, "shearMix": 0.33}, {"name": "eye", "order": 29, "bones": ["allbone42"], "target": "allbone43", "x": 43.65, "y": 28.59, "rotateMix": 0.178, "translateMix": 0.178, "scaleMix": 0.178, "shearMix": 0.178}, {"name": "face", "order": 25, "bones": ["allbone44"], "target": "allbone43", "x": 71.62, "y": 47.92, "rotateMix": -1, "translateMix": -1, "scaleMix": -1, "shearMix": -1}, {"name": "jiao", "order": 26, "bones": ["allbone45"], "target": "allbone43", "x": 126.19, "y": 71.04, "rotateMix": 0.043, "translateMix": 0.043, "scaleMix": 0.043, "shearMix": 0.043}, {"name": "jio5", "order": 21, "bones": ["target1"], "target": "allbone56", "rotation": 135.61, "x": 18.81, "y": -23.81, "rotateMix": 0.796, "translateMix": 0.796, "scaleMix": 0.796, "shearMix": 0.796}], "skins": [{"name": "default", "attachments": {"Shadow": {"Shadow": {"scaleX": 4.9567, "scaleY": 4.9567, "width": 42, "height": 24}}, "qianyang010": {"qianyang010": {"type": "mesh", "uvs": [0.73623, 0.10567, 0.78923, 0.03292, 0.85689, 0, 0.9099, 0.0134, 0.95726, 0.13051, 0.98658, 0.29022, 1, 0.46945, 0.9956, 0.63447, 0.95952, 0.8066, 0.91102, 0.93082, 0.79712, 1, 0.66292, 1, 0.50955, 0.96453, 0.36408, 0.92017, 0.22762, 0.85629, 0.09568, 0.75692, 0.02247, 0.67108, 0, 0.52549, 0.0052, 0.34108, 0.07182, 0.19355, 0.19025, 0.11978, 0.31978, 0.10037, 0.43575, 0.0829, 0.56158, 0.0829, 0.62696, 0.09649, 0.89016, 0.2053, 0.80594, 0.36813, 0.61824, 0.36055, 0.43776, 0.36055, 0.25968, 0.35677, 0.12733, 0.3757, 0.16824, 0.58775, 0.36797, 0.67105, 0.55808, 0.66726, 0.75781, 0.67105, 0.88294, 0.56503, 0.91423, 0.39842], "triangles": [36, 25, 5, 26, 0, 25, 25, 0, 1, 25, 4, 5, 1, 2, 25, 25, 3, 4, 25, 2, 3, 34, 35, 8, 8, 35, 7, 34, 26, 35, 7, 35, 6, 35, 36, 6, 35, 26, 36, 36, 5, 6, 26, 25, 36, 27, 0, 26, 9, 10, 34, 12, 33, 11, 10, 11, 34, 11, 33, 34, 9, 34, 8, 33, 27, 34, 34, 27, 26, 33, 28, 27, 27, 24, 0, 27, 23, 24, 13, 32, 12, 12, 32, 33, 13, 14, 32, 14, 31, 32, 32, 28, 33, 32, 29, 28, 29, 21, 28, 21, 22, 28, 28, 23, 27, 28, 22, 23, 15, 31, 14, 15, 16, 31, 16, 17, 31, 31, 29, 32, 17, 30, 31, 31, 30, 29, 17, 18, 30, 18, 19, 30, 30, 20, 29, 30, 19, 20, 29, 20, 21], "vertices": [5, 6, -91.16, -145.74, 0.00083, 5, -59.88, -103.86, 0.03816, 2, 69.75, 87.14, 0.10831, 3, 71.66, 49.58, 0.1836, 4, 37.62, 35.82, 0.66911, 4, 5, -74.08, -121.41, 0.00505, 2, 87.96, 100.48, 0.01627, 3, 93.01, 42.24, 0.03089, 4, 52.72, 19.03, 0.94779, 1, 4, 59.77, -2.58, 1, 1, 4, 57.3, -19.63, 1, 2, 3, 102.67, -14.44, 0.01651, 4, 33.64, -35.2, 0.98349, 2, 3, 79.13, -38.85, 0.4174, 4, 1.21, -45.12, 0.5826, 3, 2, 147.83, 6.01, 0.00872, 3, 49.59, -60.83, 0.88497, 4, -35.28, -49.98, 0.10631, 3, 2, 143.56, -27.41, 0.11841, 3, 19.71, -76.4, 0.87678, 4, -68.96, -49.09, 0.00481, 2, 2, 129.04, -61.42, 0.37127, 3, -16.5, -83.88, 0.62873, 2, 2, 111.39, -85.34, 0.55648, 3, -46.22, -83.04, 0.44352, 3, 5, -110.54, 72.5, 0.00358, 2, 73.76, -96.31, 0.77353, 3, -76.7, -58.39, 0.22289, 3, 5, -68.1, 79.91, 0.073, 2, 30.84, -92.65, 0.88249, 3, -98.19, -21.06, 0.04451, 2, 5, -18.36, 81.26, 0.40678, 2, -17.61, -81.27, 0.59322, 3, 6, -73.34, 58.12, 0.02097, 5, 29.2, 80.38, 0.80778, 2, -63.37, -68.29, 0.17125, 3, 6, -29.03, 69.29, 0.27318, 5, 74.59, 75.09, 0.71337, 2, -105.91, -51.59, 0.01345, 2, 6, 17.74, 73.5, 0.7807, 5, 119.8, 62.41, 0.21929, 2, 6, 46.88, 70.45, 0.94257, 5, 145.96, 49.2, 0.05743, 2, 6, 68.26, 48.6, 0.99605, 5, 158.18, 21.19, 0.00395, 1, 6, 86.04, 15.41, 1, 2, 6, 83.03, -21.39, 0.99595, 5, 147.13, -49.48, 0.00405, 5, 6, 58.03, -53.74, 0.87295, 5, 112.27, -70.85, 0.12335, 2, -105.12, 99.13, 0.00212, 3, -18.29, 200.02, 0.0009, 4, 32.04, 211.01, 0.00067, 5, 6, 24.31, -78.39, 0.50105, 5, 71.99, -81.91, 0.42989, 2, -63.35, 99.55, 0.04438, 3, 5.89, 165.97, 0.01413, 4, 36.64, 169.5, 0.01054, 5, 6, -5.87, -100.47, 0.19782, 5, 35.94, -91.83, 0.53523, 2, -25.96, 99.94, 0.16626, 3, 27.56, 135.49, 0.05671, 4, 40.78, 132.33, 0.04398, 5, 6, -40.59, -121.1, 0.04937, 5, -3.85, -98.78, 0.34367, 2, 14.29, 96.52, 0.30986, 3, 47.71, 100.48, 0.15581, 4, 41.4, 91.95, 0.14129, 5, 6, -60.05, -129.44, 0.01817, 5, -25, -99.67, 0.20553, 2, 34.96, 91.98, 0.30692, 3, 55.78, 80.91, 0.22234, 4, 38.95, 70.92, 0.24704, 2, 3, 78.7, -3.39, 0.0201, 4, 18.06, -13.9, 0.9799, 4, 5, -91.14, -54.97, 0.00054, 2, 87.51, 31.89, 0.0081, 3, 36.42, 3.47, 0.97998, 4, -15.57, 12.62, 0.01138, 5, 6, -85.16, -81.7, 0.00438, 5, -31.52, -46.12, 0.11736, 2, 27.61, 38.54, 0.61962, 3, 7.7, 56.46, 0.19003, 4, -14.95, 72.89, 0.06861, 5, 6, -35.36, -52.1, 0.07552, 5, 25.55, -36.15, 0.76824, 2, -30.12, 43.45, 0.11835, 3, -21.21, 106.66, 0.02293, 4, -15.85, 130.82, 0.01497, 5, 6, 14.18, -23.57, 0.71486, 5, 81.99, -27.07, 0.28145, 2, -87.01, 49.07, 0.00193, 3, -49.06, 156.59, 0.00101, 4, -15.95, 187.98, 0.00076, 1, 6, 48.73, 1.46, 1, 2, 6, 15.34, 31.94, 0.84389, 5, 102.8, 24.4, 0.15611, 3, 6, -48.46, 13.79, 0.00736, 5, 36.71, 30.11, 0.95741, 2, -57.81, -17.76, 0.03523, 2, 5, -23.27, 18.84, 0.15865, 2, 3.06, -22.17, 0.84135, 2, 2, 66.87, -28.37, 0.79745, 3, -24.84, -13.97, 0.20255, 3, 2, 108.73, -10.23, 0.07968, 3, 13.95, -37.99, 0.91814, 4, -55.35, -12.71, 0.00218, 2, 3, 48.41, -29.74, 0.83804, 4, -21.22, -22.23, 0.16196], "hull": 25, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 0, 48, 6, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72], "width": 321, "height": 204}}, "qianyang013": {"qianyang013": {"type": "mesh", "uvs": [0.68185, 0, 0.77072, 0.05054, 0.85129, 0.15614, 0.90461, 0.31018, 0.94608, 0.47541, 0.98636, 0.66175, 1, 0.80586, 0.98873, 0.8866, 0.86669, 0.8866, 0.7802, 0.83816, 0.7091, 0.90897, 0.59891, 0.96735, 0.50412, 0.96735, 0.53848, 0.89157, 0.54322, 0.79468, 0.47687, 0.82573, 0.41289, 0.84685, 0.40578, 0.76983, 0.41407, 0.70647, 0.31336, 0.70647, 0.20316, 0.65181, 0.15932, 0.56112, 0.07994, 0.47789, 0.04558, 0.38845, 0, 0.27416, 0.12496, 0.2344, 0.279, 0.21204, 0.36668, 0.16235, 0.44962, 0.07166, 0.54915, 0.00334, 0.58141, 0.09366, 0.73898, 0.14939, 0.74088, 0.2927, 0.53395, 0.21508, 0.44093, 0.3345, 0.27008, 0.36634, 0.11441, 0.32057, 0.75416, 0.46387, 0.79593, 0.64699, 0.86997, 0.77238, 0.92692, 0.83607, 0.5928, 0.40416, 0.54344, 0.54349, 0.51497, 0.67286, 0.45802, 0.78432, 0.66684, 0.59325, 0.68392, 0.73456, 0.63646, 0.85399, 0.57761, 0.92763, 0.45042, 0.45591, 0.37828, 0.53354, 0.27198, 0.55145, 0.19604, 0.46984, 0.32893, 0.44994], "triangles": [35, 26, 34, 26, 27, 34, 34, 27, 33, 27, 28, 33, 21, 22, 52, 51, 52, 53, 52, 22, 36, 22, 23, 36, 52, 35, 53, 52, 36, 35, 53, 34, 49, 53, 35, 34, 23, 24, 36, 36, 25, 35, 35, 25, 26, 36, 24, 25, 21, 52, 51, 12, 48, 11, 11, 47, 10, 11, 48, 47, 12, 13, 48, 48, 13, 47, 10, 47, 9, 13, 14, 47, 47, 46, 9, 47, 14, 46, 14, 43, 46, 8, 40, 7, 7, 40, 6, 8, 39, 40, 8, 9, 39, 39, 9, 38, 40, 39, 6, 39, 5, 6, 9, 46, 38, 39, 38, 5, 38, 4, 5, 38, 37, 4, 37, 3, 4, 41, 32, 37, 37, 32, 3, 34, 33, 41, 41, 33, 32, 31, 33, 30, 33, 31, 32, 32, 2, 3, 32, 31, 2, 33, 28, 30, 31, 1, 2, 30, 0, 31, 31, 0, 1, 28, 29, 30, 30, 29, 0, 43, 45, 46, 46, 45, 38, 43, 42, 45, 42, 41, 45, 45, 41, 37, 42, 49, 41, 49, 34, 41, 45, 37, 38, 16, 44, 15, 16, 17, 44, 15, 44, 14, 44, 43, 14, 17, 18, 44, 44, 18, 43, 43, 18, 50, 20, 51, 19, 18, 19, 50, 19, 51, 50, 43, 50, 42, 42, 50, 49, 20, 21, 51, 51, 53, 50, 50, 53, 49], "vertices": [6, 2, 111.11, 137.37, 9e-05, 3, 136.51, 44.28, 0.00175, 4, 91.74, -0.31, 0.71337, 48, 108.29, 47.01, 0.01697, 49, 57.58, -76.92, 0.11782, 56, -20, 43.44, 0.15, 5, 3, 137.12, 20.22, 0, 4, 80.59, -21.63, 0.73242, 48, 97.14, 25.68, 0.07526, 49, 46.44, -98.24, 0.04231, 56, -31.15, 22.11, 0.15, 5, 4, 56.92, -41.17, 0.61625, 48, 73.47, 6.14, 0.21952, 51, 106.81, 62.11, 0.00521, 49, 22.76, -117.78, 0.00903, 56, -54.82, 2.57, 0.15, 5, 4, 22.15, -54.4, 0.4025, 48, 38.7, -7.09, 0.36068, 51, 82.84, 33.66, 0.08588, 49, -12, -131.01, 0.00095, 56, -89.59, -10.65, 0.15, 5, 4, -15.2, -64.84, 0.18067, 48, 1.35, -17.53, 0.40876, 51, 55.26, 6.4, 0.2605, 52, 81.08, 76.94, 7e-05, 56, -126.94, -21.1, 0.15, 5, 4, -57.35, -75.08, 0.04931, 48, -40.79, -27.77, 0.28139, 51, 23.39, -23.02, 0.51859, 52, 87.05, 33.98, 0.00071, 56, -169.09, -31.34, 0.15, 5, 4, -90.01, -78.83, 0.00081, 48, -73.45, -31.52, 0.12765, 51, -3.34, -42.15, 0.71209, 52, 87.51, 1.11, 0.00946, 56, -201.74, -35.09, 0.15, 5, 3, -1.46, -119.44, 0.00184, 48, -91.82, -29.12, 0.01982, 51, -20.57, -48.98, 0.77228, 52, 83.28, -16.92, 0.05606, 56, -220.11, -32.69, 0.15, 5, 3, -15.96, -94.27, 0.00385, 48, -92.27, -0.08, 0.00042, 51, -35.06, -23.8, 0.65946, 52, 54.34, -14.46, 0.18628, 56, -220.56, -3.64, 0.15, 4, 3, -16.7, -70.94, 0.00585, 51, -35.8, -0.48, 0.44943, 52, 34.76, -1.76, 0.39472, 56, -209.88, 17.11, 0.15, 4, 3, -39.07, -64.3, 0.00417, 51, -58.17, 6.17, 0.22413, 52, 16.54, -16.34, 0.6217, 56, -226.21, 33.78, 0.15, 5, 2, 72.82, -79.76, 0.00267, 3, -63.64, -48.18, 0.00217, 51, -82.74, 22.28, 0.07366, 52, -10.72, -27.32, 0.77151, 56, -239.87, 59.8, 0.15, 5, 2, 50.34, -77.84, 0.04878, 3, -74.9, -28.63, 0.00127, 51, -94, 41.83, 0.00967, 52, -33.19, -25.41, 0.79028, 56, -240.22, 82.36, 0.15, 5, 2, 59.95, -61.4, 0.16843, 3, -55.91, -27.14, 0.00221, 51, -75.01, 43.33, 0.0003, 52, -23.59, -8.96, 0.67906, 56, -222.89, 74.44, 0.15, 5, 2, 62.94, -39.58, 0.36003, 3, -36.28, -17.14, 0.00331, 51, -55.39, 53.33, 0.00045, 52, -20.6, 12.86, 0.48621, 56, -200.88, 73.65, 0.15, 5, 2, 46.61, -45.26, 0.54466, 3, -50.27, -6.97, 0.00221, 51, -69.38, 63.49, 0.0003, 52, -36.93, 7.18, 0.30283, 56, -208.17, 89.34, 0.15, 5, 2, 31.03, -48.75, 0.67097, 3, -62.03, 3.84, 0.0011, 51, -81.13, 74.3, 0.00015, 52, -52.51, 3.69, 0.17777, 56, -213.2, 104.49, 0.15, 3, 2, 30.82, -31.18, 0.73982, 52, -52.71, 21.25, 0.11018, 56, -195.74, 106.45, 0.15, 4, 2, 34.01, -17.02, 0.74869, 52, -49.53, 35.42, 0.0633, 53, 47.39, -35.74, 0.03801, 56, -181.33, 104.7, 0.15, 5, 2, 10.13, -14.99, 0.65377, 52, -73.41, 37.45, 0.02592, 53, 23.51, -33.71, 0.15845, 50, -43.08, -57.29, 0.01186, 56, -181.7, 128.66, 0.15, 5, 2, -14.95, -0.4, 0.44725, 52, -98.49, 52.04, 0.0057, 53, -1.57, -19.12, 0.3045, 50, -45.41, -28.37, 0.09255, 56, -169.7, 155.08, 0.15, 5, 2, -23.6, 21, 0.20779, 53, -10.22, 2.28, 0.37931, 50, -32.78, -9.05, 0.26212, 49, -71.69, 45.47, 0.00078, 56, -149.28, 165.83, 0.15, 5, 2, -40.83, 41.42, 0.05675, 53, -27.44, 22.7, 0.28928, 50, -25.83, 16.75, 0.4994, 49, -53.09, 64.65, 0.00457, 56, -130.67, 185.01, 0.15, 5, 2, -47.25, 62.35, 0.00016, 53, -33.87, 43.63, 0.14802, 50, -12.32, 33.97, 0.67743, 49, -32.91, 73.14, 0.02439, 56, -110.5, 193.5, 0.15, 4, 53, -42.48, 70.4, 0.03519, 50, 4.75, 56.31, 0.6996, 49, -7.14, 84.39, 0.11521, 56, -84.72, 204.74, 0.15, 5, 4, 36.49, 131.4, 0.01159, 53, -12.08, 76.87, 0.00479, 50, 27.41, 35.04, 0.55077, 49, 2.34, 54.79, 0.28286, 56, -75.24, 175.14, 0.15, 4, 4, 42.13, 94.82, 0.06805, 50, 50.1, 5.8, 0.31395, 49, 7.98, 18.21, 0.468, 56, -69.6, 138.57, 0.15, 4, 4, 53.73, 74.13, 0.1938, 50, 70.29, -6.65, 0.12223, 49, 19.58, -2.48, 0.53397, 56, -58.01, 117.87, 0.15, 5, 4, 74.62, 54.71, 0.38368, 48, 91.18, 102.02, 1e-05, 50, 97.98, -13.49, 0.02191, 49, 40.47, -21.9, 0.4444, 56, -37.12, 98.45, 0.15, 6, 3, 120.09, 71.27, 0, 4, 90.49, 31.26, 0.57475, 48, 107.05, 78.57, 0.00252, 50, 123.24, -26.28, 1e-05, 49, 56.34, -45.35, 0.27272, 56, -21.25, 75.01, 0.15, 8, 2, 85.49, 118.21, 0.00625, 3, 106.16, 54.39, 0.01975, 4, 70.11, 23.27, 0.56794, 48, 86.66, 70.58, 0.00237, 53, 98.87, 99.49, 0.00032, 50, 109.3, -43.16, 0.00324, 49, 35.96, -53.34, 0.20012, 55, 29.99, 114.94, 0.2, 7, 3, 113.9, 15.57, 0.04267, 4, 58.04, -14.42, 0.65894, 48, 74.59, 32.89, 0.05749, 51, 94.8, 86.04, 0.00257, 52, 38.24, 154.86, 4e-05, 49, 23.88, -91.03, 0.03829, 55, 17.92, 77.24, 0.2, 8, 2, 119.48, 69.97, 5e-05, 3, 85.94, -1.05, 0.10332, 4, 25.52, -15.37, 0.51795, 48, 42.07, 31.94, 0.1079, 51, 66.83, 69.42, 0.05896, 52, 35.94, 122.41, 0.00486, 49, -8.64, -91.98, 0.00697, 55, -14.6, 76.29, 0.2, 8, 2, 71.9, 91.71, 0.03336, 3, 76.64, 50.42, 0.05647, 4, 42.38, 34.14, 0.38695, 48, 58.93, 81.45, 1e-05, 53, 85.28, 72.99, 0.00266, 50, 79.78, -47.13, 0.04908, 49, 8.22, -42.47, 0.27148, 55, 2.26, 125.81, 0.2, 7, 2, 47.54, 66.57, 0.1066, 3, 42.1, 56.08, 0.09745, 4, 14.93, 55.86, 0.1727, 53, 60.92, 47.85, 0.01059, 50, 45.24, -41.47, 0.16101, 49, -19.22, -20.75, 0.30165, 55, -25.19, 147.52, 0.15, 7, 2, 6.41, 62.82, 0.04404, 3, 15.54, 87.72, 0.05795, 4, 7.08, 96.41, 0.06966, 53, 19.79, 44.1, 0.00638, 50, 18.69, -9.83, 0.40813, 49, -27.08, 19.8, 0.26384, 55, -33.04, 188.07, 0.15, 6, 2, -29.62, 76.31, 0.0269, 3, 6.06, 125.01, 0.03343, 4, 16.9, 133.61, 0.02546, 53, -16.24, 57.59, 0.00688, 50, 9.21, 27.46, 0.63428, 49, -17.25, 57, 0.27306, 8, 2, 119.33, 30.99, 0.0001, 3, 53.84, -23.18, 0.17424, 4, -13.28, -19.13, 0.3003, 48, 3.27, 28.18, 0.14796, 51, 34.74, 47.29, 0.21157, 52, 35.79, 83.43, 0.0157, 49, -47.44, -95.74, 0.00013, 55, -53.4, 72.53, 0.15, 7, 2, 125.71, -11.27, 0.00016, 3, 22.78, -52.53, 0.15673, 4, -54.69, -29.71, 0.11755, 48, -38.14, 17.6, 0.12196, 51, 3.67, 17.93, 0.47336, 52, 42.17, 41.16, 0.03024, 55, -94.81, 61.95, 0.1, 7, 2, 140.85, -41.13, 0.00011, 3, 6.9, -82.01, 0.08853, 4, -82.88, -47.77, 0.01406, 48, -66.33, -0.46, 0.06259, 51, -12.2, -11.54, 0.69744, 52, 57.32, 11.31, 0.03727, 55, -123, 43.9, 0.1, 5, 2, 153.13, -56.68, 9e-05, 3, 1.14, -100.97, 0.0338, 48, -80.58, -14.23, 0.02299, 51, -17.97, -30.5, 0.89959, 52, 69.59, -4.25, 0.04352, 10, 2, 82.22, 47.75, 0.12336, 3, 46.43, 16.87, 0.46502, 4, -0.32, 19.47, 0.07593, 48, 16.23, 66.79, 0.00985, 51, 27.32, 87.33, 0.043, 52, -1.32, 100.19, 0.05742, 53, 95.6, 29.03, 7e-05, 50, 49.57, -80.68, 0.00084, 49, -34.48, -57.14, 0.02451, 55, -40.44, 111.14, 0.2, 10, 2, 67.83, 17.23, 0.28112, 3, 13.16, 11.27, 0.39489, 4, -32.13, 30.73, 0.05107, 48, -15.57, 78.04, 0.00469, 51, -5.95, 81.73, 0.01034, 52, -15.71, 69.67, 0.03732, 53, 81.21, -1.49, 0.00011, 50, 16.3, -86.28, 0.001, 49, -66.28, -45.88, 0.01946, 55, -72.25, 122.4, 0.2, 9, 2, 58.59, -11.45, 0.49362, 3, -15.68, 2.49, 0.19947, 4, -61.6, 37.06, 0.02567, 51, -34.78, 72.95, 0.00033, 52, -24.95, 40.98, 0.06954, 53, 71.97, -30.17, 7e-05, 50, -12.53, -95.06, 0.00063, 49, -95.75, -39.55, 0.01067, 55, -101.71, 128.72, 0.2, 9, 2, 42.93, -35.51, 0.5948, 3, -44.37, 1.61, 0.10819, 4, -87.1, 50.22, 0.00041, 51, -63.47, 72.07, 0.00035, 52, -40.61, 16.92, 0.093, 53, 56.32, -54.24, 5e-05, 50, -41.22, -95.94, 0.00039, 49, -121.26, -26.39, 0.00281, 55, -127.22, 141.89, 0.2, 10, 2, 96.13, 3.49, 0.03635, 3, 18.02, -19.82, 0.45125, 4, -42.97, 1.2, 0.05369, 48, -26.41, 48.51, 0.01596, 51, -1.08, 50.64, 0.08038, 52, 12.59, 55.93, 0.19513, 53, 109.51, -15.23, 4e-05, 50, 21.17, -117.37, 0.00051, 49, -77.12, -75.41, 0.0167, 55, -83.09, 92.86, 0.15, 9, 2, 97.46, -28.82, 0.02615, 3, -7.75, -39.35, 0.26378, 4, -74.98, -3.36, 0.0267, 48, -58.43, 43.95, 0.01148, 51, -26.85, 31.11, 0.09359, 52, 13.92, 23.62, 0.42084, 50, -4.6, -136.9, 0.00011, 49, -109.14, -79.97, 0.00735, 55, -115.1, 88.3, 0.15, 6, 2, 83.91, -54.87, 0.01657, 3, -36.88, -43.09, 0.11263, 48, -85.71, 54.82, 0.00599, 51, -55.98, 27.38, 0.0588, 52, 0.37, -2.44, 0.65601, 55, -142.38, 99.18, 0.15, 5, 2, 68.54, -70.34, 0.01436, 3, -58.36, -39.29, 0.03556, 48, -102.64, 68.57, 0.00089, 51, -77.46, 31.17, 0.04236, 52, -15, -17.91, 0.90683, 7, 2, 47.46, 38.92, 0.25425, 3, 19.34, 40.37, 0.13704, 4, -12.59, 53.18, 0.079, 53, 60.84, 20.2, 0.06497, 50, 22.49, -57.18, 0.08827, 49, -46.74, -23.43, 0.22648, 55, -52.71, 144.84, 0.15, 7, 2, 28.85, 22.82, 0.37569, 3, -4.5, 46.46, 0.11221, 4, -30.47, 70.07, 0.02402, 53, 42.24, 4.1, 0.13917, 50, -1.35, -51.09, 0.06535, 49, -64.63, -6.54, 0.13356, 55, -70.59, 161.74, 0.15, 7, 2, 3.3, 20.91, 0.41811, 3, -20.65, 66.36, 0.08406, 4, -34.93, 95.31, 0.00739, 53, 16.68, 2.19, 0.19447, 50, -17.5, -31.19, 0.07296, 49, -69.08, 18.7, 0.07301, 55, -75.05, 186.97, 0.15, 7, 2, -13.14, 40.9, 0.11069, 3, -13.61, 91.26, 0.03162, 4, -16.68, 113.66, 0.00149, 53, 0.24, 22.18, 0.20389, 50, -10.46, -6.29, 0.40111, 49, -50.84, 37.05, 0.10119, 55, -56.8, 205.33, 0.15, 7, 2, 18.76, 42.72, 0.11069, 3, 6.08, 66.11, 0.03162, 4, -11.68, 82.11, 0.00149, 53, 32.14, 24, 0.20389, 50, 9.23, -31.44, 0.40111, 49, -45.83, 5.5, 0.10119, 55, -51.8, 173.78, 0.15], "hull": 30, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 0, 58, 0, 60, 0, 62, 62, 64, 60, 66, 66, 68, 68, 70, 70, 72, 64, 74, 74, 76, 76, 78, 78, 80, 82, 84, 84, 86, 86, 88, 82, 90, 90, 92, 92, 94, 94, 96, 68, 98, 98, 100, 100, 102, 104, 106], "width": 238, "height": 227}}, "qianyang07": {"qianyang07": {"type": "mesh", "uvs": [0.8916, 0.06171, 0.99657, 0.17329, 0.99445, 0.29456, 0.89493, 0.40907, 0.76962, 0.48864, 0.67009, 0.53873, 0.62842, 0.61136, 0.49354, 0.66792, 0.43325, 0.73855, 0.42287, 0.82113, 0.53651, 0.86341, 0.64045, 0.92417, 0.64692, 0.99585, 0.33591, 1, 0.03496, 0.98769, 0.03019, 0.90437, 0.03906, 0.839, 3e-05, 0.73089, 0, 0.64128, 0.06246, 0.55069, 0.22841, 0.44984, 0.28233, 0.34295, 0.31274, 0.25091, 0.31695, 0.1087, 0.42685, 0.01782, 0.68865, 1e-05, 0.61563, 0.09512, 0.62951, 0.21487, 0.549, 0.33935, 0.43241, 0.46382, 0.32691, 0.55206, 0.19924, 0.64975, 0.1909, 0.74429, 0.17979, 0.84355, 0.23531, 0.92076, 0.4213, 0.93809], "triangles": [14, 34, 13, 13, 35, 12, 13, 34, 35, 35, 11, 12, 34, 15, 33, 33, 15, 16, 15, 34, 14, 35, 10, 11, 35, 9, 10, 35, 34, 9, 34, 33, 9, 33, 32, 9, 33, 16, 32, 16, 17, 32, 9, 32, 8, 32, 31, 8, 32, 17, 31, 8, 31, 7, 17, 18, 31, 31, 30, 7, 7, 30, 6, 18, 19, 31, 31, 19, 30, 19, 20, 30, 30, 29, 6, 6, 29, 5, 30, 20, 29, 5, 29, 4, 29, 28, 4, 4, 28, 3, 20, 21, 29, 29, 21, 28, 2, 3, 27, 21, 22, 28, 3, 28, 27, 28, 22, 27, 2, 27, 1, 22, 23, 27, 23, 26, 27, 27, 0, 1, 27, 26, 0, 23, 24, 26, 26, 25, 0, 26, 24, 25], "vertices": [1, 30, -6.7, 35.07, 1, 1, 30, 17.62, 49.11, 1, 1, 30, 44.54, 49.74, 1, 1, 30, 70.36, 38.05, 1, 1, 30, 88.54, 22.86, 1, 1, 30, 100.07, 10.7, 1, 2, 30, 116.36, 5.99, 0.45034, 31, -1.93, 6.66, 0.54966, 1, 31, 19.11, 4.75, 1, 2, 31, 35.2, 11.42, 0.96168, 32, 24.12, 42.83, 0.03832, 2, 31, 48.36, 24.26, 0.46256, 32, 32.84, 26.65, 0.53744, 2, 31, 43.89, 40.78, 0.08122, 32, 49.96, 26.41, 0.91878, 2, 31, 43.07, 59.57, 0.00491, 32, 68.25, 22.04, 0.99509, 1, 32, 77.47, 9.05, 1, 1, 32, 44.88, -12.74, 1, 1, 32, 11.41, -30.76, 1, 2, 31, 97.62, 5.2, 0.00995, 32, 0.98, -15.47, 0.99005, 2, 31, 87.14, -4.91, 0.62916, 32, -5.86, -2.62, 0.37084, 2, 30, 145.52, -72.27, 0.00921, 31, 74.88, -26.12, 0.99079, 2, 30, 125.64, -72.94, 0.05845, 31, 61.67, -40.99, 0.94155, 2, 30, 105.28, -65.73, 0.18877, 31, 42.42, -50.79, 0.81123, 2, 30, 82.2, -45.58, 0.63772, 31, 11.91, -53.63, 0.36228, 2, 30, 58.26, -39.58, 0.93734, 31, -8.93, -66.85, 0.06266, 2, 30, 37.71, -36.43, 0.99506, 31, -25.37, -79.58, 0.00494, 1, 30, 6.14, -36.95, 1, 1, 30, -14.48, -23.78, 1, 1, 30, -19.53, 9.05, 1, 1, 30, 1.88, 0.56, 1, 1, 30, 28.39, 3.19, 1, 2, 30, 56.34, -6.02, 0.99785, 31, -34.57, -45.12, 0.00215, 2, 30, 84.45, -19.79, 0.85043, 31, -5.24, -34.23, 0.14957, 2, 30, 104.47, -32.42, 0.38652, 31, 17.71, -28.42, 0.61348, 2, 30, 126.68, -47.78, 0.06773, 31, 44.15, -22.9, 0.93227, 2, 30, 147.69, -48.13, 0.00288, 31, 58.88, -7.91, 0.99712, 2, 31, 74.56, 7.63, 0.40272, 32, 9.66, 6.03, 0.59728, 1, 32, 24.75, -4.69, 1, 2, 31, 65.76, 43.53, 0.01155, 32, 46.6, 4.63, 0.98845], "hull": 26, "edges": [0, 2, 2, 4, 10, 12, 12, 14, 18, 20, 24, 26, 26, 28, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 0, 4, 6, 20, 22, 22, 24, 34, 36, 32, 34, 28, 30, 30, 32, 14, 16, 16, 18, 6, 8, 8, 10, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70], "width": 126, "height": 222}}, "qianyang08": {"qianyang08": {"type": "mesh", "uvs": [0.46333, 0.55406, 0.46346, 0.5568, 0.42957, 0.58264, 0.40559, 0.57019, 0.33413, 0.57317, 0.2, 0.62382, 0.20859, 0.74392, 0.30469, 0.91295, 0.42413, 1, 0.48251, 1, 0.53999, 0.91382, 0.57591, 0.81486, 0.67059, 0.76235, 0.8246, 0.71382, 0.97506, 0.66299, 1, 0.59136, 1, 0.50818, 0.87939, 0.37099, 0.71458, 0.22441, 0.53, 0.0817, 0.36123, 0, 0.2373, 0, 0.1094, 0.04956, 0.01975, 0.15627, 0, 0.3067, 0.02766, 0.44299, 0.12918, 0.49313, 0.27817, 0.52141, 0.42089, 0.55162, 0.57514, 0.48975, 0.742, 0.4696, 0.57777, 0.50981, 0.75231, 0.52057, 0.61863, 0.60313, 0.45821, 0.70524, 0.34793, 0.78237, 0.5685, 0.39021, 0.38358, 0.31091, 0.15966, 0.20879], "triangles": [9, 8, 10, 8, 7, 10, 7, 35, 10, 10, 35, 11, 11, 35, 34, 7, 6, 35, 6, 4, 35, 34, 4, 2, 4, 34, 35, 6, 5, 4, 12, 11, 33, 2, 4, 3, 11, 34, 33, 12, 33, 13, 33, 32, 13, 13, 32, 14, 34, 1, 33, 1, 31, 33, 34, 2, 1, 14, 32, 15, 33, 31, 32, 1, 0, 31, 31, 0, 29, 32, 16, 15, 27, 37, 28, 29, 0, 36, 0, 28, 36, 28, 37, 36, 27, 26, 37, 31, 30, 32, 32, 30, 16, 31, 29, 30, 30, 17, 16, 26, 38, 37, 38, 26, 24, 29, 36, 30, 17, 30, 18, 26, 25, 24, 30, 36, 18, 18, 36, 19, 37, 38, 20, 38, 21, 20, 36, 37, 19, 37, 20, 19, 24, 23, 38, 23, 22, 38, 38, 22, 21], "vertices": [2, 39, 68.87, -29.65, 0.23439, 40, 77.95, -8.15, 0.76561, 2, 39, 69.07, -30.04, 0.20005, 40, 77.96, -7.71, 0.79995, 3, 39, 65.93, -36.03, 0.01343, 40, 83.55, -3.92, 0.92625, 41, -4.26, 1.79, 0.06032, 3, 39, 61.67, -35.75, 0.0011, 40, 87.18, -6.17, 0.56818, 41, -5.9, -2.15, 0.43072, 2, 40, 98.41, -6.46, 0.08941, 41, -4.37, -13.28, 0.91059, 2, 40, 119.98, 0.26, 0, 41, 5.72, -33.49, 1, 1, 41, 24.85, -30.32, 1, 1, 41, 50.53, -12.71, 1, 1, 41, 62.73, 7.3, 1, 2, 40, 79.74, 63.7, 0.00315, 41, 61.87, 16.43, 0.99685, 2, 40, 69.81, 50.47, 0.06441, 41, 47.21, 24.11, 0.93559, 2, 40, 63.11, 34.95, 0.38054, 41, 30.81, 28.22, 0.61946, 2, 40, 47.7, 27.52, 0.83158, 41, 21, 42.23, 0.16842, 2, 40, 23.03, 21.36, 0.99666, 41, 10.95, 65.59, 0.00334, 1, 40, -1.1, 14.8, 1, 2, 39, 148.21, -0.4, 0.29932, 40, -5.78, 3.55, 0.70068, 2, 39, 142.68, 11.8, 0.99963, 40, -6.67, -9.81, 0.00037, 1, 39, 116.29, 24.12, 1, 1, 39, 82.95, 34.96, 1, 1, 39, 47.04, 43.95, 1, 1, 39, 17.44, 45.02, 1, 1, 39, -0.31, 37, 1, 1, 39, -15.33, 21.46, 1, 1, 39, -21.08, 0, 1, 2, 39, -13.92, -23.34, 0.99871, 40, 147.92, -52.83, 0.00129, 2, 39, -0.91, -41.55, 0.98799, 40, 145.05, -30.63, 0.01201, 2, 39, 16.96, -42.33, 0.96316, 40, 129.68, -21.5, 0.03684, 2, 39, 40.18, -36.84, 0.84338, 40, 106.63, -15.37, 0.15662, 2, 39, 62.63, -32.04, 0.49329, 40, 84.58, -8.99, 0.50671, 2, 39, 80.61, -12.98, 0.59528, 40, 59.73, -17.29, 0.40472, 1, 39, 103.17, 0.77, 1, 2, 39, 82.32, -15.76, 0.45726, 40, 59.53, -14.04, 0.54274, 2, 39, 108.03, -6.04, 0.6656, 40, 32.29, -10.46, 0.3344, 2, 40, 54.13, 1.39, 0.99584, 41, -3.76, 31.68, 0.00416, 2, 40, 80.38, 16.08, 0.22319, 41, 14.97, 8.14, 0.77681, 1, 41, 28.96, -7.94, 1, 1, 39, 73.05, 1.19, 1, 1, 39, 41.29, 0.86, 1, 1, 39, 2.44, 1.35, 1], "hull": 29, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 0, 56, 0, 58, 58, 60, 60, 32, 2, 62, 62, 64, 64, 66, 66, 68, 68, 70, 60, 72, 72, 74, 74, 76, 72, 56], "width": 157, "height": 161}}, "qianyang016": {"qianyang016": {"type": "mesh", "uvs": [0.40554, 0, 0.5334, 0.00679, 0.6753, 0.09233, 0.78445, 0.20532, 0.83747, 0.33444, 0.89049, 0.34574, 0.85618, 0.38932, 0.86866, 0.491, 0.84839, 0.57654, 0.88425, 0.63142, 0.96689, 0.72181, 1, 0.77991, 0.98093, 0.88967, 0.92167, 0.96552, 0.84059, 1, 0.72676, 1, 0.63476, 0.97682, 0.53808, 0.90742, 0.35564, 0.88967, 0.20906, 0.82026, 0.07652, 0.72181, 0, 0.63142, 0, 0.46356, 0.00635, 0.307, 0.07496, 0.15528, 0.22778, 0.01809, 0.57014, 0.13345, 0.67763, 0.29579, 0.70935, 0.45265, 0.74459, 0.60221, 0.82389, 0.70618, 0.90847, 0.79373, 0.91904, 0.87763, 0.87675, 0.93782, 0.33906, 0.19049, 0.41978, 0.36055, 0.47643, 0.50128, 0.57416, 0.70212, 0.7257, 0.83406, 0.79651, 0.89123, 0.73208, 0.24983, 0.79878, 0.42089, 0.81508, 0.49376, 0.80693, 0.59041, 0.86325, 0.67019, 0.94551, 0.76454, 0.96256, 0.82591, 0.52544, 0.60199, 0.16603, 0.41637, 0.25422, 0.60328, 0.3634, 0.77426, 0.54818, 0.82352, 0.68536, 0.93219, 0.75955, 0.96117, 0.07224, 0.55257, 0.16603, 0.69022], "triangles": [26, 1, 2, 0, 1, 26, 34, 25, 0, 34, 0, 26, 24, 25, 34, 40, 2, 3, 26, 2, 40, 27, 26, 40, 40, 3, 4, 35, 34, 26, 35, 26, 27, 6, 4, 5, 48, 24, 34, 48, 34, 35, 23, 24, 48, 40, 41, 27, 41, 4, 6, 4, 41, 40, 28, 27, 41, 36, 35, 27, 22, 23, 48, 41, 6, 7, 42, 41, 7, 28, 41, 42, 28, 36, 27, 54, 22, 48, 8, 42, 7, 42, 29, 28, 43, 42, 8, 47, 36, 28, 29, 47, 28, 42, 43, 29, 49, 48, 35, 49, 35, 36, 54, 48, 49, 21, 22, 54, 9, 43, 8, 44, 43, 9, 55, 54, 49, 20, 21, 54, 37, 47, 29, 30, 43, 44, 29, 43, 30, 55, 20, 54, 44, 9, 10, 45, 44, 10, 47, 50, 49, 47, 49, 36, 50, 47, 37, 55, 49, 50, 45, 10, 11, 31, 44, 45, 30, 44, 31, 19, 55, 50, 20, 55, 19, 51, 50, 37, 46, 45, 11, 31, 45, 46, 38, 37, 29, 38, 29, 30, 39, 38, 30, 51, 37, 38, 32, 31, 46, 31, 39, 30, 18, 19, 50, 18, 50, 51, 12, 46, 11, 32, 46, 12, 32, 39, 31, 17, 18, 51, 52, 51, 38, 52, 38, 39, 17, 51, 52, 33, 39, 32, 53, 52, 39, 14, 53, 39, 13, 32, 12, 33, 32, 13, 16, 17, 52, 15, 52, 53, 16, 52, 15, 33, 14, 39, 14, 33, 13, 15, 53, 14], "vertices": [2, 4, 177.75, -0.48, 0.7, 56, 66.01, 43.26, 0.3, 2, 4, 176.94, -23.13, 0.7, 56, 65.2, 20.62, 0.3, 2, 4, 162.7, -48.47, 0.7, 56, 50.96, -4.72, 0.3, 2, 4, 143.68, -68.08, 0.7, 56, 31.94, -24.34, 0.3, 2, 4, 121.74, -77.8, 0.7, 56, 10, -34.06, 0.3, 2, 4, 119.96, -87.22, 0.7, 56, 8.22, -43.47, 0.3, 2, 4, 112.41, -81.26, 0.7, 56, 0.67, -37.52, 0.3, 2, 4, 95.06, -83.74, 0.7, 56, -16.68, -39.99, 0.3, 2, 4, 80.38, -80.37, 0.7, 56, -31.36, -36.63, 0.3, 2, 4, 71.09, -86.87, 0.7, 56, -40.65, -43.12, 0.3, 2, 4, 55.86, -101.73, 0.7, 56, -55.87, -57.98, 0.3, 2, 4, 46.02, -107.74, 0.7, 56, -65.72, -64, 0.3, 2, 4, 27.2, -104.65, 0.7, 56, -84.54, -60.91, 0.3, 2, 4, 14.07, -94.37, 0.7, 56, -97.67, -50.62, 0.3, 2, 4, 7.95, -80.11, 0.7, 56, -103.78, -36.36, 0.3, 2, 4, 7.64, -59.96, 0.7, 56, -104.09, -16.22, 0.3, 2, 4, 11.36, -43.62, 0.7, 56, -100.38, 0.13, 0.3, 2, 4, 22.96, -26.33, 0.7, 56, -88.78, 17.42, 0.3, 2, 4, 25.5, 6.01, 0.7, 56, -86.24, 49.75, 0.3, 2, 4, 36.97, 32.13, 0.7, 56, -74.77, 75.88, 0.3, 2, 4, 53.44, 55.85, 0.7, 56, -58.3, 99.59, 0.3, 2, 4, 68.69, 69.63, 0.7, 56, -43.05, 113.37, 0.3, 2, 4, 97.39, 70.07, 0.7, 56, -14.35, 113.81, 0.3, 2, 4, 124.17, 69.36, 0.7, 56, 12.43, 113.1, 0.3, 2, 4, 150.3, 57.61, 0.7, 56, 38.56, 101.36, 0.3, 2, 4, 174.17, 30.93, 0.7, 56, 62.43, 74.68, 0.3, 2, 4, 155.38, -29.96, 0.7, 55, 115.26, 61.7, 0.3, 2, 4, 127.92, -49.42, 0.7, 55, 87.8, 42.25, 0.3, 2, 4, 101.18, -55.44, 0.7, 55, 61.06, 36.22, 0.3, 2, 4, 75.71, -62.07, 0.7, 55, 35.59, 29.59, 0.3, 2, 4, 58.15, -76.38, 0.7, 55, 18.03, 15.29, 0.3, 2, 4, 43.41, -91.58, 0.7, 55, 3.29, 0.09, 0.3, 2, 4, 29.09, -93.67, 0.7, 55, -11.03, -2, 0.3, 2, 4, 18.68, -86.34, 0.85, 55, -21.43, 5.32, 0.15, 2, 4, 145, 10.78, 0.8, 55, 104.88, 102.45, 0.2, 2, 4, 116.14, -3.95, 0.8, 55, 76.02, 87.71, 0.2, 2, 4, 92.23, -14.35, 0.8, 55, 52.11, 77.32, 0.2, 2, 4, 58.16, -32.17, 0.8, 55, 18.04, 59.49, 0.2, 2, 4, 36.01, -59.34, 0.8, 55, -4.11, 32.33, 0.2, 2, 4, 26.43, -72.02, 0.8, 55, -13.69, 19.64, 0.2, 2, 4, 135.92, -58.93, 0.85, 55, 95.8, 32.73, 0.15, 2, 4, 106.86, -71.18, 0.85, 55, 66.74, 20.48, 0.15, 3, 4, 94.44, -74.26, 0.782, 55, 54.32, 17.4, 0.138, 56, -17.3, -30.52, 0.08, 3, 4, 77.89, -73.07, 0.7548, 55, 37.78, 18.59, 0.1332, 56, -33.84, -29.33, 0.112, 3, 4, 64.41, -83.25, 0.7684, 55, 24.29, 8.41, 0.1356, 56, -47.33, -39.51, 0.096, 3, 4, 48.5, -98.06, 0.7684, 55, 8.38, -6.39, 0.1356, 56, -63.24, -54.31, 0.096, 3, 4, 38.05, -101.24, 0.7684, 55, -2.07, -9.57, 0.1356, 56, -73.69, -57.49, 0.096, 2, 4, 75.15, -23.29, 0.8, 55, 35.03, 68.38, 0.2, 2, 4, 105.91, 40.81, 0.85, 55, 65.79, 132.48, 0.15, 2, 4, 74.19, 24.71, 0.85, 55, 34.07, 116.38, 0.15, 2, 4, 45.25, 4.94, 0.85, 55, 5.13, 96.6, 0.15, 2, 4, 37.33, -27.89, 0.85, 55, -2.79, 63.77, 0.15, 3, 4, 19.13, -52.46, 0.7684, 55, -20.99, 39.21, 0.1356, 56, -92.61, -8.71, 0.096, 3, 4, 14.37, -65.66, 0.7684, 55, -25.75, 26, 0.1356, 56, -97.37, -21.92, 0.096, 3, 4, 82.36, 57.05, 0.714, 55, 42.24, 148.72, 0.126, 56, -29.38, 100.8, 0.16, 3, 4, 59.08, 40.09, 0.714, 55, 18.96, 131.76, 0.126, 56, -52.66, 83.83, 0.16], "hull": 26, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 0, 50, 0, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 50, 68, 68, 70, 70, 72, 74, 76, 76, 78, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 72, 94, 94, 74, 48, 96, 96, 98, 98, 100, 100, 102, 102, 104, 104, 106, 108, 110], "width": 177, "height": 171}}, "qianyang017": {"qianyang017": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-30.57, 32.46, -31.81, 113.45, 52.18, 114.74, 53.42, 33.75], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 81, "height": 84}, "qianyang018": {"type": "mesh", "uvs": [1, 1, 0, 1, 0, 0, 1, 0], "triangles": [1, 2, 3, 1, 3, 0], "vertices": [-31.58, 33.44, -32.81, 113.43, 49.18, 114.69, 50.41, 34.7], "hull": 4, "edges": [0, 2, 2, 4, 4, 6, 0, 6], "width": 80, "height": 82}}, "qianyang019": {"qianyang019": {"type": "mesh", "uvs": [0.7676, 0.90676, 0.82665, 0.97828, 0.89663, 1, 0.94474, 0.96584, 0.99139, 0.83939, 1, 0.6746, 0.98774, 0.47663, 0.91282, 0.29234, 0.79472, 0.14251, 0.65151, 0.03822, 0.50397, 0.00682, 0.34852, 0.01756, 0.19233, 0.06884, 0.07652, 0.18909, 0, 0.34293, 0, 0.47401, 0.04757, 0.55742, 0.06662, 0.48051, 0.09176, 0.38301, 0.171, 0.29959, 0.28224, 0.21943, 0.41024, 0.21401, 0.52384, 0.23945, 0.63526, 0.30329, 0.72012, 0.40548, 0.77193, 0.50447, 0.80269, 0.63914, 0.80217, 0.77147, 0.03301, 0.48391, 0.04177, 0.35942, 0.12056, 0.23866, 0.23351, 0.1428, 0.37622, 0.10421, 0.51631, 0.10794, 0.66428, 0.16646, 0.7851, 0.27974, 0.87003, 0.43411, 0.90943, 0.62023, 0.90374, 0.82377], "triangles": [32, 11, 10, 33, 10, 9, 32, 10, 33, 31, 12, 11, 31, 11, 32, 34, 9, 8, 33, 9, 34, 21, 32, 33, 20, 31, 32, 20, 32, 21, 30, 13, 12, 30, 12, 31, 22, 33, 34, 21, 33, 22, 35, 34, 8, 35, 8, 7, 19, 30, 31, 19, 31, 20, 23, 22, 34, 23, 34, 35, 29, 14, 13, 30, 29, 13, 18, 29, 30, 18, 30, 19, 24, 23, 35, 36, 35, 7, 24, 35, 36, 15, 14, 29, 36, 7, 6, 17, 29, 18, 28, 15, 29, 17, 28, 29, 25, 24, 36, 16, 28, 17, 15, 28, 16, 37, 36, 6, 26, 25, 36, 37, 26, 36, 37, 27, 26, 38, 37, 5, 38, 27, 37, 3, 38, 4, 38, 0, 27, 2, 1, 38, 1, 0, 38, 3, 2, 38, 37, 6, 5, 4, 38, 5], "vertices": [2, 4, 171.85, -13.35, 0.868, 56, 60.11, 30.39, 0.132, 2, 4, 158.37, -29.68, 0.868, 56, 46.63, 14.07, 0.132, 2, 4, 154.49, -48.84, 0.868, 56, 42.75, -5.1, 0.132, 2, 4, 161.25, -61.88, 0.868, 56, 49.51, -18.13, 0.132, 2, 4, 185.72, -74.24, 0.7, 56, 73.98, -30.49, 0.3, 2, 4, 217.39, -76.1, 0.7, 56, 105.66, -32.36, 0.3, 2, 4, 255.35, -72.17, 0.7, 56, 143.61, -28.43, 0.3, 2, 4, 290.41, -51.17, 0.7, 56, 178.67, -7.43, 0.3, 2, 4, 318.68, -18.5, 0.7, 56, 206.94, 25.25, 0.3, 2, 4, 338.1, 20.91, 0.7, 56, 226.36, 64.65, 0.3, 2, 4, 343.51, 61.27, 0.7, 56, 231.77, 105.02, 0.3, 2, 4, 340.79, 103.67, 0.7, 56, 229.05, 147.42, 0.3, 2, 4, 330.29, 146.16, 0.7, 56, 218.55, 189.9, 0.3, 2, 4, 306.72, 177.41, 0.7, 56, 194.98, 221.16, 0.3, 2, 4, 276.87, 197.85, 0.7, 56, 165.13, 241.59, 0.3, 2, 4, 251.7, 197.46, 0.7, 56, 139.96, 241.2, 0.3, 2, 4, 235.89, 184.23, 0.7, 56, 124.15, 227.97, 0.3, 2, 4, 250.74, 179.26, 0.7, 56, 139, 223, 0.3, 2, 4, 269.56, 172.68, 0.7, 56, 157.82, 216.42, 0.3, 2, 4, 285.91, 151.3, 0.7, 56, 174.17, 195.04, 0.3, 2, 4, 301.76, 121.17, 0.7, 56, 190.02, 164.91, 0.3, 2, 4, 303.34, 86.25, 0.7, 56, 191.6, 129.99, 0.3, 2, 4, 298.93, 55.16, 0.7, 56, 187.19, 98.91, 0.3, 2, 4, 287.14, 24.56, 0.7, 56, 175.41, 68.3, 0.3, 2, 4, 267.88, 1.09, 0.7, 56, 156.14, 44.84, 0.3, 2, 4, 249.1, -13.34, 0.7, 56, 137.36, 30.4, 0.3, 2, 4, 223.37, -22.14, 0.7, 56, 111.63, 21.61, 0.3, 2, 4, 197.97, -22.39, 0.7, 56, 86.23, 21.36, 0.3, 2, 4, 249.94, 188.42, 0.7, 55, 209.82, 280.09, 0.3, 2, 4, 273.88, 186.4, 0.7, 55, 233.76, 278.06, 0.3, 2, 4, 297.39, 165.24, 0.7, 55, 257.27, 256.91, 0.3, 2, 4, 316.27, 134.7, 0.7, 55, 276.15, 226.36, 0.3, 2, 4, 324.28, 95.85, 0.7, 55, 284.16, 187.52, 0.3, 2, 4, 324.15, 57.6, 0.7, 55, 284.03, 149.27, 0.3, 2, 4, 313.54, 17.04, 0.7, 55, 273.42, 108.71, 0.3, 2, 4, 292.29, -16.27, 0.7, 55, 252.18, 75.39, 0.3, 2, 4, 263.02, -39.91, 0.7, 55, 222.9, 51.75, 0.3, 2, 4, 227.45, -51.22, 0.7, 55, 187.33, 40.45, 0.3, 2, 4, 188.35, -50.27, 0.7, 55, 148.23, 41.4, 0.3], "hull": 28, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 0, 54, 32, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76], "width": 273, "height": 192}}, "shouji/tx_baodian2": {"shouji/tx_baodian02": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian03": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian04": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian05": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian06": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian07": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian08": {"y": 33.11, "width": 50, "height": 100}}, "qianyang11": {"qianyang011": {"type": "mesh", "uvs": [0.60743, 0.01306, 0.79405, 0.08342, 0.89575, 0.20385, 0.91468, 0.32688, 0.88936, 0.4461, 0.81992, 0.57536, 0.84596, 0.68515, 0.79931, 0.78168, 0.81739, 0.84743, 0.90236, 0.87814, 0.98416, 0.92103, 1, 0.99273, 0.82777, 0.99782, 0.58365, 0.99457, 0.52335, 0.8874, 0.46828, 0.81209, 0.45316, 0.72039, 0.47893, 0.66471, 0.40255, 0.58409, 0.21604, 0.47025, 0.06358, 0.33951, 0.02733, 0.17892, 0.16116, 0.06725, 0.3486, 0.02376, 0.40164, 0.11815, 0.52424, 0.29658, 0.56347, 0.45251, 0.63213, 0.58296, 0.69097, 0.68042, 0.64684, 0.78238, 0.67136, 0.87385, 0.75963, 0.93382, 0.87487, 0.95182], "triangles": [13, 31, 12, 12, 32, 11, 12, 31, 32, 13, 30, 31, 13, 14, 30, 32, 10, 11, 32, 9, 10, 32, 31, 9, 31, 8, 9, 31, 30, 8, 30, 14, 29, 14, 15, 29, 30, 7, 8, 30, 29, 7, 15, 16, 29, 29, 16, 28, 29, 28, 7, 28, 16, 17, 7, 28, 6, 28, 5, 6, 17, 27, 28, 28, 27, 5, 17, 18, 27, 18, 26, 27, 18, 19, 26, 27, 26, 5, 5, 26, 4, 19, 25, 26, 19, 20, 25, 4, 26, 3, 3, 26, 25, 20, 24, 25, 24, 21, 22, 24, 20, 21, 25, 2, 3, 25, 1, 2, 1, 24, 0, 1, 25, 24, 22, 23, 24, 24, 23, 0], "vertices": [1, 21, -54.25, 46.56, 1, 1, 21, -23.54, 67.83, 1, 1, 21, 14.19, 70.31, 1, 1, 21, 46.95, 59.61, 1, 1, 21, 75.71, 42.42, 1, 1, 21, 104.05, 17.25, 1, 2, 21, 133.9, 9.12, 0.50743, 22, -4.2, 9.64, 0.49257, 2, 22, 22.59, 16.45, 0.92549, 23, 19.74, 43.76, 0.07451, 2, 22, 36.62, 28.41, 0.44779, 23, 30.63, 28.89, 0.55221, 2, 22, 36.69, 45.23, 0.09811, 23, 47.4, 27.57, 0.90189, 2, 22, 39.91, 63.31, 0.00997, 23, 65.19, 23.02, 0.99003, 1, 23, 76.47, 6.51, 1, 1, 23, 50.81, -7.42, 1, 1, 23, 13.15, -24.59, 1, 3, 21, 163.65, -63.62, 0.00089, 22, 70.99, -9.09, 0.81639, 23, -9.3, -2.61, 0.18271, 2, 21, 140.63, -63.84, 0.04677, 22, 57.83, -27.83, 0.95323, 2, 21, 116.15, -56.01, 0.26149, 22, 37.41, -43.03, 0.73851, 2, 21, 103.66, -45.81, 0.55563, 22, 22.04, -47.13, 0.44437, 2, 21, 77.84, -48.77, 0.91319, 22, 9.44, -69.75, 0.08681, 2, 21, 36.02, -65.2, 0.99972, 22, -1.71, -113.23, 0.00028, 1, 21, -7.81, -74.44, 1, 1, 21, -51.35, -62.26, 1, 1, 21, -70.79, -29.01, 1, 1, 21, -69.15, 5.03, 1, 1, 21, -41.39, 2.82, 1, 1, 21, 12.6, 2.12, 1, 2, 21, 55.15, -9.08, 0.99914, 22, -35.33, -64.75, 0.00086, 2, 21, 93.19, -12.86, 0.93728, 22, -10.28, -36.21, 0.06272, 2, 21, 122.13, -14.51, 0.46736, 22, 7.8, -13.78, 0.53264, 2, 21, 145.2, -32.71, 0.01655, 22, 35.67, -5.83, 0.98345, 2, 22, 55.24, 10.72, 0.54121, 23, 11.61, 11.63, 0.45879, 2, 22, 61.96, 32.16, 0.02078, 23, 32.5, 3.34, 0.97922, 2, 22, 56.46, 51.62, 0.00942, 23, 52.31, 7.39, 0.99058], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 16, 18, 18, 20, 20, 22, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 26, 28, 40, 42, 42, 44, 4, 6, 10, 12, 6, 8, 12, 14, 14, 16, 28, 30, 30, 32, 22, 24, 24, 26, 8, 10, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 170, "height": 278}}, "qianyang12": {"qianyang012": {"type": "mesh", "uvs": [0.87624, 0.05794, 0.97359, 0.19563, 0.94537, 0.33426, 0.81055, 0.49827, 0.65596, 0.58691, 0.47225, 0.65543, 0.42802, 0.67193, 0.41326, 0.71554, 0.41346, 0.77728, 0.47088, 0.8305, 0.50684, 0.83407, 0.62546, 0.89047, 0.67669, 0.97571, 0.64798, 0.99561, 0.39426, 0.99816, 0.15924, 0.99571, 0.13286, 0.96802, 0.15828, 0.86585, 0.10049, 0.80361, 0, 0.69538, 0, 0.64907, 0.10649, 0.55988, 0.17325, 0.47504, 0.1597, 0.4077, 0.09593, 0.26924, 0.13519, 0.09722, 0.42165, 0.00369, 0.69628, 0.00431, 0.51503, 0.1531, 0.51834, 0.35005, 0.47534, 0.48232, 0.29838, 0.56952, 0.18592, 0.66163, 0.24876, 0.78802, 0.30665, 0.86739, 0.36123, 0.93401, 0.52496, 0.94577, 0.23719, 0.94773], "triangles": [15, 37, 14, 37, 35, 14, 14, 36, 13, 14, 35, 36, 15, 16, 37, 13, 36, 12, 36, 11, 12, 16, 17, 37, 37, 34, 35, 37, 17, 34, 35, 9, 36, 9, 10, 36, 36, 10, 11, 35, 34, 9, 34, 8, 9, 17, 33, 34, 34, 33, 8, 17, 18, 33, 33, 18, 32, 18, 19, 32, 33, 7, 8, 33, 32, 7, 19, 20, 32, 7, 32, 6, 5, 6, 31, 20, 21, 32, 6, 32, 31, 32, 21, 31, 5, 30, 4, 5, 31, 30, 4, 30, 3, 21, 22, 31, 31, 22, 30, 30, 29, 3, 3, 29, 2, 29, 30, 23, 30, 22, 23, 23, 24, 29, 24, 28, 29, 2, 29, 1, 0, 28, 27, 28, 1, 29, 0, 1, 28, 24, 25, 28, 25, 26, 28, 28, 26, 27], "vertices": [1, 12, -46.56, 22.47, 1, 1, 12, -25.83, 51.33, 1, 1, 12, 4.37, 65.21, 1, 2, 12, 47.58, 69.45, 0.99583, 13, 11.65, 112.83, 0.00417, 2, 12, 76.97, 61.93, 0.95984, 13, 20.78, 83.9, 0.04016, 3, 12, 104.43, 48.39, 0.77286, 13, 23.79, 53.43, 0.21467, 14, 6.47, 61.57, 0.01247, 3, 12, 111.04, 45.14, 0.63233, 13, 24.52, 46.1, 0.33264, 14, 2.71, 55.24, 0.03503, 3, 12, 120.98, 48.8, 0.36591, 13, 32.88, 39.59, 0.51411, 14, 5.59, 45.04, 0.11998, 3, 12, 133.48, 56.51, 0.10975, 13, 46.05, 33.05, 0.47955, 14, 12.32, 31.96, 0.41069, 3, 12, 140.03, 70.04, 0.0144, 13, 61.01, 34.62, 0.14133, 14, 25.31, 24.37, 0.84427, 3, 12, 138.1, 74.81, 0.00577, 13, 64.04, 38.78, 0.07476, 14, 30.21, 25.92, 0.91948, 2, 13, 83.55, 47.74, 0.0012, 14, 51.23, 21.58, 0.9988, 1, 14, 66.92, 6.79, 1, 1, 14, 65.48, 0.73, 1, 1, 14, 33.9, -16.1, 1, 2, 13, 76.55, -22.27, 0.01134, 14, 4.12, -30.67, 0.98866, 2, 13, 68.99, -22.65, 0.0355, 14, -2.2, -26.49, 0.9645, 2, 13, 48.82, -8.56, 0.92776, 14, -10.11, -3.2, 0.07224, 1, 13, 31.91, -9.23, 1, 2, 12, 147.4, -3.36, 0.04847, 13, 2.5, -10.38, 0.95153, 2, 12, 138.01, -9.13, 0.68641, 13, -7.37, -5.45, 0.31359, 1, 12, 112.06, -7.45, 1, 1, 12, 89.92, -10, 1, 1, 12, 77.27, -20.02, 1, 1, 12, 53.9, -44.94, 1, 1, 12, 16.11, -61.65, 1, 1, 12, -24, -38.9, 1, 1, 12, -44.15, -5.83, 1, 1, 12, -0.6, -9.06, 1, 2, 12, 39.1, 15.88, 1, 13, -38.38, 91.75, 0, 2, 12, 69.1, 27.19, 0.98319, 13, -12.9, 72.25, 0.01681, 3, 12, 99.85, 16.8, 0.92116, 13, -5.49, 40.65, 0.07844, 14, -24.69, 68.62, 0.0004, 3, 12, 126.83, 14.77, 0.56549, 13, 7.04, 16.66, 0.43186, 14, -28.81, 41.88, 0.00266, 3, 12, 147.82, 38.07, 0.04299, 13, 37.94, 11.13, 0.85044, 14, -7.2, 19.11, 0.10657, 3, 12, 159.64, 54.91, 0.00381, 13, 58.51, 9.99, 0.15836, 14, 8.69, 6, 0.83783, 1, 14, 22.78, -4.62, 1, 2, 13, 88.99, 29.18, 4e-05, 14, 44.62, 3.4, 0.99996, 2, 13, 71.25, -7.33, 0.01411, 14, 8.69, -15.49, 0.98589], "hull": 28, "edges": [0, 54, 0, 2, 14, 16, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 38, 40, 44, 46, 52, 54, 6, 8, 40, 42, 42, 44, 46, 48, 48, 50, 20, 22, 50, 52, 16, 18, 18, 20, 12, 14, 4, 6, 2, 4, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 34, 36, 36, 38, 66, 68, 68, 70, 70, 72, 8, 10, 10, 12], "width": 141, "height": 238}}, "qianyang13": {"qianyang011": {"type": "mesh", "uvs": [0.60743, 0.01306, 0.79405, 0.08342, 0.89575, 0.20385, 0.91468, 0.32688, 0.88936, 0.4461, 0.81992, 0.57536, 0.84596, 0.68515, 0.79931, 0.78168, 0.81739, 0.84743, 0.90236, 0.87814, 0.98416, 0.92103, 1, 0.99273, 0.82777, 0.99782, 0.58365, 0.99457, 0.52335, 0.8874, 0.46828, 0.81209, 0.45316, 0.72039, 0.47893, 0.66471, 0.40255, 0.58409, 0.21604, 0.47025, 0.06358, 0.33951, 0.02733, 0.17892, 0.16116, 0.06725, 0.3486, 0.02376, 0.40164, 0.11815, 0.52424, 0.29658, 0.56347, 0.45251, 0.63213, 0.58296, 0.69097, 0.68042, 0.64684, 0.78238, 0.67136, 0.87385, 0.75963, 0.93382, 0.87487, 0.95182], "triangles": [31, 30, 8, 31, 8, 9, 32, 31, 9, 32, 9, 10, 32, 10, 11, 13, 30, 31, 12, 31, 32, 12, 32, 11, 13, 31, 12, 13, 14, 30, 7, 28, 6, 28, 16, 17, 29, 28, 7, 29, 16, 28, 15, 16, 29, 30, 29, 7, 30, 7, 8, 14, 15, 29, 30, 14, 29, 24, 23, 0, 22, 23, 24, 1, 25, 24, 1, 24, 0, 25, 1, 2, 25, 2, 3, 24, 20, 21, 24, 21, 22, 20, 24, 25, 3, 26, 25, 4, 26, 3, 19, 20, 25, 19, 25, 26, 5, 26, 4, 27, 26, 5, 18, 19, 26, 18, 26, 27, 17, 18, 27, 28, 27, 5, 17, 27, 28, 28, 5, 6], "vertices": [1, 68, -53.6, 27.52, 1, 1, 68, -26.24, 51.91, 1, 1, 68, 9.95, 58.35, 1, 1, 68, 42.77, 51.16, 1, 1, 68, 72.44, 37.11, 1, 1, 68, 102.55, 15.07, 1, 2, 68, 132.08, 10.28, 0.48733, 69, -3.8, 10.64, 0.51267, 2, 69, 24.4, 16.14, 0.95251, 70, 14.18, 47.89, 0.04749, 2, 69, 40.23, 27.84, 0.52408, 70, 26.38, 32.45, 0.47592, 2, 69, 42.01, 44.8, 0.14038, 70, 43.4, 31.22, 0.85962, 2, 69, 46.93, 62.29, 0.02402, 70, 61.03, 26.86, 0.97598, 1, 70, 72.24, 9.83, 1, 1, 70, 47.13, -5.8, 1, 1, 70, 10.25, -25.28, 1, 3, 68, 173.11, -60.78, 0.00044, 69, 74.76, -11.52, 0.9013, 70, -11.84, -3.33, 0.09826, 2, 68, 150.38, -63.45, 0.03453, 69, 60.45, -29.32, 0.96547, 2, 68, 124.38, -57.73, 0.22683, 69, 37.98, -43.4, 0.77317, 2, 68, 109.98, -48.29, 0.52665, 69, 21.18, -46.7, 0.47335, 2, 68, 84.56, -53.46, 0.89928, 69, 6.69, -68.16, 0.10072, 2, 68, 45.78, -74.06, 0.99908, 69, -6.58, -109.99, 0.00092, 1, 68, 4.44, -87.87, 1, 1, 68, -38.96, -80.35, 1, 1, 68, -61.37, -49.34, 1, 1, 68, -63.48, -15.33, 1, 1, 68, -36.42, -14.6, 1, 1, 68, 15.83, -9.61, 1, 2, 68, 58.17, -16.26, 0.99806, 69, -37.93, -59.99, 0.00194, 2, 68, 95.34, -16.04, 0.92552, 69, -11.64, -33.85, 0.07448, 2, 68, 123.85, -14.83, 0.40828, 69, 7.8, -13.07, 0.59172, 2, 68, 149.88, -31.17, 0.01645, 69, 37.66, -6.57, 0.98355, 2, 69, 58.91, 9, 0.67829, 70, 8.16, 13.17, 0.32171, 2, 69, 67.83, 29.63, 0.03673, 70, 29.06, 4.91, 0.96327, 2, 69, 63.48, 49.22, 0.01881, 70, 48.5, 9.9, 0.98119], "hull": 24, "edges": [0, 46, 0, 2, 2, 4, 16, 18, 18, 20, 20, 22, 32, 34, 34, 36, 36, 38, 38, 40, 44, 46, 26, 28, 40, 42, 42, 44, 4, 6, 10, 12, 6, 8, 12, 14, 14, 16, 28, 30, 30, 32, 22, 24, 24, 26, 8, 10, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64], "width": 170, "height": 278}}, "qianyang021": {"qianyang021": {"type": "mesh", "uvs": [0.98454, 0.39976, 0.9947, 0.49613, 0.96759, 0.58318, 0.90321, 0.64743, 0.81398, 0.70028, 0.72814, 0.75417, 0.66941, 0.83086, 0.63213, 0.93138, 0.5542, 0.99459, 0.4661, 1, 0.37009, 0.94692, 0.30458, 0.85158, 0.24472, 0.74277, 0.21648, 0.62567, 0.24923, 0.48888, 0.34411, 0.39457, 0.46836, 0.32411, 0.48982, 0.30027, 0.44238, 0.22877, 0.3283, 0.13964, 0.22552, 0.08368, 0.15775, 0.0899, 0.11708, 0.13964, 0.17356, 0.23913, 0.13177, 0.23187, 0.03341, 0.14301, 0, 0.06871, 0, 0.00994, 0.05758, 0, 0.1857, 0.01105, 0.26184, 0.0299, 0.37304, 0.06871, 0.47698, 0.13081, 0.56279, 0.22729, 0.59301, 0.33042, 0.564, 0.43798, 0.49269, 0.51782, 0.38271, 0.55774, 0.28964, 0.61208, 0.30414, 0.69525, 0.35732, 0.77287, 0.42501, 0.82721, 0.47577, 0.84163, 0.53862, 0.82166, 0.58092, 0.77287, 0.58576, 0.67972, 0.60389, 0.56662, 0.64619, 0.45683, 0.73442, 0.37366, 0.83222, 0.35301, 0.9169, 0.36481, 0.88818, 0.44138, 0.76707, 0.51916, 0.68922, 0.62551, 0.64078, 0.71757, 0.62348, 0.80329, 0.56638, 0.89536, 0.45912, 0.90964, 0.39511, 0.87631, 0.33109, 0.80646, 0.27919, 0.72234, 0.26016, 0.61122, 0.30514, 0.51916, 0.40549, 0.44931, 0.51102, 0.37471, 0.53697, 0.31598, 0.5041, 0.2255, 0.40549, 0.13661, 0.28438, 0.07788, 0.19096, 0.04931, 0.07504, 0.05089, 0.08542, 0.14137], "triangles": [25, 71, 24, 71, 22, 24, 23, 24, 22, 71, 25, 70, 25, 26, 70, 71, 70, 22, 22, 70, 21, 21, 69, 20, 21, 70, 69, 20, 69, 68, 69, 30, 68, 70, 27, 28, 70, 26, 27, 70, 29, 69, 70, 28, 29, 69, 29, 30, 34, 64, 65, 65, 33, 34, 33, 65, 66, 65, 17, 66, 17, 18, 66, 19, 67, 18, 18, 67, 66, 67, 32, 66, 33, 66, 32, 20, 68, 19, 19, 68, 67, 68, 31, 67, 67, 31, 32, 68, 30, 31, 39, 61, 38, 13, 14, 61, 37, 38, 62, 38, 61, 62, 61, 14, 62, 37, 63, 36, 37, 62, 63, 63, 62, 15, 36, 63, 35, 62, 14, 15, 63, 64, 35, 63, 16, 64, 63, 15, 16, 35, 64, 34, 64, 16, 65, 65, 16, 17, 10, 57, 9, 9, 57, 8, 8, 57, 56, 10, 58, 57, 10, 11, 58, 57, 58, 42, 57, 42, 56, 42, 58, 41, 42, 43, 56, 11, 59, 58, 58, 59, 41, 11, 12, 59, 59, 40, 41, 12, 60, 59, 59, 60, 40, 60, 39, 40, 12, 13, 60, 39, 60, 61, 13, 61, 60, 8, 56, 7, 7, 56, 6, 56, 55, 6, 55, 43, 44, 55, 56, 43, 6, 55, 5, 55, 54, 5, 55, 44, 54, 44, 45, 54, 4, 5, 53, 5, 54, 53, 54, 45, 53, 3, 4, 52, 45, 46, 53, 4, 53, 52, 3, 52, 2, 53, 46, 52, 52, 51, 2, 2, 51, 1, 46, 47, 52, 47, 48, 52, 51, 48, 49, 51, 52, 48, 51, 0, 1, 51, 50, 0, 51, 49, 50], "vertices": [2, 7, -59.13, 13.74, 0.384, 6, 69.12, -57.98, 0.616, 2, 7, -37.31, 33.34, 0.384, 6, 51.78, -34.32, 0.616, 2, 7, -11.61, 42.94, 0.384, 6, 44.78, -7.8, 0.616, 2, 7, 14.68, 40.09, 0.384, 6, 50.22, 18.08, 0.616, 1, 7, 42.29, 29.63, 1, 1, 7, 69.6, 20.12, 1, 1, 7, 97.99, 20.77, 1, 2, 7, 128.65, 30.51, 0.91511, 8, -30.26, -3.96, 0.08489, 3, 7, 156.93, 24.44, 0.467, 8, -27.38, 24.82, 0.53298, 9, -43.82, -132.13, 2e-05, 3, 7, 172.79, 5.71, 0.16587, 8, -10.53, 42.67, 0.83401, 9, -59.13, -112.93, 0.00012, 3, 7, 175.69, -25.32, 0.00451, 8, 19.97, 49.01, 0.99504, 9, -61.12, -81.84, 0.00045, 2, 8, 52.97, 40.2, 0.99897, 9, -47.76, -50.41, 0.00103, 2, 8, 87.58, 27.33, 0.99985, 9, -30.15, -17.95, 0.00015, 2, 8, 117.46, 6.64, 0.43111, 9, -5.46, 8.72, 0.56889, 1, 9, 33.79, 24.86, 1, 1, 9, 72.3, 19.47, 1, 1, 9, 109.52, 3.25, 1, 1, 9, 118.86, 2.46, 1, 2, 9, 129.15, 25.65, 1e-05, 10, 27.95, 2.01, 0.99999, 1, 10, 69.49, 5.13, 1, 2, 10, 101.73, 13.18, 0.16523, 11, 10.39, 8.11, 0.83477, 1, 11, 29.32, 7.62, 1, 2, 10, 111.22, 46.44, 0.00011, 11, 42.42, 21.17, 0.99989, 2, 10, 78.86, 56.82, 0.00019, 11, 30.61, 53.04, 0.99981, 2, 10, 88.66, 63.43, 0.00019, 11, 41.86, 49.4, 0.99981, 2, 10, 127.04, 63.53, 6e-05, 11, 65.62, 19.27, 0.99994, 1, 11, 72.03, -4.22, 1, 1, 11, 69.8, -21.89, 1, 1, 11, 53.55, -22.88, 1, 1, 11, 18.63, -15.11, 1, 2, 10, 106.01, -5.51, 0.4259, 11, -1.66, -6.8, 0.5741, 1, 10, 75.77, -18.89, 1, 1, 10, 42, -25.85, 1, 1, 10, 4.48, -21.85, 1, 2, 9, 127.7, -26.32, 0.2173, 10, -23.48, -5.55, 0.7827, 2, 9, 96.32, -38.26, 0.80269, 10, -40.67, 23.28, 0.19731, 2, 9, 65.14, -35.76, 0.9747, 10, -43.6, 54.42, 0.0253, 1, 9, 37.78, -17.52, 1, 2, 8, 105.34, -10.21, 0.11544, 9, 9.51, -5.64, 0.88456, 2, 8, 85.25, 5.54, 0.99823, 9, -8.9, -23.32, 0.00177, 2, 8, 58.42, 12.74, 0.99872, 9, -19.81, -48.87, 0.00128, 2, 8, 33.43, 12.03, 0.99937, 9, -22.61, -73.71, 0.00063, 2, 8, 20.11, 5.64, 0.99984, 9, -18.16, -87.8, 0.00016, 2, 7, 117.33, -10.14, 0.60149, 8, 11.41, -10.67, 0.39851, 2, 7, 98.45, -9.45, 0.99501, 8, 12.83, -29.51, 0.00499, 1, 7, 74.94, -25.13, 1, 1, 7, 44.37, -41.41, 1, 1, 7, 10.62, -51.69, 1, 1, 7, -24.22, -46.91, 1, 1, 7, -45.39, -28.75, 1, 1, 7, -56.49, -7.68, 1, 1, 7, -33.08, -0.34, 1, 1, 7, 5.87, -13.44, 1, 1, 7, 44.65, -11.73, 1, 1, 7, 75.09, -6.01, 1, 1, 7, 98.85, 5.54, 1, 3, 7, 130.72, 9.32, 0.78501, 8, -9.43, 0.46, 0.21499, 9, -17.18, -117.78, 0, 3, 7, 151.9, -12.11, 0.03828, 8, 9.5, 23.9, 0.96151, 9, -37.73, -95.74, 0.00021, 2, 8, 29.42, 28.59, 0.99939, 9, -39.57, -75.36, 0.00061, 2, 8, 56.85, 25.16, 0.99886, 9, -32.32, -48.68, 0.00114, 2, 8, 84.76, 16.27, 0.99934, 9, -19.6, -22.29, 0.00066, 2, 8, 111.53, -4.83, 0.08869, 9, 5.05, 1.24, 0.91131, 1, 9, 35.1, 6.86, 1, 2, 9, 68.39, -4.01, 0.99862, 10, -11.77, 56.72, 0.00138, 2, 9, 103.68, -15.23, 0.79566, 10, -16.72, 20.02, 0.20434, 2, 9, 122.42, -11.02, 0.26504, 10, -9.33, 2.3, 0.73496, 1, 10, 16.45, -10.76, 1, 1, 10, 54.89, -10.62, 1, 1, 10, 91.33, 0.42, 1, 1, 11, 18.62, -3.43, 1, 1, 11, 50.66, -6.97, 1, 2, 10, 117.11, 53, 9e-05, 11, 51.22, 20.59, 0.99991], "hull": 51, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92, 92, 94, 94, 96, 96, 98, 98, 100, 0, 100, 0, 102, 102, 104, 104, 106, 106, 108, 108, 110, 110, 112, 112, 114, 114, 116, 116, 118, 118, 120, 120, 122, 122, 124, 124, 126, 126, 128, 128, 130, 130, 132, 132, 134, 134, 136, 136, 138, 138, 140, 140, 142, 142, 48], "width": 278, "height": 303}}, "qianyang014": {"qianyang014": {"type": "mesh", "uvs": [0, 0.03038, 0.05899, 0, 0.17203, 0.04882, 0.30442, 0.12629, 0.44191, 0.25909, 0.57328, 0.37529, 0.72197, 0.44538, 0.87645, 0.52118, 1, 0.5939, 0.97855, 0.75804, 0.9166, 0.91803, 0.7732, 0.98452, 0.64012, 1, 0.51507, 0.96998, 0.38428, 0.84116, 0.26038, 0.69987, 0.13878, 0.43807, 0.03623, 0.22777, 0, 0.13168, 0.03807, 0.08185, 0.10245, 0.1693, 0.21377, 0.31748, 0.34521, 0.50938, 0.46994, 0.66484, 0.68052, 0.79601, 0.83699, 0.78513], "triangles": [12, 24, 11, 12, 13, 24, 11, 25, 10, 11, 24, 25, 10, 25, 9, 24, 6, 25, 24, 5, 6, 25, 7, 9, 25, 6, 7, 9, 7, 8, 14, 23, 13, 13, 23, 24, 14, 22, 23, 23, 5, 24, 22, 4, 23, 23, 4, 5, 15, 22, 14, 15, 16, 22, 22, 16, 21, 21, 3, 22, 22, 3, 4, 17, 20, 16, 16, 20, 21, 20, 2, 21, 21, 2, 3, 17, 19, 20, 17, 18, 19, 19, 1, 20, 20, 1, 2, 18, 0, 19, 19, 0, 1], "vertices": [1, 58, -20.98, 2.19, 1, 1, 58, -14.2, 9.54, 1, 1, 58, 3.81, 15.4, 1, 2, 58, 25.86, 20.7, 0.99753, 59, -25.46, 24.77, 0.00247, 2, 58, 51.22, 22.19, 0.55326, 59, -0.17, 22.46, 0.44674, 3, 58, 74.94, 24.43, 0.01206, 59, 23.63, 21.15, 0.953, 60, -13.97, 28.04, 0.03495, 2, 59, 48.41, 24.75, 0.3314, 60, 10.45, 22.5, 0.6686, 2, 59, 74.26, 28.25, 0.00081, 60, 35.84, 16.47, 0.99919, 1, 60, 56.17, 10.57, 1, 1, 60, 53.14, -4.31, 1, 1, 60, 43.51, -19.02, 1, 2, 59, 74.96, -16.71, 0.02324, 60, 20.33, -25.74, 0.97676, 2, 59, 55.51, -26.43, 0.43607, 60, -1.31, -27.81, 0.56393, 3, 58, 94.75, -26.19, 0, 59, 35.68, -31.86, 0.90209, 60, -21.76, -25.76, 0.09791, 2, 58, 70.52, -27.41, 0.11865, 59, 11.53, -29.46, 0.88135, 2, 58, 46.65, -27.09, 0.74525, 59, -12.02, -25.59, 0.25475, 1, 58, 17.45, -17.32, 1, 1, 58, -6.68, -9.89, 1, 1, 58, -16.23, -5.59, 1, 1, 58, -13.27, 1.47, 1, 1, 58, -0.21, 0.23, 1, 1, 58, 22.23, -1.69, 1, 2, 58, 49.51, -5.25, 0.92106, 59, -5.94, -4.42, 0.07894, 2, 58, 74.16, -6.59, 0.00453, 59, 18.23, -9.41, 0.99547, 2, 59, 54.45, -6.95, 0.1504, 60, 4.7, -9.26, 0.8496, 1, 60, 30.16, -7.47, 1], "hull": 19, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 0, 36, 0, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 18], "width": 163, "height": 90}}, "qianyang015": {"qianyang015": {"type": "mesh", "uvs": [0.03792, 0.58084, 0.08526, 0.52349, 0.23214, 0.46375, 0.4118, 0.33711, 0.57932, 0.2033, 0.73955, 0.08622, 0.8743, 0.01214, 0.95684, 0, 1, 0.10533, 0.9884, 0.27976, 0.92528, 0.41118, 0.80025, 0.54977, 0.66186, 0.65491, 0.54776, 0.81739, 0.40816, 0.97749, 0.2722, 0.99421, 0.15324, 0.86518, 0.08162, 0.70987, 0.09448, 0.62071, 0.24155, 0.68543, 0.41803, 0.68543, 0.60144, 0.51514, 0.77446, 0.38912, 0.90596, 0.25288], "triangles": [16, 19, 15, 20, 15, 19, 16, 17, 19, 19, 17, 18, 17, 0, 18, 18, 2, 19, 20, 19, 2, 0, 1, 18, 18, 1, 2, 14, 15, 20, 14, 20, 13, 13, 21, 12, 13, 20, 21, 20, 2, 3, 20, 3, 21, 12, 21, 22, 3, 4, 21, 11, 12, 22, 11, 22, 10, 21, 4, 22, 22, 23, 10, 10, 23, 9, 4, 5, 22, 22, 5, 23, 9, 23, 8, 5, 6, 23, 23, 7, 8, 23, 6, 7], "vertices": [1, 63, 59.61, -5.54, 1, 1, 63, 50.75, -10.99, 1, 2, 62, 69.79, -23.43, 0.05384, 63, 23.29, -16.67, 0.94616, 3, 61, 91.57, -26.16, 0.10503, 62, 34.11, -23.67, 0.83709, 63, -10.31, -28.7, 0.05788, 2, 61, 57.82, -24.29, 0.96374, 62, 0.34, -25.3, 0.03626, 1, 61, 25.97, -21.57, 1, 1, 61, 0.18, -17.18, 1, 1, 61, -14.27, -11.64, 1, 1, 61, -17.3, 0.85, 1, 1, 61, -8.27, 14.91, 1, 2, 61, 7.73, 21.17, 0.99959, 62, -54.18, 14.73, 0.00041, 2, 61, 34.49, 23.1, 0.8715, 62, -27.76, 19.42, 0.1285, 2, 61, 62.16, 21.1, 0.07376, 62, -0.03, 20.29, 0.92624, 2, 62, 25.21, 27.8, 0.99144, 63, -35.73, 16.93, 0.00856, 2, 62, 54.88, 33.52, 0.57265, 63, -9.63, 32.14, 0.42735, 2, 62, 79.39, 26.61, 0.09252, 63, 15.8, 33.73, 0.90748, 1, 63, 38.04, 21.47, 1, 1, 63, 51.43, 6.72, 1, 1, 63, 49.03, -1.75, 1, 1, 63, 21.53, 4.39, 1, 2, 62, 43.96, 7.94, 0.9622, 63, -11.47, 4.39, 0.0378, 1, 62, 6.24, 4.02, 1, 2, 61, 32.34, 7.24, 0.97075, 62, -28.26, 3.43, 0.02925, 1, 61, 4.58, 6.03, 1], "hull": 18, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 0, 34, 0, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46], "width": 187, "height": 95}}, "shouji/tx_baodian3": {"shouji/tx_baodian02": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian03": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian04": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian05": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian06": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian07": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian08": {"y": 33.11, "width": 50, "height": 100}}, "shouji/tx_baodian4": {"shouji/tx_baodian02": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian03": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian04": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian05": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian06": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian07": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian08": {"y": 33.11, "width": 50, "height": 100}}, "qianyang020": {"qianyang020": {"type": "mesh", "uvs": [0.74422, 0.86908, 0.79119, 0.93986, 0.90894, 1, 0.97693, 0.93481, 1, 0.8716, 0.9538, 0.73129, 0.91735, 0.5834, 0.86198, 0.39127, 0.77506, 0.24211, 0.65661, 0.1054, 0.55214, 0.03452, 0.45285, 0, 0.33185, 0, 0.22171, 0.02663, 0.11006, 0.12371, 0.02931, 0.28911, 0, 0.47843, 0, 0.66256, 0.03299, 0.78854, 0.08028, 0.82731, 0.08888, 0.78079, 0.06738, 0.66062, 0.07598, 0.52495, 0.12649, 0.39315, 0.18668, 0.31368, 0.27374, 0.25554, 0.36402, 0.23034, 0.4586, 0.27298, 0.55318, 0.33113, 0.62089, 0.42804, 0.68322, 0.54433, 0.71831, 0.63994, 0.74186, 0.7446, 0.0631, 0.78301, 0.03583, 0.66196, 0.03723, 0.4993, 0.08827, 0.31394, 0.16658, 0.21054, 0.26867, 0.14119, 0.3771, 0.12174, 0.47664, 0.14557, 0.57441, 0.20752, 0.66954, 0.29488, 0.75058, 0.40925, 0.81312, 0.5538, 0.85716, 0.71265, 0.89063, 0.84449], "triangles": [1, 46, 2, 2, 46, 3, 46, 1, 45, 3, 46, 4, 46, 5, 4, 1, 0, 45, 0, 32, 45, 46, 45, 5, 18, 33, 19, 19, 33, 20, 18, 34, 33, 18, 17, 34, 33, 21, 20, 33, 34, 21, 45, 32, 44, 45, 6, 5, 32, 31, 44, 45, 44, 6, 34, 17, 35, 21, 34, 22, 35, 17, 16, 22, 34, 35, 31, 30, 44, 44, 7, 6, 30, 43, 44, 44, 43, 7, 30, 29, 43, 22, 35, 23, 16, 15, 35, 35, 36, 23, 35, 15, 36, 29, 42, 43, 29, 28, 42, 43, 8, 7, 43, 42, 8, 23, 36, 24, 28, 41, 42, 28, 27, 41, 36, 37, 24, 36, 14, 37, 36, 15, 14, 24, 37, 25, 41, 9, 42, 42, 9, 8, 26, 39, 27, 27, 40, 41, 27, 39, 40, 37, 38, 25, 25, 38, 26, 26, 38, 39, 37, 13, 38, 37, 14, 13, 40, 10, 41, 41, 10, 9, 39, 11, 40, 40, 11, 10, 38, 12, 39, 38, 13, 12, 39, 12, 11], "vertices": [2, 4, 164.12, 60.2, 0.864, 56, 52.38, 103.95, 0.136, 2, 4, 151.75, 44.93, 0.864, 56, 40.01, 88.68, 0.136, 2, 4, 141.63, 6.97, 0.864, 56, 29.89, 50.72, 0.136, 2, 4, 153.57, -14.67, 0.864, 56, 41.83, 29.07, 0.136, 2, 4, 164.93, -21.9, 0.864, 56, 53.19, 21.84, 0.136, 2, 4, 189.68, -6.69, 0.7, 56, 77.94, 37.05, 0.3, 2, 4, 215.82, 5.41, 0.7, 56, 104.08, 49.16, 0.3, 2, 4, 249.74, 23.71, 0.7, 56, 138, 67.46, 0.3, 2, 4, 275.86, 52.02, 0.7, 56, 164.12, 95.76, 0.3, 2, 4, 299.6, 90.41, 0.7, 56, 187.87, 134.16, 0.3, 2, 4, 311.7, 124.14, 0.7, 56, 199.96, 167.88, 0.3, 2, 4, 317.36, 156.1, 0.7, 56, 205.62, 199.84, 0.3, 2, 4, 316.76, 194.94, 0.7, 56, 205.02, 238.68, 0.3, 2, 4, 311.48, 230.21, 0.7, 56, 199.74, 273.96, 0.3, 2, 4, 293.65, 265.78, 0.7, 56, 181.91, 309.53, 0.3, 2, 4, 263.81, 291.25, 0.7, 56, 152.07, 334.99, 0.3, 2, 4, 229.97, 300.14, 0.7, 56, 118.23, 343.88, 0.3, 2, 4, 197.2, 299.63, 0.7, 56, 85.46, 343.38, 0.3, 2, 4, 174.94, 288.7, 0.7, 56, 63.2, 332.44, 0.3, 2, 4, 168.27, 273.42, 0.7, 56, 56.54, 317.16, 0.3, 2, 4, 176.6, 270.78, 0.7, 56, 64.86, 314.53, 0.3, 2, 4, 197.88, 278.01, 0.7, 56, 86.14, 321.76, 0.3, 2, 4, 222.07, 275.62, 0.7, 56, 110.33, 319.37, 0.3, 2, 4, 245.77, 259.77, 0.7, 56, 134.03, 303.52, 0.3, 2, 4, 260.21, 240.67, 0.7, 56, 148.47, 284.42, 0.3, 2, 4, 270.99, 212.89, 0.7, 56, 159.25, 256.63, 0.3, 2, 4, 275.92, 183.98, 0.7, 56, 164.18, 227.73, 0.3, 2, 4, 268.8, 153.51, 0.7, 56, 157.06, 197.25, 0.3, 2, 4, 258.92, 122.99, 0.7, 56, 147.18, 166.74, 0.3, 1, 4, 242.01, 100.99, 1, 2, 4, 221.62, 80.67, 0.7, 56, 109.88, 124.41, 0.3, 2, 4, 204.77, 69.14, 0.7, 56, 93.03, 112.89, 0.3, 2, 4, 186.26, 61.3, 0.7, 56, 74.52, 105.04, 0.3, 2, 4, 176.07, 279.05, 0.7, 55, 135.95, 370.72, 0.3, 2, 4, 197.48, 288.14, 0.7, 55, 157.36, 379.8, 0.3, 2, 4, 226.44, 288.13, 0.7, 55, 186.32, 379.8, 0.3, 2, 4, 259.68, 272.26, 0.7, 55, 219.56, 363.92, 0.3, 2, 4, 278.47, 247.4, 0.7, 55, 238.35, 339.07, 0.3, 2, 4, 291.32, 214.83, 0.7, 55, 251.2, 306.49, 0.3, 2, 4, 295.32, 180.08, 0.7, 55, 255.2, 271.74, 0.3, 2, 4, 291.57, 148.07, 0.7, 55, 251.45, 239.73, 0.3, 2, 4, 281.02, 116.51, 0.7, 55, 240.9, 208.18, 0.3, 2, 4, 265.94, 85.74, 0.7, 55, 225.83, 177.41, 0.3, 2, 4, 245.99, 59.42, 0.7, 55, 205.87, 151.09, 0.3, 2, 4, 220.57, 38.95, 0.7, 55, 180.45, 130.62, 0.3, 2, 4, 192.52, 24.38, 0.7, 55, 152.4, 116.05, 0.3, 2, 4, 169.22, 13.28, 0.7, 55, 129.1, 104.94, 0.3], "hull": 33, "edges": [0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 0, 64, 38, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 321, "height": 178}}, "shouji/tx_baodian02": {"shouji/tx_baodian02": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian03": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian04": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian05": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian06": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian07": {"y": 33.11, "width": 50, "height": 100}, "shouji/tx_baodian08": {"y": 33.11, "width": 50, "height": 100}}}}], "events": {"atk": {}}, "animations": {"die": {"slots": {"qianyang13": {"attachment": [{"name": null}]}, "qianyang08": {"attachment": [{"name": "qianyang08"}]}, "qianyang017": {"attachment": [{"time": 0.1333, "name": "qianyang017"}]}}, "bones": {"allbone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 18.57, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": -14.51}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -4.12, "y": 9.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -4.17, "y": -7.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -70.6, "y": 95.72}]}, "allbone3": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": 7.08}]}, "allbone4": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 7.08, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -56.92}]}, "allbone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "angle": -8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 10.93, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 2.71}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -4.12, "y": 9.35, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "x": -4.17, "y": -7.25, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -70.6, "y": 95.72}]}, "allbone6": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1667, "angle": -8.98, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 7.08}]}, "allbone7": {"rotate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.0667, "angle": 0.58, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "angle": 13.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.9}]}, "allbone8": {"rotate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.0667, "angle": 0.58, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "angle": 13.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.9}]}, "allbone9": {"rotate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.0667, "angle": 0.58, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "angle": 13.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.9}]}, "allbone10": {"rotate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.0667, "angle": 0.58, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "angle": 13.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.9}]}, "allbone11": {"rotate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.0667, "angle": 0.58, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "angle": 13.21, "curve": 0.25, "c3": 0.75}, {"time": 0.3333, "angle": 2.9}]}, "allbone12": {"rotate": [{"angle": -0.02}]}, "allbone13": {"rotate": [{"angle": 0.02}]}, "allbone14": {"rotate": [{"angle": 0.23}]}, "allbone18": {"rotate": [{"angle": -0.32}]}, "allbone19": {"rotate": [{"angle": 0.52}]}, "allbone20": {"rotate": [{"angle": -0.33}]}, "allbone24": {"rotate": [{"angle": 0.31}]}, "allbone25": {"rotate": [{"angle": -0.27}]}, "allbone26": {"rotate": [{"angle": 1.52}]}, "allbone30": {"rotate": [{"angle": -0.13}]}, "allbone31": {"rotate": [{"angle": 0.04}]}, "allbone32": {"rotate": [{"angle": -0.61}]}, "allbone35": {"rotate": [{"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 50.91}], "translate": [{"curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.0667, "x": -7.73, "y": -1.42, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "x": -44.84, "y": -72.51, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "x": -103.64, "y": 42.86}]}, "allbone49": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 22.66}]}, "allbone50": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 22.66}]}, "allbone51": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3, "angle": 22.66}]}, "allbone": {"translate": [{"time": 0.0333, "curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": -20.57, "curve": 0.25, "c3": 0.75}, {"time": 0.2333}]}}}, "hurt": {"slots": {"qianyang13": {"attachment": [{"name": null}]}, "shouji/tx_baodian02": {"attachment": [{"time": 0.1333, "name": "shouji/tx_baodian02"}, {"time": 0.1667, "name": "shouji/tx_baodian03"}, {"time": 0.2, "name": "shouji/tx_baodian04"}, {"time": 0.2333, "name": "shouji/tx_baodian05"}, {"time": 0.2667, "name": "shouji/tx_baodian06"}, {"time": 0.3, "name": "shouji/tx_baodian07"}, {"time": 0.3333, "name": "shouji/tx_baodian08"}]}, "shouji/tx_baodian3": {"attachment": [{"time": 0.1333, "name": "shouji/tx_baodian02"}, {"time": 0.1667, "name": "shouji/tx_baodian03"}, {"time": 0.2, "name": "shouji/tx_baodian04"}, {"time": 0.2333, "name": "shouji/tx_baodian05"}, {"time": 0.2667, "name": "shouji/tx_baodian06"}, {"time": 0.3, "name": "shouji/tx_baodian07"}, {"time": 0.3333, "name": "shouji/tx_baodian08"}]}, "qianyang08": {"attachment": [{"name": "qianyang08"}]}, "qianyang017": {"attachment": [{"time": 0.1333, "name": "qianyang017"}, {"time": 0.3667, "name": "qianyang018"}]}, "shouji/tx_baodian2": {"attachment": [{"time": 0.1333, "name": "shouji/tx_baodian02"}, {"time": 0.1667, "name": "shouji/tx_baodian03"}, {"time": 0.2, "name": "shouji/tx_baodian04"}, {"time": 0.2333, "name": "shouji/tx_baodian05"}, {"time": 0.2667, "name": "shouji/tx_baodian06"}, {"time": 0.3, "name": "shouji/tx_baodian07"}, {"time": 0.3333, "name": "shouji/tx_baodian08"}]}, "shouji/tx_baodian4": {"attachment": [{"time": 0.1333, "name": "shouji/tx_baodian02"}, {"time": 0.1667, "name": "shouji/tx_baodian03"}, {"time": 0.2, "name": "shouji/tx_baodian04"}, {"time": 0.2333, "name": "shouji/tx_baodian05"}, {"time": 0.2667, "name": "shouji/tx_baodian06"}, {"time": 0.3, "name": "shouji/tx_baodian07"}, {"time": 0.3333, "name": "shouji/tx_baodian08"}]}}, "bones": {"allbone2": {"rotate": [{"angle": -11.75, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0333, "angle": -16.41, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": 10.34, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": -11.75}], "translate": [{"time": 0.1, "curve": 0.33, "c2": 0.32, "c3": 0.693, "c4": 0.75}, {"time": 0.2333, "y": 27.72, "curve": 0.377, "c2": 0.61, "c3": 0.72}, {"time": 0.3}]}, "allbone3": {"rotate": [{"angle": 2.32, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1, "angle": -14.09, "curve": 0.25, "c3": 0.75}, {"time": 0.2, "angle": 13.91, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3, "angle": 6.58, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3667, "angle": 2.32}]}, "allbone4": {"rotate": [{"angle": 6.89, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.0333, "angle": 5.48, "curve": 0.32, "c2": 0.29, "c3": 0.757}, {"time": 0.1333, "angle": -10.93, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 16.29, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.3, "angle": 12.32, "curve": 0.339, "c2": 0.35, "c3": 0.697, "c4": 0.76}, {"time": 0.3667, "angle": 6.89}]}, "allbone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "angle": -4.32, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "angle": 20.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.1, "y": 10.6, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "y": 38.32, "curve": 0.25, "c3": 0.75}, {"time": 0.3667}]}, "allbone6": {"rotate": [{"angle": 2.32, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.25, "c3": 0.75}, {"time": 0.1333, "angle": -4.32, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.2333, "angle": 13.34, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.3, "angle": 6.3, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 2.32}]}, "allbone7": {"rotate": [{"angle": 3.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.0667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.2667, "angle": 10.2, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 0.3667, "angle": 3.75}]}, "allbone8": {"rotate": [{"angle": 8.88, "curve": 0.303, "c2": 0.24, "c3": 0.661, "c4": 0.65}, {"time": 0.0667, "angle": 3.75, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.1333, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.3333, "angle": 10.2, "curve": 0.284, "c3": 0.625, "c4": 0.38}, {"time": 0.3667, "angle": 8.88}]}, "allbone9": {"rotate": [{"angle": 10.2, "curve": 0.258, "c3": 0.619, "c4": 0.45}, {"time": 0.0667, "angle": 6.45, "curve": 0.363, "c2": 0.44, "c3": 0.755}, {"time": 0.1667, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.3667, "angle": 10.2}]}, "allbone10": {"rotate": [{"angle": 7.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.0667, "angle": 10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.2333, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 0.3667, "angle": 7.31}]}, "allbone11": {"rotate": [{"angle": 2.9, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.0667, "angle": 7.31, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.1333, "angle": 10.2, "curve": 0.25, "c3": 0.75}, {"time": 0.3, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3667, "angle": 2.9}]}, "allbone12": {"rotate": [{"angle": -0.02, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -0.88, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": -6.9, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.02}]}, "allbone13": {"rotate": [{"angle": 0.02, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": 12.42, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": 1.31, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 0.02}]}, "allbone14": {"rotate": [{"angle": 0.23, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -6.38, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": -0.44, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 0.23}]}, "allbone18": {"rotate": [{"angle": -0.32, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": 5.43, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": -0.5, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.32}]}, "allbone19": {"rotate": [{"angle": 0.52, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -3.02, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": -13.33, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 0.52}]}, "allbone20": {"rotate": [{"angle": -0.33, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": 1.35, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": 5.54, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.33}]}, "allbone24": {"rotate": [{"angle": 0.31, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": 4.72, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": -1.18, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 0.31}]}, "allbone25": {"rotate": [{"angle": -0.27, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -1.26, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": -13.35, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.27}]}, "allbone26": {"rotate": [{"angle": 1.52, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": 2.22, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": 6.71, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 1.52}]}, "allbone30": {"rotate": [{"angle": -0.13, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": 9.47, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": -8.8, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.13}]}, "allbone31": {"rotate": [{"angle": 0.04, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": -7.72, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": -2.37, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 0.04}]}, "allbone32": {"rotate": [{"angle": -0.61, "curve": 0.26, "c3": 0.618, "c4": 0.44}, {"time": 0.1, "angle": 2.05, "curve": 0.359, "c2": 0.43, "c3": 0.756}, {"time": 0.2333, "angle": 4.49, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -0.61}]}, "allbone46": {"rotate": [{"angle": 28.02, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.1667, "angle": -26.57, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.3667, "angle": 28.02}]}, "allbone47": {"rotate": [{"angle": 22.86, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 0.1667, "angle": -24.19, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.3667, "angle": 22.86}]}, "allbone48": {"rotate": [{"angle": 7.45, "curve": 0.324, "c2": 0.31, "c3": 0.671, "c4": 0.68}, {"time": 0.1667, "angle": -20.57, "curve": 0.382, "c2": 0.56, "c3": 0.739}, {"time": 0.3667, "angle": 7.45}]}, "allbone49": {"rotate": [{"angle": -23.83, "curve": 0.253, "c3": 0.621, "c4": 0.48}, {"time": 0.1667, "angle": 33.95, "curve": 0.37, "c2": 0.48, "c3": 0.753}, {"time": 0.3667, "angle": -23.83}]}, "allbone50": {"rotate": [{"angle": -16.32, "curve": 0.349, "c2": 0.39, "c3": 0.686, "c4": 0.74}, {"time": 0.1667, "angle": 36.51, "curve": 0.373, "c2": 0.62, "c3": 0.713}, {"time": 0.3667, "angle": -16.32}]}, "allbone51": {"rotate": [{"angle": 14.63, "curve": 0.341, "c2": 0.36, "c3": 0.682, "c4": 0.72}, {"time": 0.1667, "angle": 38.46, "curve": 0.381, "c2": 0.59, "c3": 0.727}, {"time": 0.3667, "angle": 14.63}]}, "allbone": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.0667, "x": 8.08, "curve": 0.25, "c3": 0.75}, {"time": 0.1667, "x": -32.73, "curve": 0.25, "c3": 0.75}, {"time": 0.2667}]}}}, "idle": {"slots": {"qianyang13": {"attachment": [{"name": null}]}, "qianyang08": {"attachment": [{"name": "qianyang08"}]}, "qianyang017": {"attachment": [{"time": 1.6333, "name": "qianyang017"}, {"time": 1.7667, "name": "qianyang018"}]}}, "bones": {"allbone2": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "allbone3": {"rotate": [{"angle": 1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.26}]}, "allbone4": {"rotate": [{"angle": 3.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.17}]}, "allbone5": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "allbone6": {"rotate": [{"angle": 1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.26}]}, "allbone7": {"rotate": [{"angle": -14.04, "curve": 0.286, "c2": 0.18, "c3": 0.664, "c4": 0.66}, {"time": 0.5333, "angle": -4.28, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "angle": -15.07, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -14.04}]}, "allbone8": {"rotate": [{"angle": -13.11, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "angle": -15.07, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5333, "angle": -10.79, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -13.11}]}, "allbone9": {"rotate": [{"angle": -6.87, "curve": 0.372, "c2": 0.48, "c3": 0.752}, {"time": 0.5333, "angle": -15.07, "curve": 0.25, "c3": 0.75}, {"time": 1.5333, "curve": 0.252, "c3": 0.622, "c4": 0.48}, {"time": 2, "angle": -6.87}]}, "allbone10": {"rotate": [{"angle": -1.02, "curve": 0.286, "c2": 0.18, "c3": 0.664, "c4": 0.66}, {"time": 0.5333, "angle": -10.79, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.8667, "angle": -15.07, "curve": 0.25, "c3": 0.75}, {"time": 1.8667, "curve": 0.297, "c3": 0.634, "c4": 0.37}, {"time": 2, "angle": -1.02}]}, "allbone11": {"rotate": [{"angle": -1.96, "curve": 0.375, "c2": 0.62, "c3": 0.716}, {"time": 0.2, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.5333, "angle": -4.28, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1.2, "angle": -15.07, "curve": 0.243, "c3": 0.68, "c4": 0.71}, {"time": 2, "angle": -1.96}]}, "allbone12": {"rotate": [{"angle": -0.02}]}, "allbone13": {"rotate": [{"angle": 0.02}]}, "allbone14": {"rotate": [{"angle": 0.23}]}, "allbone18": {"rotate": [{"angle": -0.32}]}, "allbone19": {"rotate": [{"angle": 0.52}]}, "allbone20": {"rotate": [{"angle": -0.33}]}, "allbone24": {"rotate": [{"angle": 0.31}]}, "allbone25": {"rotate": [{"angle": -0.27}]}, "allbone26": {"rotate": [{"angle": 1.52}]}, "allbone30": {"rotate": [{"angle": -0.13}]}, "allbone31": {"rotate": [{"angle": 0.04}]}, "allbone32": {"rotate": [{"angle": -0.61}]}, "allbone35": {"rotate": [{"angle": 4.82, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "angle": 13.09, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "angle": 4.82}], "translate": [{"x": -14.35, "y": -5.74, "curve": 0.381, "c2": 0.55, "c3": 0.742}, {"time": 0.4, "curve": 0.25, "c3": 0.75}, {"time": 1.4, "x": -39.02, "y": -15.6, "curve": 0.245, "c3": 0.637, "c4": 0.56}, {"time": 2, "x": -14.35, "y": -5.74}]}, "allbone36": {"rotate": [{"angle": 3.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.17}], "translate": [{"x": -1.35, "y": -6.87, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": -1.89, "y": -9.59, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "x": -1.35, "y": -6.87}]}, "allbone37": {"rotate": [{"angle": 3.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.17}], "translate": [{"x": 13.75, "y": 3.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "x": 24.37, "y": 5.9, "curve": 0.337, "c2": 0.35, "c3": 0.675, "c4": 0.69}, {"time": 2, "x": 13.75, "y": 3.33}]}, "allbone38": {"rotate": [{"angle": 1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.26}], "translate": [{"x": 3.44, "y": 3.33, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": 12.14, "y": 11.74, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": 3.44, "y": 3.33}]}, "allbone39": {"rotate": [{"angle": 1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.26}], "translate": [{"x": -0.54, "y": -2.72, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "x": -1.89, "y": -9.59, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "x": -0.54, "y": -2.72}]}, "allbone40": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": -1.89, "y": -9.59, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "allbone41": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 2}], "translate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "x": 12.14, "y": 11.74, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "allbone42": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "allbone43": {"rotate": [{"angle": 1.97, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "angle": 3.35, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "angle": 1.97}], "translate": [{"x": 3.07, "y": -5.04, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.5667, "curve": 0.25, "c3": 0.75}, {"time": 1.5667, "x": 5.21, "y": -8.56, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2, "x": 3.07, "y": -5.04}]}, "allbone44": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "allbone45": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 1, "angle": 4.42, "curve": 0.25, "c3": 0.75}, {"time": 2}]}, "allbone46": {"rotate": [{"angle": 1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.26}]}, "allbone47": {"rotate": [{"angle": 1.26, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 4.42, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 1.26}]}, "allbone48": {"rotate": [{"angle": 3.17, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 4.42, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 3.17}]}, "allbone49": {"rotate": [{"angle": 2.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.3333, "curve": 0.25, "c3": 0.75}, {"time": 1.3333, "angle": 9.4, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 2, "angle": 2.67}]}, "allbone50": {"rotate": [{"angle": 6.72, "curve": 0.325, "c2": 0.31, "c3": 0.675, "c4": 0.69}, {"time": 0.3333, "angle": 2.67, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 0.6667, "curve": 0.25, "c3": 0.75}, {"time": 1.6667, "angle": 9.4, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 2, "angle": 6.72}]}, "allbone51": {"rotate": [{"angle": 9.4, "curve": 0.265, "c3": 0.618, "c4": 0.43}, {"time": 0.3333, "angle": 6.73, "curve": 0.351, "c2": 0.4, "c3": 0.757}, {"time": 1, "curve": 0.25, "c3": 0.75}, {"time": 2, "angle": 9.4}]}}}, "run": {"bones": {"allbone": {"rotate": [{"angle": 3.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "angle": -10.27, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": 12.16, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7667, "angle": 3.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.9667, "angle": -4.27, "curve": 0.249, "c3": 0.627, "c4": 0.51}, {"time": 1.2, "angle": 1.53, "curve": 0.337, "c2": 0.35, "c3": 0.673, "c4": 0.69}, {"time": 1.6667, "angle": 24.33, "curve": 0.382, "c2": 0.57, "c3": 0.737}, {"time": 2.3667, "angle": 21.93, "curve": 0.25, "c3": 0.75}, {"time": 3, "angle": 6.33, "curve": "stepped"}, {"time": 3.2, "angle": 6.33, "curve": 0.377, "c2": 0.51, "c3": 0.749}, {"time": 3.4, "angle": 12.16, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 3.5333, "angle": 3.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 3.7333, "angle": -10.27, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": 12.16, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4.3, "angle": 3.06, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 4.5, "angle": -10.27, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": 12.16, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 5.0333, "angle": 3.06}], "translate": [{"x": -0.31, "y": 15.46, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -6.83, "y": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -0.31, "y": 15.46, "curve": 0.243, "c3": 0.649, "c4": 0.6}, {"time": 1.1667, "x": -4.98, "y": 0.35, "curve": 0.343, "c2": 0.37, "c3": 0.678, "c4": 0.71}, {"time": 1.6667, "x": 11.19, "y": 17.53, "curve": "stepped"}, {"time": 2.3667, "x": 11.19, "y": 17.53, "curve": 0.25, "c3": 0.75}, {"time": 3, "x": -4.98, "y": 0.35, "curve": "stepped"}, {"time": 3.2, "x": -4.98, "y": 0.35, "curve": 0.382, "c2": 0.57, "c3": 0.735}, {"time": 3.3, "x": -6.83, "y": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -0.31, "y": 15.46, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -6.83, "y": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": -0.31, "y": 15.46, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "x": -6.83, "y": -5.64, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": -0.31, "y": 15.46}]}, "allbone17": {"rotate": [{"angle": 11.92, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": 36.4, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -36.49, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.7667, "angle": 11.92, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.9, "angle": 36.4, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.3, "angle": -3, "curve": "stepped"}, {"time": 3.1333, "angle": -3, "curve": 0.378, "c2": 0.51, "c3": 0.748}, {"time": 3.3, "angle": -36.49, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.5333, "angle": 11.92, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.7, "angle": 36.4, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -36.49, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.3, "angle": 11.92, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 4.4333, "angle": 36.4, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": -36.49, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.0333, "angle": 11.92}], "translate": [{"x": -46.42, "y": -36.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "x": -129.95, "y": -53.86, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": 118.81, "y": -0.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.7667, "x": -46.42, "y": -36.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.9, "x": -129.95, "y": -53.86, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.3, "x": 4.49, "y": -25.13, "curve": 0.25, "c3": 0.75}, {"time": 1.6, "x": -123.69, "y": -50.61, "curve": 0.25, "c3": 0.75}, {"time": 1.9667, "x": -47.08, "y": -27.76, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "x": -107.09, "y": -40.33, "curve": 0.25, "c3": 0.75}, {"time": 2.6333, "x": 7.2, "y": -7.25, "curve": 0.25, "c3": 0.75}, {"time": 2.9, "x": 4.49, "y": -25.13, "curve": "stepped"}, {"time": 3.1333, "x": 4.49, "y": -25.13, "curve": 0.378, "c2": 0.51, "c3": 0.748}, {"time": 3.3, "x": 118.81, "y": -0.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.5333, "x": -46.42, "y": -36.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.7, "x": -129.95, "y": -53.86, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": 118.81, "y": -0.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.3, "x": -46.42, "y": -36.01, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 4.4333, "x": -129.95, "y": -53.86, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "x": 118.81, "y": -0.7, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.0333, "x": -46.42, "y": -36.01}]}, "allbone57": {"rotate": [{"angle": 36.4, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -36.49, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": 36.4, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3, "angle": -12.02, "curve": "stepped"}, {"time": 3.1333, "angle": -12.02, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.2333, "angle": -36.49, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": 36.4, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": -36.49, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": 36.4, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": -36.49, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "angle": 36.4}], "translate": [{"x": -135.38, "y": -74.42, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": 113.1, "y": -20, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": -135.38, "y": -74.42, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3, "x": 29.67, "y": -38.27, "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 1.4333, "x": -48.58, "y": -65.2, "curve": 0.337, "c2": 0.35, "c3": 0.67, "c4": 0.68}, {"time": 1.8, "x": 16.12, "y": -26.14, "curve": 0.358, "c2": 0.64, "c3": 0.694}, {"time": 2.1333, "x": -41.71, "y": -61.16, "curve": 0.358, "c2": 0.44, "c3": 0.695, "c4": 0.8}, {"time": 2.5, "x": 37.81, "y": -19.42, "curve": 0.358, "c2": 0.64, "c3": 0.694}, {"time": 2.9, "x": 29.67, "y": -38.27, "curve": "stepped"}, {"time": 3.1333, "x": 29.67, "y": -38.27, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.2333, "x": 113.1, "y": -20, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -135.38, "y": -74.42, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": 113.1, "y": -20, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": -135.38, "y": -74.42, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "x": 113.1, "y": -20, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": -135.38, "y": -74.42}]}, "allbone23": {"rotate": [{"angle": -16.67, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "angle": -41.2, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": 31.85, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.7667, "angle": -16.67, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.9, "angle": -41.2, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.3, "angle": -1.72, "curve": 0.337, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 1.6667, "angle": -24.52, "curve": "stepped"}, {"time": 2.2333, "angle": -24.52, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 2.9, "angle": -1.72, "curve": "stepped"}, {"time": 3.0667, "angle": -1.72, "curve": 0.378, "c2": 0.51, "c3": 0.748}, {"time": 3.3, "angle": -10.15, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.5333, "angle": -16.67, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.7, "angle": -41.2, "curve": 0.305, "c3": 0.64, "c4": 0.36}, {"time": 3.7333, "angle": -38.18, "curve": 0.289, "c2": 0.17, "c3": 0.755}, {"time": 4.0333, "angle": 31.85, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.3, "angle": -16.67, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 4.4333, "angle": -41.2, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": 31.85, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.0333, "angle": -16.67}], "translate": [{"x": 103.13, "y": -49.93, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "x": 172.83, "y": -48.79, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -34.75, "y": -52.19, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.7667, "x": 103.13, "y": -49.93, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.9, "x": 172.83, "y": -48.79, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.3, "x": 60.64, "y": -50.63, "curve": 0.337, "c2": 0.34, "c3": 0.672, "c4": 0.68}, {"time": 1.6667, "x": 69.2, "y": -31.86, "curve": "stepped"}, {"time": 2.2333, "x": 69.2, "y": -31.86, "curve": 0.382, "c2": 0.56, "c3": 0.738}, {"time": 2.9, "x": 60.64, "y": -50.63, "curve": "stepped"}, {"time": 3.0667, "x": 60.64, "y": -50.63, "curve": 0.345, "c2": 0.37, "c3": 0.687, "c4": 0.73}, {"time": 3.2667, "x": 87.82, "y": -51.29, "curve": 0.378, "c2": 0.6, "c3": 0.722}, {"time": 3.5333, "x": 103.13, "y": -49.93, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.7, "x": 172.83, "y": -48.79, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -34.75, "y": -52.19, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.3, "x": 103.13, "y": -49.93, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 4.4333, "x": 172.83, "y": -48.79, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "x": -34.75, "y": -52.19, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.0333, "x": 103.13, "y": -49.93}]}, "allbone29": {"rotate": [{"angle": -43.29, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": 29.77, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "angle": -43.29, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3, "angle": 5.24, "curve": 0.34, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 1.6667, "angle": -27.16, "curve": "stepped"}, {"time": 2.2333, "angle": -27.16, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.9, "angle": 5.24, "curve": "stepped"}, {"time": 3.0667, "angle": 5.24, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.2, "angle": -2.63, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "angle": -43.29, "curve": 0.247, "c3": 0.63, "c4": 0.52}, {"time": 3.7333, "angle": -2.45, "curve": 0.379, "c2": 0.52, "c3": 0.747}, {"time": 3.9, "angle": 29.77, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "angle": -43.29, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": 29.77, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "angle": -43.29}], "translate": [{"x": 65.87, "y": -34.79, "curve": 0.25, "c3": 0.75}, {"time": 0.3667, "x": -141.66, "y": -29.25, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "x": 65.87, "y": -34.79, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 1.3, "x": -71.98, "y": -31.11, "curve": 0.34, "c2": 0.35, "c3": 0.674, "c4": 0.69}, {"time": 1.6667, "x": -35.39, "y": -21.88, "curve": "stepped"}, {"time": 2.2333, "x": -35.39, "y": -21.88, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 2.9, "x": -71.98, "y": -31.11, "curve": "stepped"}, {"time": 3.0667, "x": -71.98, "y": -31.11, "curve": 0.34, "c2": 0.36, "c3": 0.675, "c4": 0.69}, {"time": 3.2, "x": -23.4, "y": -51.15, "curve": 0.345, "c2": 0.38, "c3": 0.68, "c4": 0.72}, {"time": 3.2667, "x": 18.58, "y": -55.53, "curve": 0.374, "c2": 0.62, "c3": 0.714}, {"time": 3.5333, "x": 65.87, "y": -34.79, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "x": -141.66, "y": -29.25, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "x": 65.87, "y": -34.79, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "x": -141.66, "y": -29.25, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": 65.87, "y": -34.79}]}, "allbone2": {"rotate": [{"angle": 3.95, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0667, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.4667, "angle": -7.25, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.7667, "angle": 3.95, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.8, "angle": 4.89, "curve": 0.296, "c3": 0.633, "c4": 0.37}, {"time": 1.2333, "angle": 3.32, "curve": 0.305, "c2": 0.23, "c3": 0.645, "c4": 0.58}, {"time": 2.2333, "angle": 8.34, "curve": 0.324, "c2": 0.3, "c3": 0.664, "c4": 0.66}, {"time": 3.2333, "angle": -7.25, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.5333, "angle": 3.95, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3.6, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": -7.25, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.3, "angle": 3.95, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.3333, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.7333, "angle": -7.25, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 5.0333, "angle": 3.95}]}, "allbone3": {"rotate": [{"angle": 2.16, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.5, "angle": -7.25, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7667, "angle": 2.16, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.8667, "angle": 4.89, "curve": 0.301, "c3": 0.637, "c4": 0.36}, {"time": 1.2333, "angle": 3.69, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 15.69, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": 3.69, "curve": 0.25, "c3": 0.75}, {"time": 2.8333, "angle": 16.89, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": 3.69, "curve": 0.301, "c2": 0.21, "c3": 0.642, "c4": 0.57}, {"time": 3.3, "angle": -7.25, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 3.5333, "angle": 2.16, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 3.6333, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.0333, "angle": -7.25, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 4.3, "angle": 2.16, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.4, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.8, "angle": -7.25, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 5.0333, "angle": 2.16}]}, "allbone5": {"rotate": [{"angle": 3.95, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0667, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.4667, "angle": -7.25, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.7667, "angle": 3.95, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.8, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.2333, "angle": -7.25, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.5333, "angle": 3.95, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3.6, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4, "angle": -7.25, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.3, "angle": 3.95, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.3333, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.7333, "angle": -7.25, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 5.0333, "angle": 3.95}]}, "allbone6": {"rotate": [{"angle": 2.16, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.5, "angle": -7.25, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7667, "angle": 2.16, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.8667, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.3, "angle": -7.25, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 3.5333, "angle": 2.16, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 3.6333, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.0333, "angle": -7.25, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 4.3, "angle": 2.16, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.4, "angle": 4.89, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.8, "angle": -7.25, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 5.0333, "angle": 2.16}]}, "allbone4": {"rotate": [{"angle": -2.67, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1667, "angle": 9.77, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.5667, "angle": -20.91, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7667, "angle": -2.67, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.9, "angle": 9.77, "curve": 0.303, "c3": 0.639, "c4": 0.36}, {"time": 1.2333, "angle": -15.62, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "angle": 2.38, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": -15.62, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "angle": -7.22, "curve": 0.25, "c3": 0.75}, {"time": 3.1667, "angle": -15.62, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "angle": -20.91, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 3.5333, "angle": -2.67, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 3.7, "angle": 9.77, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.1, "angle": -20.91, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4.3, "angle": -2.67, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 4.4333, "angle": 9.77, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.8333, "angle": -20.91, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.0333, "angle": -2.67}]}, "allbone46": {"rotate": [{"curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.4, "angle": -18.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.7667, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.2333, "angle": -3.56, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "angle": 5.57, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "angle": -11.7, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "angle": 5.14, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": -18.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.5333, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.9333, "angle": -18.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.3, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.7, "angle": -18.56, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 5.0333}], "scale": [{"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": 1.1, "curve": 0.25, "c3": 0.75}, {"time": 2.3, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "x": 1.08, "curve": 0.25, "c3": 0.75}, {"time": 3.2}]}, "allbone47": {"rotate": [{"angle": -5.3}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -18.56}, {"time": 0.7667, "angle": -5.3}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -18.56}, {"time": 3.5333, "angle": -5.3}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -18.56}, {"time": 4.3, "angle": -5.3}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": -18.56}, {"time": 5.0333, "angle": -5.3}]}, "allbone48": {"rotate": [{"angle": -10.61}, {"time": 0.2, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "angle": -18.56}, {"time": 0.7667, "angle": -10.61}, {"time": 0.9667, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "angle": -18.56}, {"time": 3.5333, "angle": -10.61}, {"time": 3.7333, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "angle": -18.56}, {"time": 4.3, "angle": -10.61}, {"time": 4.5, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "angle": -18.56}, {"time": 5.0333, "angle": -10.61}]}, "allbone49": {"rotate": [{"curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 0.4, "angle": 23.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 0.7667, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 1.2333, "angle": 6.68, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "angle": 16.47, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "angle": 10.66, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 3.5333, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 3.9333, "angle": 23.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 4.3, "curve": 0.248, "c3": 0.628, "c4": 0.52}, {"time": 4.7, "angle": 23.85, "curve": 0.378, "c2": 0.52, "c3": 0.748}, {"time": 5.0333}], "scale": [{"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": 0.76, "curve": 0.25, "c3": 0.75}, {"time": 2.2333, "curve": 0.25, "c3": 0.75}, {"time": 2.7667, "x": 0.88, "curve": 0.25, "c3": 0.75}, {"time": 3.2}]}, "allbone50": {"rotate": [{"angle": 6.82}, {"time": 0.1}, {"time": 0.5, "angle": 23.85}, {"time": 0.7667, "angle": 6.82}, {"time": 0.8667}, {"time": 3.3, "angle": 23.85}, {"time": 3.5333, "angle": 6.82}, {"time": 3.6333}, {"time": 4.0333, "angle": 23.85}, {"time": 4.3, "angle": 6.82}, {"time": 4.4}, {"time": 4.8, "angle": 23.85}, {"time": 5.0333, "angle": 6.82}]}, "allbone51": {"rotate": [{"angle": 13.63}, {"time": 0.2}, {"time": 0.6, "angle": 23.85}, {"time": 0.7667, "angle": 13.63}, {"time": 0.9667}, {"time": 3.4, "angle": 23.85}, {"time": 3.5333, "angle": 13.63}, {"time": 3.7333}, {"time": 4.1333, "angle": 23.85}, {"time": 4.3, "angle": 13.63}, {"time": 4.5}, {"time": 4.9, "angle": 23.85}, {"time": 5.0333, "angle": 13.63}]}, "allbone7": {"rotate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.3667, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": "stepped"}, {"time": 1.1667, "curve": 0.25, "c3": 0.75}, {"time": 1.8333, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 2.5667, "curve": 0.25, "c3": 0.75}, {"time": 3.1, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.9, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.6333, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 5.0333}]}, "allbone8": {"rotate": [{"angle": -7.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "angle": -22.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 0.7667, "angle": -7.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -7.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 1.4667, "curve": 0.25, "c3": 0.75}, {"time": 2.0667, "angle": -22.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 2.5667, "angle": -7.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 2.8, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "angle": -22.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 3.5333, "angle": -7.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "angle": -22.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 4.3, "angle": -7.56, "curve": 0.382, "c2": 0.56, "c3": 0.74}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "angle": -22.5, "curve": 0.244, "c3": 0.641, "c4": 0.57}, {"time": 5.0333, "angle": -7.56}]}, "allbone9": {"rotate": [{"angle": -18.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.6667, "angle": -22.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 0.7667, "angle": -18.35, "curve": "stepped"}, {"time": 1.1667, "angle": -18.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 1.7, "curve": 0.25, "c3": 0.75}, {"time": 2.3667, "angle": -22.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 2.5667, "angle": -18.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.4333, "angle": -22.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 3.5333, "angle": -18.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.2, "angle": -22.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 4.3, "angle": -18.35, "curve": 0.333, "c2": 0.33, "c3": 0.758}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 4.9333, "angle": -22.5, "curve": 0.276, "c3": 0.621, "c4": 0.4}, {"time": 5.0333, "angle": -18.35}]}, "allbone10": {"rotate": [{"angle": -20.77, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.0667, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 0.4667, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 0.7667, "angle": -20.77, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 0.8, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -20.77, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 1.3, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 2, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 2.5667, "angle": -20.77, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 2.6333, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 3.2333, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 3.5333, "angle": -20.77, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 3.6, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 4, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 4.3, "angle": -20.77, "curve": 0.367, "c2": 0.63, "c3": 0.705}, {"time": 4.3333, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 4.7333, "curve": 0.244, "c3": 0.697, "c4": 0.78}, {"time": 5.0333, "angle": -20.77}]}, "allbone11": {"rotate": [{"angle": -9.13, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.2, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 0.6, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 0.7667, "angle": -9.13, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 0.9667, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 1.1667, "angle": -9.13, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 1.5333, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 2.2667, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 2.5667, "angle": -9.13, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 2.9333, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 3.4, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 3.5333, "angle": -9.13, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 3.7333, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 4.1333, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 4.3, "angle": -9.13, "curve": 0.367, "c2": 0.46, "c3": 0.754}, {"time": 4.5, "angle": -22.5, "curve": 0.25, "c3": 0.75}, {"time": 4.9, "curve": 0.255, "c3": 0.62, "c4": 0.47}, {"time": 5.0333, "angle": -9.13}]}, "allbone36": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -21.37, "y": -9.75, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -21.37, "y": -9.75, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -21.37, "y": -9.75, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": -21.37, "y": -9.75, "curve": 0.25, "c3": 0.75}, {"time": 5.0333}]}, "allbone39": {"translate": [{"x": -8.67, "y": -3.96, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.1667, "curve": 0.25, "c3": 0.75}, {"time": 0.5667, "x": -21.37, "y": -9.75, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 0.7667, "x": -8.67, "y": -3.96, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 3.3333, "x": -21.37, "y": -9.75, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 3.5333, "x": -8.67, "y": -3.96, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 3.7, "curve": 0.25, "c3": 0.75}, {"time": 4.1, "x": -21.37, "y": -9.75, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 4.3, "x": -8.67, "y": -3.96, "curve": 0.38, "c2": 0.53, "c3": 0.745}, {"time": 4.4333, "curve": 0.25, "c3": 0.75}, {"time": 4.8333, "x": -21.37, "y": -9.75, "curve": 0.246, "c3": 0.633, "c4": 0.54}, {"time": 5.0333, "x": -8.67, "y": -3.96}]}, "allbone40": {"translate": [{"x": -19.72, "y": -9, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 0.3, "curve": 0.25, "c3": 0.75}, {"time": 0.7, "x": -21.37, "y": -9.75, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 0.7667, "x": -19.72, "y": -9, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 3.0667, "curve": 0.25, "c3": 0.75}, {"time": 3.5, "x": -21.37, "y": -9.75, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 3.5333, "x": -19.72, "y": -9, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 3.8333, "curve": 0.25, "c3": 0.75}, {"time": 4.2333, "x": -21.37, "y": -9.75, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 4.3, "x": -19.72, "y": -9, "curve": 0.303, "c2": 0.22, "c3": 0.756}, {"time": 4.6, "curve": 0.25, "c3": 0.75}, {"time": 5, "x": -21.37, "y": -9.75, "curve": 0.295, "c3": 0.633, "c4": 0.37}, {"time": 5.0333, "x": -19.72, "y": -9}]}, "allbone41": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -13.76, "y": 13.72, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -13.76, "y": 13.72, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -13.76, "y": 13.72, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": -13.76, "y": 13.72, "curve": 0.25, "c3": 0.75}, {"time": 5.0333}]}, "allbone38": {"translate": [{"x": -4.29, "y": -0.78, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.1, "curve": 0.25, "c3": 0.75}, {"time": 0.5, "x": -19.11, "y": -3.47, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 0.7667, "x": -4.29, "y": -0.78, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 0.8667, "curve": 0.25, "c3": 0.75}, {"time": 3.3, "x": -19.11, "y": -3.47, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 3.5333, "x": -4.29, "y": -0.78, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 3.6333, "curve": 0.25, "c3": 0.75}, {"time": 4.0333, "x": -19.11, "y": -3.47, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 4.3, "x": -4.29, "y": -0.78, "curve": 0.381, "c2": 0.59, "c3": 0.729}, {"time": 4.4, "curve": 0.25, "c3": 0.75}, {"time": 4.8, "x": -19.11, "y": -3.47, "curve": 0.243, "c3": 0.658, "c4": 0.64}, {"time": 5.0333, "x": -4.29, "y": -0.78}]}, "allbone37": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": -13.76, "y": 13.72, "curve": 0.25, "c3": 0.75}, {"time": 0.7667, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -13.76, "y": 13.72, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": -13.76, "y": 13.72, "curve": 0.25, "c3": 0.75}, {"time": 4.3, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": -13.76, "y": 13.72, "curve": 0.25, "c3": 0.75}, {"time": 5.0333}]}, "root": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4667, "x": 58.85, "y": 31.46, "curve": "stepped"}, {"time": 0.6333, "x": 58.85, "y": 31.46, "curve": 0.25, "c3": 0.75}, {"time": 1.2333, "x": 102.73, "y": 13.63, "curve": "stepped"}, {"time": 3.2333, "x": 102.73, "y": 13.63, "curve": 0.357, "c2": 0.42, "c3": 0.756}, {"time": 3.8333, "x": 26.96, "y": 2.25, "curve": "stepped"}, {"time": 3.9, "x": 26.96, "y": 2.25, "curve": 0.25, "c3": 0.75}, {"time": 4.4667, "x": -83.89, "y": 2.25, "curve": "stepped"}, {"time": 4.5333, "x": -83.89, "y": 2.25, "curve": 0.25, "c3": 0.75}, {"time": 5.0333}], "scale": [{"time": 3.1667}, {"time": 3.2333, "x": -1, "curve": "stepped"}, {"time": 4.5, "x": -1}, {"time": 4.5333}]}, "allbone43": {"translate": [{"x": -4.43, "y": -16.44, "curve": 0.25, "c3": 0.75}, {"time": 0.4, "y": 14, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "x": -4.43, "y": -16.44, "curve": 0.272, "c3": 0.619, "c4": 0.41}, {"time": 1.2333, "x": -3.47, "y": -20.37, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -3.47, "y": 31.03, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": -3.47, "y": -20.37, "curve": 0.25, "c3": 0.75}, {"time": 2.7, "x": -3.47, "y": 31.03, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": -3.47, "y": -20.37, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "x": -4.43, "y": -16.44, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "y": 14, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "x": -4.43, "y": -16.44, "curve": 0.25, "c3": 0.75}, {"time": 4.6667, "y": 14, "curve": 0.25, "c3": 0.75}, {"time": 5.0333, "x": -4.43, "y": -16.44}]}, "allbone44": {"translate": [{"curve": 0.25, "c3": 0.75}, {"time": 0.4, "x": 13.7, "curve": 0.25, "c3": 0.75}, {"time": 0.7333, "curve": 0.27, "c3": 0.618, "c4": 0.41}, {"time": 1.2333, "x": 3.22, "curve": 0.25, "c3": 0.75}, {"time": 1.7667, "x": -10.22, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "x": 3.22, "curve": 0.25, "c3": 0.75}, {"time": 2.7333, "x": -10.22, "curve": 0.25, "c3": 0.75}, {"time": 3.2, "x": 3.22, "curve": 0.25, "c3": 0.75}, {"time": 3.5333, "curve": 0.25, "c3": 0.75}, {"time": 3.9333, "x": 13.7, "curve": 0.25, "c3": 0.75}, {"time": 4.2667, "curve": 0.25, "c3": 0.75}, {"time": 4.7, "x": 13.7, "curve": 0.25, "c3": 0.75}, {"time": 5.0333}]}, "allbone42": {"translate": [{"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "x": -2.63, "y": 22.11, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "x": -2.63, "y": 18.17, "curve": 0.25, "c3": 0.75}, {"time": 3.1}], "scale": [{"time": 1.2333, "curve": 0.25, "c3": 0.75}, {"time": 1.7333, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 2.2, "curve": 0.25, "c3": 0.75}, {"time": 2.6667, "y": 0.9, "curve": 0.25, "c3": 0.75}, {"time": 3.1}]}}}}}