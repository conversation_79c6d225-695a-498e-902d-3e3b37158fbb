import { _decorator, Component, EventTouch, ccenum, Node, NodeEventType, Vec2 } from "cc";

import { LayoutManager } from "./LayoutManager";
import { Scroller } from "../../../lib/ui/Scroller";
import { Deque } from "../../../lib/utils/Deque";
import { ListAdapter } from "./ListAdapter";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
const { ccclass, property } = _decorator;
const max_speed = 4;
const log = Logger.getLoger(LOG_LEVEL.STOP);

/**
 * @en
 * layout direction.
 *
 * @zh
 * 布局方向。
 */
enum Orientation {
  /**
   * @en
   * The horizontal direction .
   *
   * @zh
   * 水平方向。
   */
  HORIZONTAL = 0,
  /**
   * @en
   * The vertical direction.
   *
   * @zh
   * 垂直方向。
   */
  VERTICAL = 1,
}
ccenum(Orientation);
@ccclass("AdapterView")
export class AdapterView extends Component {
  private _touchPosList: Deque<number[]> = new Deque();
  private touchPointId: number = -1;
  // private a = 0.0008;
  private v0 = 0;
  // private t0 = 0;
  private _moveed: boolean = false;

  // @property({ serializable: true })
  _orientation: Orientation = Orientation.VERTICAL;

  // @property({ type: Orientation })
  get orientation(): Orientation {
    return this._orientation;
  }
  set orientation(value: Orientation) {
    this._orientation = value;
    // this.layout();
  }
  // 是否正在触摸中
  private isTouching: boolean = false;
  private _scroller: Scroller = new Scroller();
  protected onLoad(): void {
    // 添加触摸事件监听器
    this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this, true);
    this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this, true);
    this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this, true);
    this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this, true);
  }
  start() {
    // this._scroller.init();
  }
  protected onDestroy(): void {
    // this._scroller.deInit();
  }

  update(deltaTime: number) {
    if (this.isTouching) {
      return;
    }
    this._scroller.update();
  }

  private startMove(event: EventTouch) {
    if (this._moveed) {
      return;
    }

    if (event.target === this.node) {
      return;
    }
    let moveX = Math.abs(event.getUILocation().x - event.getUIStartLocation().x);
    let moveY = Math.abs(event.getUILocation().y - event.getUIStartLocation().y);
    let length = Vec2.distance(event.getUILocation(), event.getUIStartLocation());
    // console.log("distance", length, moveX, moveY);
    if (length > 20) {
      if (
        (this.orientation == Orientation.HORIZONTAL && moveX > moveY) ||
        (this.orientation == Orientation.VERTICAL && moveY > moveX)
      ) {
        const cancelEvent = new EventTouch(event.getTouches(), event.bubbles, NodeEventType.TOUCH_CANCEL);
        cancelEvent.touch = event.touch;
        cancelEvent.simulate = true;
        (event.target as Node).dispatchEvent(cancelEvent);
        this._moveed = true;
      }
    }

    // this.content.dispatchEvent(new CancelEvent(event.getAllTouches()));
  }

  private onTouchStart(event: EventTouch) {
    event.bubbles = false;
    if (this.touchPointId > 0) {
      return;
    }
    this.touchPointId = event.getID();
    this.isTouching = true;
    this._moveed = false;
    let currentPos = event.getUILocation();
    this._touchPosList.addRear([currentPos.x, currentPos.y, Date.now()]);
  }
  private onTouchMove(event: EventTouch) {
    log.log("AdapterView onTouchMove", event.getDeltaX(), event.getDeltaY());
    if (!this.isTouching) return;
    if (this.touchPointId != event.getID()) {
      return;
    }

    this.startMove(event);
    if (this._moveed) {
      event.propagationStopped = true;
    }
    const touchPos = event.getUILocation();

    try {
      let startPos = this._touchPosList.peekRear();
      let startX = startPos[0] ?? 0;
      let startY = startPos[1] ?? 0;

      const deltaY = touchPos.y - startY;
      const deltaX = touchPos.x - startX;
      this.offsetChilds(deltaX, deltaY);

      if (this._touchPosList.size() > 3) {
        this._touchPosList.removeFront();
      }
      this._touchPosList.addRear([touchPos.x, touchPos.y, Date.now()]);
    } catch (error) {
      log.error(error);
      log.info(event);
    }
    log.info(this._touchPosList);
  }

  private onTouchEnd(event: EventTouch) {
    log.log("AdapterView onTouchEnd");

    if (this.touchPointId != event.getID()) {
      return;
    }
    this.touchPointId = -1;
    this.isTouching = false;
    let touchPos = event.getUILocation();
    let startPos = this._touchPosList.peekFront();
    let startX = startPos[0];
    let startY = startPos[1];
    let startTime = startPos[2];
    // log.log(Date.now());
    const deltaX = touchPos.x - startX;
    const deltaY = touchPos.y - startY;
    const deltaTime = Date.now() - startTime;
    let lastSpeedY = deltaY / deltaTime;
    let lastSpeedX = deltaX / deltaTime;
    // log.log(`lastSpeedY ${lastSpeedY}`);
    this.v0 = lastSpeedY;
    this._scroller.setFlings(lastSpeedX, lastSpeedY);
    // this.v0 = Math.max(-max_speed, Math.min(lastSpeedY, max_speed));
    // this.t0 = Date.now();
    this._touchPosList.clear();

    if (this.v0 != 0) {
      event.propagationStopped = true;
    }
    this._moveed = false;
    // log.log(`test distance ${this.testOffset}-${this.overBoundaryTop}`);
  }

  private onTouchCancel(event: EventTouch) {
    log.log("AdapterView onTouchCancel");
    if (event && event.simulate) {
      return;
    }
    if (this._touchPosList.size() > 0) {
      this.onTouchEnd(event);
    } else {
      if (this.touchPointId != event.getID()) {
        return;
      }
      this.touchPointId = -1;
      this.isTouching = false;
      this._moveed = false;
    }
  }
  private offsetChilds(offsetX: number, offsetY: number) {
    try {
      this.getComponent(LayoutManager).scrollBy(offsetX, offsetY);
    } catch (error) {
      log.error(error);
    }
  }
  private onDataSetChange(data: any) {
    if (data === "clear") {
      this.getComponent(LayoutManager).removeAllChildren();
    } else {
      this.getComponent(LayoutManager).requestLayout(true);
    }
  }

  public setAdapter(adapter: ListAdapter) {
    if (!this.getComponent(LayoutManager)) {
      throw new Error("must set layoutManager Component first");
    }
    this.getComponent(LayoutManager).attachTo(this.node, this._scroller, adapter);
    adapter.setObserver(this.onDataSetChange.bind(this));
  }
}
