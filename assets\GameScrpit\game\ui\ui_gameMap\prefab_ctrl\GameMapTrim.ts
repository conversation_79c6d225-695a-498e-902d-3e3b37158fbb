import {
  _decorator,
  color,
  instantiate,
  isValid,
  Label,
  Node,
  sp,
  Sprite,
  SpriteFrame,
  tween,
  UIOpacity,
  UITransform,
  Vec3,
} from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { IConfigBuildTrimReward } from "../../../JsonDefine";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { CityModule } from "db://assets/GameScrpit/module/city/CityModule";
import { GameMapTopTrimPop } from "./GameMapTopTrimPop";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { SpineUtil } from "db://assets/platform/src/lib/utils/SpineUtil";
import { Sleep } from "../../../GameDefine";
import { RewardRouteEnum } from "db://assets/GameScrpit/ext_reward/RewardDefine";
import { EventActionModule } from "db://assets/GameScrpit/module/event_action/src/EventActionModule";
import { EventRepelResponse } from "../../../net/protocol/WorldEvent";
import { EventCtrlManager } from "../event_ctrl/EventCtrlManager";
import { DecorationManualReward } from "../../../net/protocol/City";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const { ccclass, property } = _decorator;
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
@ccclass("GameMapTrim")
export class GameMapTrim extends BaseCtrl {
  cfgBuildTrimReward: IConfigBuildTrimReward;

  _cd = 0;

  private _eventId: number;

  init(args: IConfigBuildTrimReward): void {
    this.cfgBuildTrimReward = args;
  }

  protected onLoad(): void {
    this.setQiPao(false);
  }

  protected start(): void {
    super.start();
    MsgMgr.on(MsgEnum.ON_CITY_TRIM_UPDATE, this.show, this);
  }

  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_CITY_TRIM_UPDATE, this.show, this);
    super.onDestroy();
  }

  /**
   * 启用后刷新一下状态
   */
  protected onEnable(): void {
    this.show();
  }

  public async show() {
    // 数据还未准备好
    if (!this.cfgBuildTrimReward) {
      return;
    }

    if (this.cfgBuildTrimReward.hide > 0) {
      this.node.active = false;
      return;
    }

    const nodeSpine = this.node.getChildByName("spine");
    const nodeBgTrim = this.node.getChildByName("bg_trim");
    const nodeBgBubble = this.node.getChildByName("bg_bubble");
    const nodeBtnTip = this.node.getChildByName("btn_tip");
    const btn_build = this.node.getChildByName("btn_build");

    // if (this.cfgBuildTrimReward.pos?.length > 1) {
    //   this.node.setPosition(this.cfgBuildTrimReward.pos[0], this.cfgBuildTrimReward.pos[1]);
    // }

    // 加载设置spine
    const spine = nodeSpine.getComponent(sp.Skeleton);

    // spine偏移
    // if (this.cfgBuildTrimReward.spinePos.length > 1) {
    //   nodeSpine.setPosition(this.cfgBuildTrimReward.spinePos[0], this.cfgBuildTrimReward.spinePos[1]);
    // }

    btn_build.active = false;

    nodeSpine.active = false;
    nodeBgBubble.active = false;
    nodeBgTrim.active = false;
    nodeBtnTip.active = false;

    if (CityModule.data.cityAggregateMessage.decorationIdList.includes(this.cfgBuildTrimReward.id)) {
      nodeSpine.active = true;
      // if (this.cfgBuildTrimReward.scale.length > 1) {
      //   nodeSpine.setScale(this.cfgBuildTrimReward.scale[0], this.cfgBuildTrimReward.scale[1], 1);
      // }

      SpineUtil.playSpine(spine, this.cfgBuildTrimReward.aniName, true);
    } else {
      if (this.cfgBuildTrimReward.bubble === 1) {
        nodeBgBubble.active = true;
        nodeBtnTip.active = true;

        // 设置气泡图片
        if (this.cfgBuildTrimReward.bubbleName) {
          const sf = await this.assetMgr.loadSpriteFrameSync(
            BundleEnum.BUNDLE_G_GAME_MAP,
            `images/${this.cfgBuildTrimReward.bubbleName}`
          );
          if (isValid(this.node)) {
            nodeBgBubble.getComponent(Sprite).spriteFrame = sf;
          } else {
            return;
          }

          // 设置气泡名字
          const lblName = nodeBgBubble.getChildByName("lbl_name").getComponent(Label);
          lblName.string = this.cfgBuildTrimReward.name2;
          lblName.color = color().fromHEX(this.cfgBuildTrimReward.bubbleColor);
        }
      }

      // 设置虚影图片
      if (nodeBgTrim.children.length > 0) {
        // bgTrim有子节点，为新版规则
        nodeBgTrim.active = true;
      } else if (this.cfgBuildTrimReward.xuying) {
        // 旧版规则，如果有动画则播放动画，否则显示图片
        const spine = nodeSpine.getComponent(sp.Skeleton);
        if (spine.findAnimation(this.cfgBuildTrimReward.xuying)) {
          nodeSpine.active = true;
          SpineUtil.playSpine(spine, this.cfgBuildTrimReward.xuying, true);
        } else {
          nodeSpine.active = false;
          const sf = await this.assetMgr.loadSpriteFrameSync(
            BundleEnum.BUNDLE_G_GAME_MAP,
            `images/${this.cfgBuildTrimReward.xuying}`
          );
          if (isValid(this.node)) {
            nodeBgTrim.getComponent(Sprite).spriteFrame = sf;
          } else {
            return;
          }
        }
      }

      // 设置虚影大小
      if (this.cfgBuildTrimReward && this.cfgBuildTrimReward.xuyingBig.length > 1) {
        nodeBgTrim.setScale(this.cfgBuildTrimReward.xuyingBig[0], this.cfgBuildTrimReward.xuyingBig[1], 1);
      }

      if (
        this.cfgBuildTrimReward &&
        CityModule.data.cityAggregateMessage.activeLockIdList.includes(this.cfgBuildTrimReward.id)
      ) {
        // 显示解锁按钮
        btn_build.active = true;
      }
    }
  }

  async on_click_btn_tip() {
    TipsMgr.setEnableTouch(false, 3, false);

    let pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_GAME_MAP, "prefab/trim/GameMapTopTrimPop");
    let node = instantiate(pb);
    node.getComponent(GameMapTopTrimPop).init(this.cfgBuildTrimReward);
    TipsMgr.showTipNode(node);

    TipsMgr.setEnableTouch(true);
  }

  on_click_btn_build() {
    CityModule.api.activeDecoration(this.cfgBuildTrimReward.id, async (data: DecorationManualReward) => {
      // 播放解锁动画
      if (this.cfgBuildTrimReward.musicId) {
        AudioMgr.instance.playEffect(this.cfgBuildTrimReward.musicId);
      }

      TipsMgr.setEnableTouch(false, 7, false);
      this.getNode("btn_build").active = false;
      this.node.getChildByName("bg_trim").active = false;

      const x = this.node.getComponent(UITransform).contentSize.x;
      const wPos = this.node.getWorldPosition();

      if (this.cfgBuildTrimReward.midName) {
        const spine = this.node.getChildByName("spine").getComponent(sp.Skeleton);
        // 播放建建造动画，并等待动画播放完成
        let te = await SpineUtil.playSpine(spine, this.cfgBuildTrimReward.midName, false);
        await Sleep(te.animation.duration);

        TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopLevelUp, {
          width: x,
          wPos: wPos,
          showBuild: false,
        });
        SpineUtil.playSpine(spine, this.cfgBuildTrimReward.aniName, true);
      } else {
        TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopLevelUp, {
          width: x,
          wPos: wPos,
          showBuild: true,
        });
        await Sleep(1.2);
        this.show();
      }

      await Sleep(0.4);
      // 奖励动画
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, data.rewardMessage);
    });
  }

  public setEvent(eventId: number) {
    this._eventId = eventId;
    if (this._eventId == null) {
      this.setQiPao(false);
    } else {
      this.setQiPao(true);
    }
  }

  private setQiPao(bool: boolean) {
    let img_qipao_3 = this.node.getChildByName("img_qipao_3");
    if (img_qipao_3 && bool == true) {
      img_qipao_3.active = true;

      let rewardList = EventActionModule.data.eventTrainMessage.eventMap[this._eventId].rewardList;
      let id = rewardList[0] || 0;

      this.assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/item_${id}`, (sp: SpriteFrame) => {
        if (this.node.isValid == false) {
          return;
        }
        img_qipao_3.getChildByName("item_icon").getComponent(Sprite).spriteFrame = sp;
      });

      tween(img_qipao_3).stop();
      img_qipao_3.angle = 0; // 重置角度
      // 添加左右晃动效果
      tween(img_qipao_3)
        .repeatForever(
          tween()
            .to(0.1, { angle: 10 }, { easing: "sineInOut" })
            .to(0.1, { angle: -10 }, { easing: "sineInOut" })
            .to(0.1, { angle: 0 }, { easing: "sineInOut" })
            .delay(0.8)
        )
        .start();
      this.node.off(Node.EventType.TOUCH_END, this.on_click_event, this);
      this.node.on(Node.EventType.TOUCH_END, this.on_click_event, this);
      img_qipao_3.off(Node.EventType.TOUCH_END, this.on_click_event, this);
      img_qipao_3.on(Node.EventType.TOUCH_END, this.on_click_event, this);
    } else if (img_qipao_3 && bool == false) {
      img_qipao_3.active = false;
      img_qipao_3.getChildByName("event_skt").active = false;
      // 停止所有缓动动画
      tween(img_qipao_3).stop();
      img_qipao_3.angle = 0; // 重置角度
      this.node.off(Node.EventType.TOUCH_END, this.on_click_event, this);
      img_qipao_3.off(Node.EventType.TOUCH_END, this.on_click_event, this);
    }
  }

  private on_click_event() {
    log.log("进行事件完成===", this._eventId);
    AudioMgr.instance.playEffect(523);
    let img_qipao_3 = this.node.getChildByName("img_qipao_3");
    if (img_qipao_3) {
      img_qipao_3.off(Node.EventType.TOUCH_END, this.on_click_event, this);
    }
    this.node.off(Node.EventType.TOUCH_END, this.on_click_event, this);

    const bubbleNode = this.node.getChildByName("img_qipao_3");
    if (!bubbleNode.getComponent(UIOpacity)) {
      bubbleNode.addComponent(UIOpacity);
    }

    // 停止原有晃动动画
    tween(bubbleNode).stop();
    // bubbleNode.active = false;
    // // 重置状态以便下次使用
    // bubbleNode.scale = Vec3.ONE;

    EventActionModule.api.postExpel(this._eventId, async (data: EventRepelResponse) => {
      log.log("收割挂机事件完成", data);
      EventCtrlManager.instance.removeEvent(this._eventId);

      let event_skt = img_qipao_3.getChildByName("event_skt");
      event_skt.active = true;
      event_skt.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        //清空监听
        if ("animation1" == trackEntry.animation.name) {
          if (data.rewardMessage) {
            event_skt.active = false;
            bubbleNode.active = false;
            MsgMgr.emit(MsgEnum.ON_GET_AWARD, {
              itemList: data.rewardMessage.rewardList,
              transformList: data.rewardMessage.transformList,
            });
          }
        }
      });
      event_skt.getComponent(sp.Skeleton).setAnimation(0, "animation1", false);
    });

    // // 爆开动画组合
    // tween(bubbleNode)
    //   .to(0.2, { scale: new Vec3(1.5, 1.5, 1) }, { easing: "sineIn" })
    //   .call(() => {
    //     tween(bubbleNode.getComponent(UIOpacity))
    //       .to(0.2, { opacity: 0 })
    //       .call(() => {
    //         bubbleNode.getComponent(UIOpacity).opacity = 255;
    //         EventActionModule.api.postExpel(this._eventId, async (data: EventRepelResponse) => {
    //           log.log("收割挂机事件完成", data);
    //           EventCtrlManager.instance.removeEvent(this._eventId);
    //           if (data.rewardMessage) {
    //             MsgMgr.emit(MsgEnum.ON_GET_AWARD, {
    //               itemList: data.rewardMessage.rewardList,
    //               transformList: data.rewardMessage.transformList,
    //             });
    //           }
    //         });
    //       })
    //       .start();

    //     bubbleNode.active = false;
    //     // 重置状态以便下次使用
    //     bubbleNode.scale = Vec3.ONE;
    //   })
    //   .start();
  }
}
