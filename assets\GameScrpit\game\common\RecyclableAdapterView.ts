import { _decorator, CCInteger, Component, EventTouch, Mask, Node, UITransform, Widget } from "cc";
import { ListAdapter } from "../../../platform/src/core/ui/adapter_view/ListAdapter";
import { Deque } from "../../../platform/src/lib/utils/Deque";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

const { ccclass, property } = _decorator;

const rebound_time = 200;

const log = Logger.getLoger(LOG_LEVEL.STOP);
export abstract class RecyclableAdapterView extends Component {
  @property({ type: CCInteger })
  private spaceY: number = 0;
  // @property({ type: CCInteger })
  // private spaceY: number;

  // 是否正在触摸中
  private isTouching: boolean = false;

  private a = 0.001;
  // private a = 0.0015;
  private bound_a = 0.05;

  private rebound_a = 0;

  private v0 = 0;
  private t0 = 0;

  private overBoundaryTop: number = 0;
  private overBoundaryBottom: number = 0;

  private adapter: ListAdapter;
  private cacheViews: Map<number, Deque<Node>> = new Map();
  private visibleViews: Deque<Node> = new Deque();
  private firstVisiblePosition: number = 0;
  private lastVisiblePosition: number = 0;
  private _touchPosList: Deque<number[]> = new Deque();

  private _lastItemBottom: number = 0;
  private touchPointId: number = -1;

  testOffset = 0;

  protected start(): void {
    // log.log("==========start=========");
    // log.log(`${this.node.getComponent(UITransform).height}`);
  }

  onLoad() {
    // 添加触摸事件监听器
    this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this, true);
    this.node.on(Node.EventType.TOUCH_MOVE, this.onTouchMove, this, true);
    this.node.on(Node.EventType.TOUCH_END, this.onTouchEnd, this, true);
    this.node.on(Node.EventType.TOUCH_CANCEL, this.onTouchCancel, this, true);
    // this.node.on(Input.EventType.MOUSE_WHEEL, this.onMouseWheel, this);
    this.node.getComponent(Widget)?.updateAlignment();
    if (!this.node.getComponent(Mask)) {
      this.node.addComponent(Mask);
    }
    this.node.getComponent(UITransform)?.setAnchorPoint(0, 1);
  }
  protected update(dt: number): void {
    if (this.isTouching || this.v0 == 0) {
      return;
    }
    let a = this.a;
    if (this.rebound_a != 0) {
      a = this.rebound_a;
      this.overBoundaryTop = 0;
      this.overBoundaryBottom = 0;
    } else if (this.overBoundaryTop > 0 || this.overBoundaryBottom > 0) {
      a = this.bound_a;
    }
    if (this.v0 > 0) {
      let t = Date.now() - this.t0;
      let s = this.v0 * t - 0.5 * a * t * t;
      this.t0 = Date.now();
      this.v0 = this.v0 - a * t;
      log.warn(`t[${t}],s[${s}],v[${this.v0}],a[${a}]`);
      if (this.v0 <= 0) {
        this.v0 = 0;
        if (this.rebound_a != 0) {
          this.rebound_a = 0;
          let front = this.visibleViews.peekFront();
          s = this.getBorderTop() - this.getNodeTop(front);
        } else {
          this.calcRebound();
        }
        log.warn(`test distance ${this.testOffset}-${this.overBoundaryTop}`);
      }
      this.offsetChilds(0, s);
    } else {
      let t = Date.now() - this.t0;
      let s = this.v0 * t + 0.5 * a * t * t;
      this.t0 = Date.now();
      this.v0 = this.v0 + a * t;
      log.warn(`t[${t}],s[${s}],v[${this.v0}],a[${a}]`);
      if (this.v0 >= 0) {
        this.v0 = 0;
        if (this.rebound_a != 0) {
          this.rebound_a = 0;
          let last = this.visibleViews.peekRear();
          let bottom = Math.max(this.getBorderBottom(), this._lastItemBottom);
          log.warn(
            `test distance [${bottom}][${this._lastItemBottom}][${this.getNodeBottom(last)}][${this.getBorderBottom()}]`
          );
          s = bottom - this.getNodeBottom(last);
        } else {
          this.calcRebound();
        }
        log.warn(`test distance ${this.testOffset}-${this.overBoundaryTop}`);
      }
      this.offsetChilds(0, s);
    }
  }

  private onMouseWheel(event: EventTouch) {
    log.log(event);
  }
  private onTouchStart(event: EventTouch) {
    try {
      event.bubbles = false;
      if (this.touchPointId > 0) {
        return;
      }
      this.overBoundaryTop = 0;
      this.overBoundaryBottom = 0;
      this.touchPointId = event.getID();
      this.isTouching = true;
      let currentPos = event.getUILocation();
      this._touchPosList.addRear([currentPos.x, currentPos.y, Date.now()]);
    } catch (error) {
      log.error(error);
    }
  }

  private onTouchMove(event: EventTouch) {
    try {
      if (!this.isTouching) return;
      if (this.touchPointId != event.getID()) {
        return;
      }
      const touchPos = event.getUILocation();

      let startPos = this._touchPosList.peekRear();
      let startX = startPos[0] ?? 0;
      let startY = startPos[1] ?? 0;

      const deltaY = touchPos.y - startY;
      this.offsetChilds(0, deltaY);

      if (this._touchPosList.size() > 3) {
        this._touchPosList.removeFront();
      } else {
      }
      this._touchPosList.addRear([touchPos.x, touchPos.y, Date.now()]);
    } catch (error) {
      log.error(error);
    }
  }

  private onTouchEnd(event: EventTouch) {
    log.log("ListView onTouchEnd");
    try {
      if (this.touchPointId != event.getID()) {
        return;
      }
      this.touchPointId = -1;
      this.isTouching = false;
      let touchPos = event.getUILocation();
      let startPos = this._touchPosList.peekFront();
      let startX = startPos[0];
      let startY = startPos[1];
      let startTime = startPos[2];
      // log.log(Date.now());
      const deltaX = touchPos.x - startX;
      const deltaY = touchPos.y - startY;
      const deltaTime = Date.now() - startTime;
      let lastSpeedY = deltaY / deltaTime;
      log.warn(`lastSpeedY ${lastSpeedY}`);
      // this.v0 = lastSpeedY;
      this.v0 = Math.max(-4, Math.min(lastSpeedY, 4));
      this.t0 = Date.now();
      this._touchPosList.clear();
      if (this.overBoundaryBottom > 0 || this.overBoundaryTop > 0) {
        this.v0 = 0;
        this.calcRebound();
      }
      if (this.v0 != 0) {
        event.propagationStopped = true;
      }
    } catch (error) {
      log.error(error);
    }

    // log.log(`test distance ${this.testOffset}-${this.overBoundaryTop}`);
  }

  private onTouchCancel(event: EventTouch) {
    try {
      if (this.touchPointId != event.getID()) {
        return;
      }
      this.isTouching = false;
      if (this._touchPosList.size() > 0) {
        this.onTouchEnd(event);
      }
    } catch (error) {
      log.error(error);
    }
  }

  protected getNodeLeft(node: Node): number {
    if (!node) {
      return 0;
    }
    let width = node.getComponent(UITransform).width;
    let anchorX = node.getComponent(UITransform).anchorX;
    return node.position.x - width * anchorX;
  }
  protected getNodeTop(node: Node): number {
    if (!node) {
      return 0;
    }
    let height = node.getComponent(UITransform).height;
    let anchorY = node.getComponent(UITransform).anchorY;
    return node.position.y + height * (1 - anchorY);
  }
  protected getNodeBottom(node: Node): number {
    if (!node) {
      return 0;
    }
    let height = node.getComponent(UITransform).height;
    return this.getNodeTop(node) - height;
  }
  protected getBorderLeft() {
    let width = this.node.getComponent(UITransform).width;
    let anchorX = this.node.getComponent(UITransform).anchorX;
    return 0 - width * anchorX;
  }
  protected getBorderTop() {
    let height = this.node.getComponent(UITransform).height;
    let anchorY = this.node.getComponent(UITransform).anchorY;
    return height * (1 - anchorY);
  }
  protected getBorderBottom() {
    let height = this.node.getComponent(UITransform).height;
    let anchorY = this.node.getComponent(UITransform).anchorY;
    return 0 - height * anchorY;
  }

  private setCacheView(viewType: number, view: Node) {
    if (this.cacheViews.has(viewType)) {
      this.cacheViews.get(viewType).addFront(view);
    } else {
      let views = new Deque<Node>();
      views.addFront(view);
      this.cacheViews.set(viewType, views);
    }
  }
  private getCacheView(viewType: number): Node {
    if (this.cacheViews.has(viewType)) {
      return this.cacheViews.get(viewType).removeRear();
    }
    return null;
  }

  private recycleAndUpdateView(offsetY: number) {
    //
    if (this.visibleViews.size() == 0) {
      return;
    }
    let front = this.visibleViews.peekFront();
    let last = this.visibleViews.peekRear();
    if (offsetY > 0) {
      //向上滑动
      //往底部加入一个view
      if (this.lastVisiblePosition == this.adapter.getCount() - 1) {
        //到达边界 需要做边界处理
        if (this._lastItemBottom > this.getBorderBottom()) {
          this.overBoundaryBottom = this.getNodeBottom(last) - this._lastItemBottom;
        } else {
          this.overBoundaryBottom = this.getNodeBottom(last) - this.getBorderBottom();
        }
      } else {
        // log.log(`scroll up[${this.lastVisiblePosition}]`);
        this.overBoundaryBottom = 0;
        this.fillDown(this.lastVisiblePosition + 1, last);
      }
    } else if (offsetY < 0) {
      //向下滑动
      //往头部加入一个view
      if (this.firstVisiblePosition == 0) {
        //到达边界 需要做边界处理
        this.overBoundaryTop = this.getBorderTop() - this.getNodeTop(front);
        // log.log(`${this.getBorderTop()}-${this.getNodeTop(front)}`);
      } else {
        this.overBoundaryTop = 0;
        this.fillUp(this.firstVisiblePosition - 1, front);
      }
    }

    while (this.getNodeBottom(front) > this.getBorderTop()) {
      if (this.lastVisiblePosition - this.firstVisiblePosition < 2) {
        break;
      }
      //回收第一个View
      front = this.visibleViews.removeFront();
      this.setCacheView(this.adapter.getViewType(this.firstVisiblePosition), front);
      this.firstVisiblePosition++;
      this.node.removeChild(front);
      front = this.visibleViews.peekFront();
    }
    while (this.getNodeTop(last) < this.getBorderBottom()) {
      if (this.lastVisiblePosition - this.firstVisiblePosition < 2) {
        break;
      }
      //回收最后一个view
      last = this.visibleViews.removeRear();
      this.setCacheView(this.adapter.getViewType(this.lastVisiblePosition), last);
      this.lastVisiblePosition--;
      this.node.removeChild(last);
      last = this.visibleViews.peekRear();
      // log.log(`recycle-last[${this.lastVisiblePosition}]`);
    }
  }
  private calcRebound() {
    if (this.v0 != 0) {
      this.rebound_a = 0;
      return;
    }
    let front = this.visibleViews.peekFront();
    let last = this.visibleViews.peekRear();
    if (this.firstVisiblePosition == 0) {
      //第二次核验overboundary的值确保不会发生错误
      this.overBoundaryTop = this.getBorderTop() - this.getNodeTop(front);
      log.log(`${this.getBorderTop()}-${this.getNodeTop(front)}`);
    }
    if (this.lastVisiblePosition == this.adapter.getCount() - 1) {
      //到达边界 需要做边界处理
      if (this._lastItemBottom > this.getBorderBottom()) {
        this.overBoundaryBottom = this.getNodeBottom(last) - this._lastItemBottom;
      } else {
        this.overBoundaryBottom = this.getNodeBottom(last) - this.getBorderBottom();
      }
    }
    if (this.overBoundaryTop > 0 && this.firstVisiblePosition == 0) {
      //计算向上滑动速度 offsetY>0
      this.v0 = (this.overBoundaryTop * 2) / rebound_time;
      this.rebound_a = (this.overBoundaryTop * 2) / (rebound_time * rebound_time);
    } else if (this.overBoundaryBottom > 0 && this.lastVisiblePosition == this.adapter.getCount() - 1) {
      //计算向下滑动速度 offsetY<0
      this.v0 = -(this.overBoundaryBottom * 2) / rebound_time;
      this.rebound_a = (this.overBoundaryBottom * 2) / (rebound_time * rebound_time);
    }
    log.log(`rebound speed [${this.rebound_a}]-[${this.v0}]-[${this.overBoundaryTop}]-[${this.overBoundaryBottom}]`);
    this.t0 = Date.now();
    // log.log(`rebound speed [${this.v0}]-[${this.overBoundaryBottom}]`);
  }
  private checkBoundary(offsetY: number): number {
    let finalOffsetY = offsetY;
    if (offsetY > 0) {
      if (this.isTouching && this.overBoundaryBottom > 0 && this.rebound_a == 0) {
        finalOffsetY /= 3; //越界后增加阻尼
      }
      //向上滑动
    } else {
      //向下滑动
      if (this.isTouching && this.overBoundaryTop > 0 && this.rebound_a == 0) {
        finalOffsetY /= 3; //越界后增加阻尼
      }
    }
    return finalOffsetY;
  }
  private makeAndUpdateView(position: number, nextTop: number): Node {
    let viewType = this.adapter.getViewType(position);
    log.log("makeAndUpdateView", viewType);
    let view = this.getCacheView(viewType);
    log.log("makeAndUpdateView", view);
    if (view) {
      this.node.addChild(view);
      return view;
    }
    view = this.adapter.onCreateView(viewType);
    this.node.addChild(view);
    return view;
  }
  //fill data from top to bottom
  private fillFromTop() {
    //
    this.firstVisiblePosition = Math.min(this.firstVisiblePosition, this.adapter.getCount() - 1);
    if (this.firstVisiblePosition < 0) {
      this.firstVisiblePosition = 0;
    }
    this.fillDown(this.firstVisiblePosition, this.visibleViews.peekFront());
    let last = this.visibleViews.peekRear();
    let front = this.visibleViews.peekFront();
    if (this.firstVisiblePosition == 0 && this.getBorderTop() >= this.getNodeTop(front)) {
      this._lastItemBottom = this.getNodeBottom(last);
    }
  }
  //fill data to bottom
  private fillDown(pos: number, lastView: Node) {
    //
    let end = this.getBorderBottom();
    let nextTop = 0;
    if (lastView && pos == 0) {
      nextTop = this.getNodeBottom(lastView);
    } else if (lastView) {
      nextTop = this.getNodeBottom(lastView) - this.spaceY;
    }

    log.log(`1-fillDown [${pos}][${nextTop}][${end}]`);
    while (pos < this.adapter.getCount() && nextTop > end) {
      let addView = this.makeAndUpdateView(pos, nextTop);
      this.adapter.onBindData(addView, pos);
      this.layout(addView, this.getBorderLeft(), nextTop);
      nextTop = this.getNodeBottom(addView) - this.spaceY;
      log.log(pos, nextTop, end);
      pos++;
      this.visibleViews.addRear(addView);
    }
    if (this.visibleViews.size() > 0) {
      this.lastVisiblePosition = this.firstVisiblePosition + this.visibleViews.size() - 1;
    }
    log.log(`2-fillDown [${this.firstVisiblePosition}][${this.lastVisiblePosition}]`);
    log.log(`---${this.visibleViews.size()}---`);
    log.log(this.visibleViews);
  }
  //fill data to top
  private fillUp(pos: number, lastView: Node) {
    //
    let nextBottom = 0;
    if (lastView) {
      nextBottom = this.getNodeTop(lastView) + this.spaceY;
    }
    log.log(`fillUp [${pos}][${nextBottom}]`);
    let end = this.getBorderTop();
    while (pos >= 0 && nextBottom < end) {
      let addView = this.makeAndUpdateView(pos, nextBottom);
      this.adapter.onBindData(addView, pos);
      let top = addView.getComponent(UITransform).height + nextBottom;
      this.layout(addView, this.getBorderLeft(), top);
      nextBottom = this.getNodeTop(addView) + this.spaceY;
      this.visibleViews.addFront(addView);
      pos--;
    }
    this.firstVisiblePosition = Math.max(pos + 1, 0);
    log.log(`fillUp [${this.firstVisiblePosition}][${this.lastVisiblePosition}]`);
  }

  private offsetChilds(offsetX: number, offsetY: number) {
    let finalOffsetY = this.checkBoundary(offsetY);
    if (this.rebound_a != 0 && this.firstVisiblePosition == 0) {
      let front = this.visibleViews.peekFront();
      let deltaS = (this.v0 * this.v0) / (2 * this.rebound_a);
      let height = front.getComponent(UITransform).height;
      let deltaY = this.getNodeTop(front) - this.getBorderTop();
      log.warn(`rebound-deltaS[${deltaS}]-deltaY[${deltaY}]`);
      if (deltaS > height && this.v0 < 0) {
        this.v0 = -(height * 2) / rebound_time;
        this.rebound_a = (height * 2) / (rebound_time * rebound_time);
        let deltaS = (this.v0 * this.v0) / (2 * this.rebound_a);
        log.warn(`rebound-deltaS[${deltaS}]-height[${height}]`);
        this.t0 = Date.now();
        this._lastItemBottom =
          this.getNodeBottom(this.visibleViews.peekRear()) + (this.getBorderTop() - this.getNodeTop(front));
        return;
      }
      if (deltaY > 0 && deltaY < Math.abs(offsetY)) {
        finalOffsetY = -deltaY;
        this.rebound_a = 0;
        this.v0 = 0;
      }
      // this.testOffset += finalOffsetY;
    }
    for (let i = 0; i < this.visibleViews.size(); i++) {
      let view = this.visibleViews.get(i);
      let x = view.position.x + offsetX;
      let y = view.position.y + finalOffsetY;
      view.setPosition(x, y);
    }
    this.recycleAndUpdateView(offsetY);
  }
  private dataChange(data: any): void {
    if (data === "DATAONLY") {
      for (let i = 0; i < this.visibleViews.size(); i++) {
        let view = this.visibleViews.get(i);
        this.adapter.onBindData(view, this.firstVisiblePosition + i);
      }
    } else if (data === "clear") {
      while (this.visibleViews.size() > 0) {
        let removeView = this.visibleViews.removeRear();
        this.node.removeChild(removeView);
        if (this.firstVisiblePosition < this.lastVisiblePosition) {
          //第0个View不会被缓存,但是会被从节点上删除
          //TODO:需要测试viewType的正确性
          this.setCacheView(this.adapter.getViewType(this.lastVisiblePosition--), removeView);
        }
      }
      this.firstVisiblePosition = this.lastVisiblePosition = 0;
    } else {
      log.log(`dataChange orignal view size [${this.visibleViews.size()}]`);
      log.log(this.visibleViews);
      this.v0 = 0;
      this.rebound_a = 0;
      while (this.visibleViews.size() > 0) {
        let removeView = this.visibleViews.removeRear();
        this.node.removeChild(removeView);
        if (this.firstVisiblePosition < this.lastVisiblePosition) {
          //第0个View不会被缓存,但是会被从节点上删除
          //TODO:需要测试viewType的正确性
          this.setCacheView(this.adapter.getViewType(this.lastVisiblePosition--), removeView);
        }
      }
      if (this.adapter.getCount() <= 0) {
        return;
      }
      log.log(`dataChange new view size [${this.visibleViews.size()}]`);
      log.log(this.visibleViews);
      // this.visibleViews.clear();
      if (this.firstVisiblePosition >= this.adapter.getCount()) {
        this.firstVisiblePosition = this.adapter.getCount() - 1;
        // this._lastItemBottom = 0;
      }
      log.log(
        `dataChange fillbeforce first[${this.firstVisiblePosition}] last[${
          this.lastVisiblePosition
        }] size[${this.visibleViews.size()}]`
      );
      this.fillFromTop();
      this.recycleAndUpdateView(0);
      this.calcRebound();
      log.log(`dataChange fillafter first[${this.firstVisiblePosition}] last[${this.lastVisiblePosition}]`);
    }
    //TODO:
  }

  public setAdapter(adapter: ListAdapter) {
    this.adapter = adapter;
    this.adapter.setObserver(this.dataChange.bind(this));
  }
  abstract layout(node: Node, left: number, top: number);
}
