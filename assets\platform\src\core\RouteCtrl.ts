import {
  _decorator,
  assetManager,
  BlockInputEvents,
  Component,
  EventTouch,
  math,
  Node,
  NodeEventType,
  Sprite,
  tween,
  UIOpacity,
  UITransform,
} from "cc";
import { AssetMgr, BundleEnum } from "../ResHelper";
import { instantiate } from "cc";
import { Prefab } from "cc";
import { BaseCtrl, MSG_BASE_CTRL_CLOSE, MSG_BASE_CTRL_READY } from "./BaseCtrl";
import { isValid } from "cc";
import { GameData } from "../../../GameScrpit/game/GameData";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import ResMgr from "../../../GameScrpit/lib/common/ResMgr";
import { PageEdge, RouteConfig, RouteShowArgs, RouteTableManager } from "./managers/RouteTableManager";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

// 资源自动释放时间间隔：秒
const assetAutoReleaseCd = 7;

// 新增参数类定义（建议放在文件顶部或路由相关类型定义附近）
interface RouteStackItem {
  config: RouteConfig;
  args: RouteShowArgs;
}

@ccclass("RouteCtrl")
export class RouteCtrl extends Component {
  // 节点低层级
  @property(Node)
  public nodeLowLayer: Node = null; // 节点默认层级

  @property(Node)
  public nodeDefaultLayer: Node = null;

  // 节点中层级
  @property(Node)
  public nodeHighLayer: Node = null;

  private _assetMgr: AssetMgr;

  private _autoReleaseInterval: number = 0;

  // 可用路由表
  public routeMap: Map<string, RouteConfig> = new Map();

  // 路由栈
  public routeHistory: RouteStackItem[] = [];

  public onRouteUpdateFunction: Function;

  // 预制体手工引用计数
  private _prerabUsedMap: Map<string, Node[]> = new Map();

  protected onLoad(): void {
    this._assetMgr = AssetMgr.create();
    if (!this.nodeDefaultLayer) {
      this.nodeDefaultLayer = this.node;
    }
  }

  protected onEnable(): void {
    this.node.on(MSG_BASE_CTRL_READY, this.updateRouteFocus, this);
  }

  protected onDisable(): void {
    this.node.off(MSG_BASE_CTRL_READY, this.updateRouteFocus, this);
  }

  protected onDestroy(): void {
    this._assetMgr.release();
  }

  update(deltaTime: number) {
    // 自动释放资源
    this._autoReleaseInterval += deltaTime;
    if (this._autoReleaseInterval > assetAutoReleaseCd) {
      this._autoReleaseInterval = 0;
      this.releaseCachePrefab();
    }
  }

  // 注册路由
  public regRoute(key: string, routeConfig: RouteConfig) {
    this.routeMap.set(key, routeConfig);
  }

  // 获取当前页面到目标页面的路径
  public getRoutePath(desPage: string, desPageTag?: string): PageEdge[] {
    let index = this.routeHistory.length - 1;
    let backPath = [];
    let path = [];
    for (; index >= 0; index--) {
      path = RouteTableManager.instance.findPath(
        this.routeHistory[index].config.url,
        desPage,
        this.routeHistory[index].args.pageTag,
        desPageTag
      );
      if (path.length > 0) {
        path = [...backPath, ...path];
        break;
      }
      let pageEdge: PageEdge = {
        via: this.routeHistory[index].config.exit,
        pageTag: null,
        des: null,
        src: null,
      };
      backPath.push(pageEdge);
    }
    return path;
  }

  // 传输type类型
  public showRoute<T>(type: new () => T, args: RouteShowArgs = {}) {
    let routeConfig = RouteTableManager.instance.getRouteConfig(type);
    if (!routeConfig) {
      log.error(`路由不存在：${type.name}`);
      return;
    }
    // 单例节点不重复显示
    if (routeConfig.isSingleton) {
      let findRoute = this.routeHistory.find(
        (e) => e.config.url == routeConfig.url && e.config.bundle == routeConfig.bundle
      );
      if (!findRoute) {
        findRoute = GameData.instance[routeConfig.url];
      }

      if (findRoute?.config?.baseCtrl) {
        findRoute.config.baseCtrl.onCloseBack = args?.onCloseBack || findRoute.config.baseCtrl.onCloseBack;
        findRoute.config.baseCtrl.init(args);
        return;
      }
    }

    this.showPrefab(routeConfig.bundle, routeConfig.url, args, args?.onCloseBack, args?.onUIReady, routeConfig);
  }

  // 显示路由节点
  public show(routeName: string, args: any = {}, onCloseBack?: Function, onUIReady?: Function) {
    let routeConfig: RouteConfig = this.routeMap.get(routeName);
    if (!routeConfig) {
      log.error(`路由未注册：${routeName}`);
      return;
    }
    routeConfig.transparent = true;

    // 单例节点不重复显示
    if (routeConfig.isSingleton) {
      let findRoute = this.routeHistory.find(
        (e) => e.config.url == routeConfig.url && e.config.bundle == routeConfig.bundle
      );
      if (!findRoute) {
        findRoute = GameData.instance[routeConfig.url];
      }

      if (findRoute?.config?.baseCtrl) {
        findRoute.config.baseCtrl.onCloseBack = onCloseBack || findRoute.config.baseCtrl.onCloseBack;
        findRoute.config.baseCtrl.init(args);
        return;
      }
    }

    this.showPrefab(routeConfig.bundle, routeConfig.url, args, onCloseBack, onUIReady, routeConfig);
  }

  // 显示预制体
  public showPrefab(
    bundleName: string,
    prefabUrl: string,
    args: any,
    onCloseBack?: Function,
    onUIReady?: Function,
    routeConfig?: RouteConfig
  ) {
    let routeConfigCopy: RouteConfig = {
      bundle: bundleName,
      url: prefabUrl,
      isSingleton: routeConfig?.isSingleton,
      parentNode: routeConfig?.parentNode,
      nextHop: [],
      exit: routeConfig?.exit,
    };

    if (routeConfig?.isSingleton) {
      let RouteStack: RouteStackItem = {
        config: routeConfigCopy,
        args: args,
      };
      GameData.instance[routeConfig.url] = RouteStack;
    }
    let rootNode = null;
    if (routeConfig && !routeConfig.transparent) {
      let rootName = prefabUrl.split("/").pop();
      rootNode = new Node("ROUTE_" + rootName);
      rootNode.parent = this.node;
      rootNode.layer = this.node.layer;
      rootNode.addComponent(UITransform);
      rootNode.getComponent(UITransform).setContentSize(this.node.getComponent(UITransform).contentSize);

      let dialogMask = new Node("ROUTE_" + rootName + "_MASK");
      dialogMask.addComponent(UITransform);
      dialogMask.getComponent(UITransform).setContentSize(this.node.getComponent(UITransform).contentSize);

      rootNode.addChild(dialogMask);
      dialogMask.layer = this.node.layer;
      dialogMask.addComponent(Sprite);
      dialogMask.getComponent(Sprite).sizeMode = Sprite.SizeMode.CUSTOM;
      dialogMask.getComponent(Sprite).type = Sprite.Type.SIMPLE;
      dialogMask.getComponent(Sprite).color = math.color(0, 0, 0, 191);
      dialogMask.addComponent(UIOpacity);
      dialogMask.getComponent(UIOpacity).opacity = 0;

      rootNode.addComponent(BlockInputEvents);

      ///Users/<USER>/Project/CocosWork/fm-client/assets/bundle_common_ui/atlas_imgs/default_sprite_splash.png
      ResMgr.setSpriteFrame(
        BundleEnum.BUNDLE_COMMON_UI,
        "atlas_imgs/default_sprite_splash",
        dialogMask.getComponent(Sprite),
        () => {
          tween(dialogMask.getComponent(UIOpacity)).to(0.05, { opacity: 255 }).start();
        }
      );
      ResMgr.setNodePrefab("platform_res", "prefabs/RouteMask", dialogMask);
      rootNode.once(MSG_BASE_CTRL_CLOSE, (e: EventTouch) => {
        log.log("ROUTE_CLOSE", e);
        tween(dialogMask.getComponent(UIOpacity)).to(0.05, { opacity: 0 }).start();
      });
      rootNode.on(NodeEventType.TOUCH_END, (event: EventTouch) => {
        if (event.target == rootNode) {
          rootNode.destroy();
        }
      });

      routeConfigCopy.parentNode = rootNode;
    }

    this._assetMgr.loadPrefab(bundleName, prefabUrl, (prefab: Prefab) => {
      // 如果所在节点不存在说明已经被释放，则不显示
      if (!this.node) {
        GameData.instance[routeConfig.url] = null;
        return;
      }

      routeConfigCopy.node = instantiate(prefab);
      routeConfigCopy.baseCtrl = routeConfigCopy.node.getComponent(BaseCtrl);

      let RouteStack: RouteStackItem = {
        config: routeConfigCopy,
        args: args,
      };
      this.routeHistory.push(RouteStack);

      // 用于本节点的自动释放
      this.cachePrefab(routeConfigCopy.bundle, routeConfigCopy.url, routeConfigCopy.node);

      this.showNode(routeConfigCopy.node, args, onCloseBack, onUIReady, routeConfigCopy.parentNode, rootNode);
    });
  }

  // 显示节点
  public showNode(
    nodeShow: Node,
    args: any = {},
    onCloseBack?: Function,
    onUIReady?: Function,
    nodeParent?: Node,
    rootNode: Node = null
  ) {
    const baseCtrl = nodeShow.getComponent(BaseCtrl);
    if (baseCtrl) {
      baseCtrl.nodeRoute = this.node;
      baseCtrl.onCloseBack = onCloseBack || baseCtrl.onCloseBack;
      baseCtrl.onUIReady = onUIReady;
      baseCtrl.init(args);
    }
    if (isValid(nodeParent)) {
      nodeParent.addChild(nodeShow);
      if (isValid(rootNode)) {
        nodeShow.once(NodeEventType.NODE_DESTROYED, () => {
          rootNode.destroy();
        });
      }
    } else {
      this.nodeDefaultLayer.addChild(nodeShow);
    }
    nodeShow.once(NodeEventType.NODE_DESTROYED, (e: EventTouch) => {
      log.log("NODE_DESTROYED", e);
      if (isValid(rootNode)) {
        rootNode.destroy();
      }
      this.onNodeCloseBack(nodeShow.uuid);
    });
  }

  public back() {
    let routeLast = this.routeHistory.pop();
    if (routeLast.config.node) {
      return routeLast.config.node.destroy();
    }
  }

  public closeByName(routeName: string) {
    let routeConfig: RouteConfig = this.routeMap.get(routeName);
    let routeInfo = this.routeHistory.find(
      (e) => e.config.url == routeConfig.url && e.config.bundle == routeConfig.bundle
    );
    if (routeInfo?.config.baseCtrl) {
      routeInfo.config.baseCtrl.closeBack();
    }
  }

  // 更新路由节点的焦点状态
  private updateRouteFocus() {
    this.routeHistory.forEach((routeConfig, index) => {
      if (routeConfig.config.baseCtrl) {
        if (index == this.routeHistory.length - 1) {
          routeConfig.config.baseCtrl.focus = true;
        } else {
          routeConfig.config.baseCtrl.focus = false;
        }
      }
    });
  }

  // 节点关闭回调
  public onNodeCloseBack(uuid: string) {
    let routeConfig = this.routeHistory.find((e) => e.config.node?.uuid == uuid);
    if (routeConfig) {
      this.routeHistory.splice(this.routeHistory.indexOf(routeConfig), 1);
      if (routeConfig.config.isSingleton) {
        GameData.instance[routeConfig.config.url] = null;
      }
      this.updateRouteFocus();
    }
  }

  // 缓存计数,依赖cocoscreator的isValid来释放资源
  public cachePrefab(bundle: string, url: string, node: Node) {
    let nodeList = this._prerabUsedMap.get(bundle + "?" + url);
    if (!nodeList) {
      nodeList = [node];
      this._prerabUsedMap.set(bundle + "?" + url, nodeList);
    } else {
      nodeList.push(node);
    }
  }

  // 定时调用-释放引用资源
  public releaseCachePrefab() {
    this._prerabUsedMap.forEach((nodeList, key) => {
      let hasValid = false;

      nodeList = nodeList.filter((node) => {
        let rs = isValid(node);
        if (!rs) {
          hasValid = true;
        }
        return rs;
      });

      if (nodeList.length == 0) {
        let splitKey = key.split("?");
        this._assetMgr.releaseOne(splitKey[0], splitKey[1]);
        this._prerabUsedMap.delete(key);
      }

      if (hasValid) {
        this._prerabUsedMap.set(key, nodeList);
      }
    });
  }

  // 移除所有路由节点
  public removeAllRouteNode() {
    log.log("removeAllRouteNode  removeAllRouteNode  removeAllRouteNode");
    this.routeHistory.forEach((routeConfig) => {
      if (routeConfig.config.node) {
        routeConfig.config.node.destroy();
      }
    });
    this.routeHistory = [];
  }
}
