import { _decorator, Component, Node, sp, director, Label } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { PlayerModule } from "../module/player/PlayerModule";
import { AudioMgr } from "../../platform/src/AudioHelper";
import { SpineUtil } from "../../platform/src/lib/utils/SpineUtil";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

@ccclass("TopMovie")
export class TopMovie extends BaseCtrl {
  @property(Node)
  skt_layer: Node = null;

  @property(Node)
  btn_skip: Node = null;

  private spineNodes: Node[] = [];
  private currentIndex = 0;
  private isPlaying = false;
  private skipLabel: Label;
  private nextLabel: Label;

  start() {
    AudioMgr.instance.enableMusic(false);
    // 获取 skt_layer 下的所有节点
    this.spineNodes = this.skt_layer.children;

    // 隐藏所有 Spine 节点
    this.spineNodes.forEach((node) => {
      node.active = false;
    });

    // 获取按钮下的文本组件
    this.skipLabel = this.btn_skip.getChildByName("skipText")?.getComponent(Label);
    this.nextLabel = this.btn_skip.getChildByName("nextText")?.getComponent(Label);
    this.updateButtonText();

    // 绑定按钮点击事件
    this.btn_skip.on(Node.EventType.TOUCH_END, this.onBtnSkip, this);

    // 开始播放第一个 Spine 动画
    this.playNextSpine();
  }

  private updateButtonText() {
    if (this.isPlaying) {
      if (this.skipLabel) this.skipLabel.node.active = true;
      if (this.nextLabel) this.nextLabel.node.active = false;
    } else {
      if (this.skipLabel) this.skipLabel.node.active = false;
      if (this.nextLabel) this.nextLabel.node.active = true;
    }
  }

  private playNextSpine() {
    if (this.currentIndex >= this.spineNodes.length) {
      // 所有动画播放完成，可添加跳转场景等逻辑
      log.log("所有 Spine 动画播放完成");
      // 示例：跳转到加载场景
      director.loadScene("SceneLoading", () => {
        PlayerModule.data.onPlayerAttrUpdate();
      });
      return;
    }

    // 隐藏所有 Spine 节点
    this.spineNodes.forEach((node) => {
      node.active = false;
    });

    const currentNode = this.spineNodes[this.currentIndex];
    const spineComponent = currentNode.getComponent(sp.Skeleton);
    const aniName = currentNode.name;

    // 默认显示跳过按钮
    if (this.btn_skip) {
      this.btn_skip.active = true;
    }

    if (spineComponent) {
      // 新增 plot_animation_02 的特殊处理
      if (aniName === "plot_animation_02") {
        // 激活当前节点
        currentNode.active = true;
        this.isPlaying = true;
        this.updateButtonText();

        // 设置动画事件监听
        spineComponent.setEventListener((_entry, event) => {
          if (event["data"].name === "skip") {
            // 暂停动画播放
            spineComponent.timeScale = 0;
            // 显示跳过按钮
            this.btn_skip.active = true;
            this.isPlaying = false;
            this.updateButtonText();
            spineComponent.setEventListener(null);
          }
        });

        // 播放动画
        SpineUtil.playSpine(spineComponent, aniName, false);
        AudioMgr.instance.playMusic(1821, null, 0.8, false, false);
        // 设置动画完成监听
        spineComponent.setCompleteListener((trackEntry) => {
          if (trackEntry.animation?.name === aniName) {
            this.currentIndex++;
            this.playNextSpine();
            spineComponent.setCompleteListener(null);
          }
        });
        return;
      }

      if (aniName === "plot_animation_04") {
        // 隐藏跳过按钮
        if (this.btn_skip) {
          this.btn_skip.active = false;
        }

        // 激活当前节点
        currentNode.active = true;
        this.isPlaying = true;
        this.updateButtonText();

        // 播放循环动画 daji_daiji
        SpineUtil.playSpine(spineComponent, "daji_daiji", true);
        AudioMgr.instance.playMusic(1823);

        // 修改查找路径，从 currentNode 中查找 click_daiji 节点
        const clickDaijiNode = currentNode.getChildByName("click_daiji");

        if (clickDaijiNode) {
          const clickHandler = () => {
            // 清理之前的完成监听器
            spineComponent.setCompleteListener(null);

            // 直接使用 setAnimation 方法播放动画
            spineComponent.setAnimation(0, "dianji_fuhuo", false);

            // 注销点击事件
            clickDaijiNode.off(Node.EventType.TOUCH_END, clickHandler, this);

            // 重新注册完成监听器
            spineComponent.setCompleteListener((trackEntry) => {
              if (trackEntry.animation?.name === "dianji_fuhuo") {
                // 确保是 dianji_fuhuo 动画播放完成
                spineComponent.setCompleteListener(null);

                // 隐藏当前节点
                currentNode.active = false;

                // 增加索引
                this.currentIndex++;
                AudioMgr.instance.disableMusic();
                AudioMgr.instance.enableMusic();
                // 播放下一个动画
                this.playNextSpine();
              }
            });
          };

          clickDaijiNode.on(Node.EventType.TOUCH_END, clickHandler, this);
        } else {
          log.error("未找到 click_daiji 节点");
        }
        return;
      }

      // 非特殊节点的原有逻辑
      // 激活当前节点
      currentNode.active = true;
      this.isPlaying = true;
      this.updateButtonText();

      // 清除之前的完成监听器
      spineComponent.setCompleteListener(null);

      // 假设动画名称和节点名称相同，可根据实际情况修改
      SpineUtil.playSpine(spineComponent, aniName, false);
      switch (currentNode.name) {
        case "plot_animation_01":
          AudioMgr.instance.playMusic(1820, null, 0.8, false, true);
          break;

        case "plot_animation_05":
          spineComponent.setEventListener((_entry, event) => {
            if (event["data"].name === "audio") {
              // 暂停动画播放
              AudioMgr.instance.playMusic(1824, null, 0.8, false, false);
              spineComponent.setEventListener(null);
            }
          });
          break;

        case "plot_animation_03":
          AudioMgr.instance.playMusic(1822, null, 0.8, false, false);
          break;
        default:
          break;
      }

      // 使用 setCompleteListener 监听动画完成
      spineComponent.setCompleteListener((trackEntry) => {
        if (aniName === trackEntry.animation?.name) {
          this.isPlaying = false;
          this.updateButtonText();
          // 移除自动播放下一个动画的逻辑
        }
      });
    } else {
      log.error(`节点 ${currentNode.name} 上未找到 Spine 组件`);
      this.currentIndex++;
      this.playNextSpine();
    }
  }

  private onBtnSkip() {
    if (this.currentIndex >= this.spineNodes.length) {
      return;
    }

    const currentNode = this.spineNodes[this.currentIndex];
    const spineComponent = currentNode.getComponent(sp.Skeleton);
    const aniName = currentNode.name;

    // 新增 plot_animation_02 恢复逻辑
    if (aniName === "plot_animation_02" && !this.isPlaying) {
      // 恢复动画播放
      spineComponent.timeScale = 1;
      this.isPlaying = true;
      this.updateButtonText();

      return;
    }

    if (aniName === "plot_animation_04" && !this.isPlaying) {
      // 处理 plot_animation_04 动画播放完成后的点击逻辑
      currentNode.active = false;
      this.currentIndex++;
      this.playNextSpine();
      return;
    }

    if (this.isPlaying && spineComponent) {
      // 如果在播放中点击，让动画直接进入最后一帧
      SpineUtil.playToLastFrame(spineComponent, aniName);
      this.isPlaying = false;
      this.updateButtonText();
      return;
    }

    // 动画不在播放状态，点击进入下一个动画
    currentNode.active = false;
    if (aniName === "plot_animation_05" && this.currentIndex === this.spineNodes.length - 1) {
      this.changeScene();
      return;
    }

    this.currentIndex++;
    this.playNextSpine();
  }

  private changeScene() {
    AudioMgr.instance.enableEffect();
    AudioMgr.instance.enableMusic();
    // 关闭界面
    director.loadScene("SceneLoading", () => {
      PlayerModule.data.onPlayerAttrUpdate();
    });
  }
}
