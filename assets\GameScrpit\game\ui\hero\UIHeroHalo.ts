import { _decorator, EventTouch, instantiate, Label, Node, RichText, Sprite } from "cc";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { HeroModule } from "../../../module/hero/HeroModule";
import MsgMgr from "../../../lib/event/MsgMgr";
import { HeroEvent } from "../../../module/hero/HeroConstant";
import { ItemCost } from "../../common/ItemCost";
import { divide } from "../../../lib/utils/NumbersUtils";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import ResMgr from "../../../lib/common/ResMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
const { ccclass, property } = _decorator;

@ccclass("UIHeroHalo")
export class UIHeroHalo extends BaseCtrl {
  @property(Node)
  private lblNone: Node;
  @property(Node)
  private nodeHaloContent: Node;
  @property(Label)
  private lblHaloName: Label;
  @property(ItemCost)
  private itemCost: ItemCost;
  @property(Node)
  private btnHaloUpgrade: Node;
  @property(RichText)
  private lblHaloAdd: RichText;
  @property(Node)
  private nodeHaloViewholder: Node;
  @property(Node)
  private bgYimanji: Node;

  private _index: number = 0;
  start() {
    MsgMgr.on(HeroEvent.HERO_VIEWMODEL, this.onViewModelChange, this);
    this.onHeroHaloRefresh();
  }

  update(deltaTime: number) {}

  protected onDestroy(): void {
    super.onDestroy();
    MsgMgr.off(HeroEvent.HERO_VIEWMODEL, this.onViewModelChange, this);
  }
  private onViewModelChange(data: any) {
    //
    this.onHeroHaloRefresh();
  }
  private refreshItem(index: number) {
    let item = this.nodeHaloContent.children[index];
    if (index == this._index) {
      item.getChildByName("select_background").active = true;
      this.refreshItemExtends();
    } else {
      item.getChildByName("select_background").active = false;
    }
    let heroInfo = HeroModule.config.getHeroInfo(HeroModule.viewModel.currentHero.id);
    let lightId = heroInfo.lightList[index];
    let haloLevel = HeroModule.data.getHeroHaloLv(HeroModule.viewModel.currentHero.id, lightId);
    if (haloLevel > 0) {
      item.getChildByPath("item_skill/head_mask/lock").active = false;
    } else {
      item.getChildByPath("item_skill/head_mask/lock").active = true;
    }
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_G_HERO,
      `atlas_hero_skill/heroskill_${lightId}`,
      item.getChildByPath("item_skill/head_mask/head").getComponent(Sprite)
    );
  }
  private refreshItemExtends() {
    let heroInfo = HeroModule.config.getHeroInfo(HeroModule.viewModel.currentHero.id);
    let lightId = heroInfo.lightList[this._index];
    let skill = HeroModule.config.getHeroSkillData(lightId);
    let haloLevel = HeroModule.data.getHeroHaloLv(HeroModule.viewModel.currentHero.id, lightId);
    let haloShowLv = haloLevel;
    if (haloLevel < 1) {
      haloShowLv = 1;
    }
    this.lblHaloName.string = `${skill.name}${haloShowLv}级`;
    this.bgYimanji.active = false;
    this.itemCost.node.active = false;
    this.btnHaloUpgrade.active = false;
    if (HeroModule.config.getHeroHaloLvInfo(lightId, haloShowLv)?.isOrNot == 1) {
      this.lblHaloAdd.maxWidth = 600;
      let haloInfo = HeroModule.config.getHeroHaloLvInfo(lightId, haloShowLv);
      let haloInfo_lv1 = HeroModule.config.getHeroHaloLvInfo(lightId, 1);
      this.lblHaloAdd.string = skill.des
        .replace("%s", `${divide(haloInfo.powerAdd2, 100)}`)
        .replace("%s", `${divide(haloInfo.powerAdd2, 100)}`)
        .replace("%s", `${divide(haloInfo.powerAdd2, 100)}`)
        .replace("%s", `${divide(haloInfo_lv1.powerAdd2, 100)}`)
        .replace("%s", `${divide(haloInfo_lv1.powerAdd2, 100)}`)
        .replace("%s", `${divide(haloInfo_lv1.powerAdd2, 100)}`);
      if (haloLevel >= skill.maxLv) {
        this.bgYimanji.active = true;
      }
    } else {
      let haloInfo = HeroModule.config.getHeroHaloLvInfo(lightId, haloShowLv);
      let str = "";
      if (haloLevel == 0) {
        this.lblHaloAdd.maxWidth = 600;
        if (HeroModule.config.getHeroSkillData(skill.breakList[1])?.name) {
          str = `<color=#ff8f3c>（${HeroModule.config.getHeroSkillData(skill.breakList[1]).name}满级激活）</color>`;
        }
      } else if (haloShowLv >= skill.maxLv) {
        this.bgYimanji.active = true;
        this.lblHaloAdd.maxWidth = 431;
      } else {
        this.itemCost.node.active = true;
        this.btnHaloUpgrade.active = true;
        this.lblHaloAdd.maxWidth = 431;
        let nextHaloInfo = HeroModule.config.getHeroHaloLvInfo(lightId, haloShowLv + 1);
        if (nextHaloInfo) {
          this.itemCost.setItemId(nextHaloInfo.costList[0], nextHaloInfo.costList[1]);
        }
      }

      let powerAdd = 0;
      if (haloInfo.id >= 21030001 && haloInfo.id <= 21030010) {
        powerAdd = divide(haloInfo?.heroPowerAdd1 ?? 0, 100);
      } else if (haloInfo.id >= 22030001 && haloInfo.id <= 22030010) {
        powerAdd = divide(haloInfo?.heroPowerAdd2 ?? 0, 100);
      } else if (haloInfo.id >= 23020001 && haloInfo.id <= 23020010) {
        powerAdd = divide(haloInfo?.heroPowerAdd3 ?? 0, 100);
      } else {
        powerAdd = divide(haloInfo?.powerAdd2 ?? 0, 100);
      }
      this.lblHaloAdd.string =
        skill.des.replace("%s", `${powerAdd}`).replace("%s", `${powerAdd}`).replace("%s", `${powerAdd}`) + str;
    }
  }
  private onItemClick(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let target: Node = e.target;
    let lastIndex = this._index;
    this._index = target.getSiblingIndex();
    this.refreshItem(lastIndex);
    this.refreshItem(this._index);
  }
  private onHeroHaloRefresh() {
    let heroInfo = HeroModule.config.getHeroInfo(HeroModule.viewModel.currentHero.id);
    if (heroInfo.lightList.length <= 0) {
      this.node.children.forEach((node) => (node.active = false));
      this.lblNone.active = true;
    } else {
      this.node.children.forEach((node) => (node.active = true));
      this.lblNone.active = false;
    }
    let index = 0;
    for (; index < heroInfo.lightList.length; index++) {
      if (!this.nodeHaloContent.children[index]) {
        let item = instantiate(this.nodeHaloViewholder);
        this.nodeHaloContent.addChild(item);
        item.on(Node.EventType.TOUCH_END, this.onItemClick, this);
      }
      this.nodeHaloContent.children[index].active = true;
      this.refreshItem(index);
    }
    while (index < this.nodeHaloContent.children.length) {
      this.nodeHaloContent.children[index++].active = false;
    }
  }
  //绑定事件
  private onHeroHaloUpgrade() {
    let heroInfo = HeroModule.config.getHeroInfo(HeroModule.viewModel.currentHero.id);
    let lightId = heroInfo.lightList[this._index];
    let haloLevel = HeroModule.data.getHeroHaloLv(HeroModule.viewModel.currentHero.id, lightId);
    let haloInfo = HeroModule.config.getHeroHaloLvInfo(lightId, haloLevel + 1);
    if (!this.itemCost.isEnough()) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, { itemId: haloInfo.costList[0] });
      return;
    }
    // this.upEffect.active = true;
    // this.upEffect.getComponent(sp.Skeleton).setAnimation(0, "animation", false);
    HeroModule.api.skillLevelUp(heroInfo.id, lightId, this.skillUpResponse.bind(this));
  }
  private skillUpResponse(data: any) {
    // this.updateData(this.heroId, this.skillInfo.id);
    this.onHeroHaloRefresh();
  }
}
