import { _decorator, isValid, Sprite, tween, sp, UIOpacity, v3 } from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import { Sleep } from "../../GameDefine";

const { ccclass, property } = _decorator;

@ccclass("TopCityComponentReward")
export class TopCityComponentReward extends BaseCtrl {
  private _trimId: number;

  init(args: any): void {
    super.init(args);
    this._trimId = args.trimId;
  }

  protected async start(): Promise<void> {
    super.start();

    TipsMgr.setEnableTouch(false, 0.3);

    const nodeHint = this.getNode("node_hint");
    nodeHint.active = false;

    const spine = this.getNode("spine_T_天降大任").getComponent(sp.Skeleton);

    SpineUtil.playOneByOne(spine, "enter", "loop");

    tween(nodeHint)
      .delay(0.6)
      .call(() => {
        nodeHint.active = true;
      })
      .start();

    const nodeSrzs = this.getNode("node_frzs");

    tween(nodeSrzs.getComponent(UIOpacity)).set({ opacity: 0 }).delay(0.3).to(0.3, { opacity: 255 }).start();

    tween(this.getNode("bg_zi_tianjiangdaren"))
      .set({ scale: v3(0, 0, 1) })
      .delay(0.3)
      .to(0.3, { scale: v3(1, 1, 1) })
      .start();

    const sf = await this.assetMgr.loadSpriteFrameSync(
      BundleEnum.BUNDLE_G_GAME_MAP,
      `image_trim_icon/icon_frzs_${this._trimId}`
    );
    if (isValid(this.node)) {
      nodeSrzs.getComponent(Sprite).spriteFrame = sf;
    }
  }

  private async on_click_btn_close() {
    this.closeBack();
    await Sleep(0.3);
    MsgMgr.emit(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, `node_trim_${this._trimId}`);
  }
}
