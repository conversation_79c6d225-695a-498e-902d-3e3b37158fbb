import { _decorator, Node, sp, tween, v3 } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { SpineUtil } from "../../platform/src/lib/utils/SpineUtil";
import { Sleep } from "../game/GameDefine";
import { GuideRouteEnum } from "./GuideDefine";
import { AudioMgr } from "../../platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("TopEventHeroCome")
export class TopEventHeroCome extends BaseCtrl {
  spineQianjidaozhang: sp.Skeleton;
  nodeWord: Node;

  async start() {
    super.start();

    TipsMgr.setEnableTouch(false, 2, false);
    this.spineQianjidaozhang = this.getNode("spine_qianjidaozhang").getComponent(sp.Skeleton);

    this.nodeWord = this.getNode("node_word");
    this.nodeWord.active = false;
    this.nodeWord.setScale(v3(0, 0, 1));

    SpineUtil.playOneByOne(this.spineQianjidaozhang, "animation1", "animation2");

    await Sleep(1.5);
    AudioMgr.instance.playEffect(512);
    this.nodeWord.active = true;
    tween(this.nodeWord)
      .to(0.5, { scale: v3(1, 1, 1) }, { easing: "expoOut" })
      .start();
    await Sleep(0.5);

    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopEventRoute, { from: 303, to: 303 }, async () => {
      this.closeBack();
    });

    await Sleep(3);
    this.closeBack();
  }

  protected onDestroy(): void {
    // 做主线任务
    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopEventRoute, { from: 304, to: 304 });
    super.onDestroy();
  }
}
