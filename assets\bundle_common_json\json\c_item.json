{"1": {"id": 1, "name": "气运", "des": "繁荣度越高，气运产出越高，一种常规消耗资源。", "iconId": "item_1", "color": 2, "goodsType": 1}, "2": {"id": 2, "name": "繁荣度", "iconId": "item_2", "color": 2, "goodsType": 1, "jumplist": [71, 72, 73, 74, 75]}, "3": {"id": 3, "name": "阅历", "des": "弟子培养、挑战关卡等方式产出，用于提升战将等级。", "iconId": "item_3", "color": 2, "goodsType": 1, "jumplist": [41, 21, 79, 80, 95]}, "4": {"id": 4, "name": "因果", "des": "用于升级战将。", "iconId": "item_4", "color": 2, "goodsType": 1, "jumplist": [21, 43]}, "5": {"id": 5, "name": "功德", "des": "用于升级主角", "iconId": "item_5", "color": 2, "goodsType": 1, "jumplist": [21, 43, 85, 86]}, "6": {"id": 6, "name": "仙玉", "des": "蕴含仙气的玉石，比较稀缺的货币，用于道具商店购买等。", "iconId": "item_6", "color": 2, "goodsType": 1, "jumplist": [55]}, "7": {"id": 7, "name": "活跃度", "iconId": "item_7", "color": 2, "goodsType": 1}, "8": {"id": 8, "name": "战力", "iconId": "item_8", "color": 2, "goodsType": 1}, "9": {"id": 9, "name": "资质", "des": "升级战将用的", "iconId": "item_9", "color": 2, "goodsType": 1}, "10": {"id": 10, "name": "天命", "iconId": "item_10", "color": 2, "goodsType": 1}, "11": {"id": 11, "name": "缘分点", "iconId": "item_11", "color": 2, "goodsType": 1, "jumplist": [42]}, "12": {"id": 12, "name": "资质经验", "des": "没固定", "iconId": "item_12", "color": 3, "goodsType": 1}, "50": {"id": 50, "name": "技能点", "des": "用于主角技能属性的提升", "iconId": "item_50", "color": 3, "goodsType": 1}, "1001": {"id": 1001, "name": "小资质丹", "des": "20个小资质丹可以合成一个资质丹", "iconId": "item_1001", "color": 4, "goodsType": 2, "type2List": [1], "jumplist": [47, 21, 54]}, "1002": {"id": 1002, "name": "资质丹", "des": "用于强化战将资质技能", "iconId": "item_1002", "color": 5, "goodsType": 2, "type1": 2, "type1Son1List": [[1001, 20]], "type2List": [1, 3], "jumplist": [87, 61, 63]}, "1004": {"id": 1004, "name": "精力丹", "des": "使用后,可恢复3点精力", "iconId": "item_1004", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [21, 54]}, "1005": {"id": 1005, "name": "活力丹", "des": "使用后,可恢复5点活力", "iconId": "item_1005", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [21, 52, 47, 54]}, "1006": {"id": 1006, "name": "体力丹", "des": "使用后恢复1点体力,体力可用于游历", "iconId": "item_1006", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [21, 47, 63]}, "1007": {"id": 1007, "name": "黄金诏令", "des": "使用后,随机为一个据点招募5个族人", "iconId": "item_1007", "color": 4, "goodsType": 2, "type1": 1, "type1Son1List": [[401, 5]], "type2List": [1]}, "1008": {"id": 1008, "name": "白银诏令", "des": "使用后,随机为一个据点招募3个族人", "iconId": "item_1008", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[401, 3]], "type2List": [1], "jumplist": [21]}, "1009": {"id": 1009, "name": "青铜诏令", "des": "使用后,随机为一个据点招募1个族人", "iconId": "item_1009", "color": 2, "goodsType": 2, "type1": 1, "type1Son1List": [[401, 1]], "type2List": [1], "jumplist": [21]}, "1010": {"id": 1010, "name": " 精魄", "des": "随机一名战将\n生命+800 攻击+150 防御+60", "iconId": "item_1010", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[108, 800, 150, 60]], "type2List": [1], "jumplist": [54]}, "1011": {"id": 1011, "name": "百年精魄", "des": "随机一名战将\n生命+8000 攻击+1500 防御+600", "iconId": "item_1011", "color": 4, "goodsType": 2, "type1": 1, "type1Son1List": [[108, 8000, 1500, 600]], "type2List": [1], "jumplist": [54]}, "1012": {"id": 1012, "name": "千年精魄", "des": "随机一名战将\n生命+80000 攻击+15000 防御+6000", "iconId": "item_1012", "color": 5, "goodsType": 2, "type1": 1, "type1Son1List": [[108, 80000, 15000, 6000]], "type2List": [1]}, "1013": {"id": 1013, "name": "鸡腿", "des": "关卡中,鼓舞士气可临时增加战将5%的总战力", "iconId": "item_1013", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [21]}, "1014": {"id": 1014, "name": "女娲符文残片", "des": "用于合成女娲符文的碎片", "iconId": "item_1014", "color": 3, "goodsType": 2, "type2List": [1]}, "1015": {"id": 1015, "name": "女娲符文", "des": "用于升级女娲神像的道具", "iconId": "item_1015", "color": 4, "goodsType": 2, "type1": 2, "type1Son1List": [[1014, 20]], "type2List": [1, 3], "jumplist": [21, 51, 54]}, "1016": {"id": 1016, "name": "天矶石", "des": "用于扩建建筑提升等级等", "iconId": "item_1016", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [21, 63, 54]}, "1017": {"id": 1017, "name": "抽奖券", "des": "用于玲珑宝鼎抽奖", "iconId": "item_1017", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [21, 62, 52]}, "1018": {"id": 1018, "name": "洗炼精华", "des": "用于提升仙友的技能", "iconId": "item_1018", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [21, 49]}, "1021": {"id": 1021, "name": "药剂", "des": "随机一名人族战将\n生命+385 攻击+75 防御+30", "iconId": "item_1021", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[501, 385, 75, 30]], "type2List": [1], "jumplist": [21, 43]}, "1022": {"id": 1022, "name": "忘尘", "des": "随机一名神族战将\n生命+385 攻击+75 防御+30", "iconId": "item_1022", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[502, 385, 75, 30]], "type2List": [1], "jumplist": [21, 43]}, "1023": {"id": 1023, "name": "玄晶", "des": "随机一名妖族战将\n生命+385 攻击+75 防御+30", "iconId": "item_1023", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[503, 385, 75, 30]], "type2List": [1], "jumplist": [21, 43]}, "1024": {"id": 1024, "name": "圣水", "des": "随机一名冥族战将\n生命+385 攻击+75 防御+30", "iconId": "item_1024", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[504, 385, 75, 30]], "type2List": [1], "jumplist": [21, 43]}, "1025": {"id": 1025, "name": "炎魂", "des": "随机一名巫族战将\n生命+385 攻击+75 防御+30", "iconId": "item_1025", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[505, 385, 75, 30]], "type2List": [1], "jumplist": [21, 43]}, "1026": {"id": 1026, "name": "百宝箱", "des": "开启后,获得药剂/忘尘/玄晶/圣水/炎魂中的一种", "iconId": "item_1026", "color": 3, "goodsType": 2, "type1": 4, "type1Son1List": [[1021, 1], [1022, 1], [1023, 1], [1024, 1], [1025, 1]], "type2List": [1]}, "1027": {"id": 1027, "name": "小阅历箱", "des": "开启后,获得2500阅历", "iconId": "item_1027", "color": 2, "goodsType": 2, "type1": 1, "type1Son1List": [[103, 2500]], "type2List": [1]}, "1028": {"id": 1028, "name": "中阅历箱", "des": "开启后,获得10000阅历", "iconId": "item_1028", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[103, 10000]], "type2List": [1]}, "1029": {"id": 1029, "name": "大阅历箱", "des": "开启后,获得100000阅历", "iconId": "item_1029", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[103, 100000]], "type2List": [1]}, "1031": {"id": 1031, "name": "分钟宝袋", "des": "使用后,立即获得1分钟的气运", "iconId": "item_1031", "color": 2, "goodsType": 2, "type1": 1, "type1Son1List": [[402, 1]], "type2List": [1]}, "1032": {"id": 1032, "name": "小时净瓶", "des": "使用后,立即获得1小时的气运", "iconId": "item_1032", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[402, 60]], "type2List": [1]}, "1033": {"id": 1033, "name": "功德卡", "des": "使用后可立即获得100点功德", "iconId": "item_1033", "color": 4, "goodsType": 2, "type1": 1, "type1Son1List": [[105, 100]], "type2List": [1]}, "1034": {"id": 1034, "name": "功德符", "des": "使用后可立即获得10点功德", "iconId": "item_1034", "color": 3, "goodsType": 2, "type1": 1, "type1Son1List": [[105, 10]], "type2List": [1]}, "1035": {"id": 1035, "name": "战将令碎片", "des": "收集20个战将令碎片,可以合成一个战将令", "iconId": "item_1035", "color": 4, "goodsType": 2, "type2List": [1], "jumplist": [54, 61]}, "1036": {"id": 1036, "name": "战将令", "des": "用于玲珑宝鼎兑换商店战将/仙友兑换和战将精进", "iconId": "item_1036", "color": 5, "goodsType": 2, "type1": 2, "type1Son1List": [[1035, 20]], "type2List": [1, 3], "jumplist": [54, 61, 81]}, "1037": {"id": 1037, "name": "天王令碎片", "des": "收集20个天王令碎片,可以合成一个天王令", "iconId": "item_1037", "color": 4, "goodsType": 2, "type2List": [1], "jumplist": [61]}, "1038": {"id": 1038, "name": "天王令", "des": "用于稀有战将的兑换和技能升级", "iconId": "item_1038", "color": 5, "goodsType": 2, "type1": 2, "type1Son1List": [[1037, 20]], "type2List": [1, 3], "jumplist": [61, 81, 96]}, "1039": {"id": 1039, "name": "文牒碎片", "des": "收集20个文牒碎片,可以合成一个通关文牒", "iconId": "item_1039", "color": 4, "goodsType": 2, "type2List": [1], "jumplist": [61]}, "1040": {"id": 1040, "name": "通关文牒", "des": "用于稀有战将的兑换和技能升级", "iconId": "item_1040", "color": 5, "goodsType": 2, "type1": 2, "type1Son1List": [[1039, 20]], "type2List": [1, 3], "jumplist": [61, 81, 96]}, "1041": {"id": 1041, "name": "人族书籍", "des": "用于提升人族书籍技能", "iconId": "item_1041", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [43, 41, 21]}, "1042": {"id": 1042, "name": "神族书籍", "des": "用于提升神族书籍技能", "iconId": "item_1042", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [43, 41, 21]}, "1043": {"id": 1043, "name": "妖族书籍", "des": "用于提升妖族书籍技能", "iconId": "item_1043", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [43, 41, 21]}, "1044": {"id": 1044, "name": "冥族书籍", "des": "用于提升冥族书籍技能", "iconId": "item_1044", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [43, 41, 21]}, "1045": {"id": 1045, "name": "巫族书籍", "des": "用于提升巫族书籍技能", "iconId": "item_1045", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [43, 41, 21]}, "1046": {"id": 1046, "name": "无字天书", "des": "点击可以随机获得一本种族书籍", "iconId": "item_1046", "color": 2, "goodsType": 2, "type1": 4, "type1Son1List": [[1041, 1], [1042, 1], [1043, 1], [1044, 1], [1045, 1]], "type2List": [1], "jumplist": [43, 41, 21]}, "1047": {"id": 1047, "name": "自选牒令碎片宝箱", "des": "打开自选获得天王令碎片、文牒碎片中的一种", "iconId": "item_1047", "color": 4, "goodsType": 2, "type1": 5, "type1Son1List": [[1037, 1], [1039, 1]], "type2List": [1], "jumplist": [49]}, "1048": {"id": 1048, "name": "自选牒令宝箱", "des": "打开自选获得天王令、通关文牒中的一种", "iconId": "item_1048", "color": 5, "goodsType": 2, "type1": 5, "type1Son1List": [[1038, 1], [1040, 1]], "type2List": [1]}, "1049": {"id": 1049, "name": "突破宝箱", "des": "打开获得\n卓越佩剑*1、卓越头盔*1、卓越护甲*1", "iconId": "item_1049", "color": 4, "goodsType": 2, "type1": 3, "type1Son1List": [[1051, 1], [1052, 1], [1053, 1]], "type2List": [1], "jumplist": [49]}, "1050": {"id": 1050, "name": "机选宝物箱", "des": "打开随机同概率获得药剂、忘尘、玄晶、圣水、炎魂的一种", "iconId": "item_1050", "color": 3, "goodsType": 2, "type1": 4, "type1Son1List": [[1021, 1], [1022, 1], [1023, 1], [1024, 1], [1025, 1]], "type2List": [1]}, "1051": {"id": 1051, "name": "卓越佩剑", "des": "用于战将升级突破的必备道具", "iconId": "item_1051", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [89, 90, 47, 44]}, "1052": {"id": 1052, "name": "卓越头盔", "des": "用于战将升级突破的必备道具", "iconId": "item_1052", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [89, 90, 47, 44]}, "1053": {"id": 1053, "name": "卓越护甲", "des": "用于战将升级突破的必备道具", "iconId": "item_1053", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [89, 90, 47, 44]}, "1054": {"id": 1054, "name": "传奇佩剑", "des": "用于战将升级突破的必备道具", "iconId": "item_1054", "color": 4, "goodsType": 2, "type1": 2, "type1Son1List": [[1051, 20]], "type2List": [1, 3], "jumplist": [51]}, "1055": {"id": 1055, "name": "传奇头盔", "des": "用于战将升级突破的必备道具", "iconId": "item_1055", "color": 4, "goodsType": 2, "type1": 2, "type1Son1List": [[1052, 20]], "type2List": [1, 3], "jumplist": [51]}, "1056": {"id": 1056, "name": "传奇护甲", "des": "用于战将升级突破的必备道具", "iconId": "item_1056", "color": 4, "goodsType": 2, "type1": 2, "type1Son1List": [[1053, 20]], "type2List": [1, 3], "jumplist": [51]}, "1057": {"id": 1057, "name": "无双佩剑", "des": "用于战将升级突破的必备道具", "iconId": "item_1057", "color": 5, "goodsType": 2, "type1": 2, "type1Son1List": [[1054, 50]], "type2List": [1, 3], "jumplist": [51]}, "1058": {"id": 1058, "name": "无双头盔", "des": "用于战将升级突破的必备道具", "iconId": "item_1058", "color": 5, "goodsType": 2, "type1": 2, "type1Son1List": [[1055, 50]], "type2List": [1, 3], "jumplist": [51]}, "1059": {"id": 1059, "name": "无双护甲", "des": "用于战将升级突破的必备道具", "iconId": "item_1059", "color": 5, "goodsType": 2, "type1": 2, "type1Son1List": [[1056, 50]], "type2List": [1, 3], "jumplist": [51]}, "1060": {"id": 1060, "name": "机选突破宝箱", "des": "打开随机同概率获得卓越佩剑、卓越头盔、卓越护甲中的一种", "iconId": "item_1060", "color": 3, "goodsType": 2, "type1": 4, "type1Son1List": [[1051, 1], [1052, 1], [1053, 1]], "type2List": [1]}, "1061": {"id": 1061, "name": "铜镜", "des": "可赠送给仙友,增加1点因果", "iconId": "item_1061", "color": 1, "goodsType": 2, "type2List": [1], "jumplist": [21, 47, 54]}, "1062": {"id": 1062, "name": "玉佩", "des": "可赠送给仙友,增加2点因果", "iconId": "item_1062", "color": 2, "goodsType": 2, "type2List": [1]}, "1063": {"id": 1063, "name": "指环", "des": "可赠送给仙友,增加5点因果", "iconId": "item_1063", "color": 3, "goodsType": 2, "type2List": [1]}, "1064": {"id": 1064, "name": "信物", "des": "可赠送给仙友,增加1点天命", "iconId": "item_1064", "color": 1, "goodsType": 2, "type2List": [1], "jumplist": [21, 47]}, "1065": {"id": 1065, "name": "史书", "des": "可赠送给仙友,增加2点天命", "iconId": "item_1065", "color": 2, "goodsType": 2, "type2List": [1]}, "1066": {"id": 1066, "name": "典籍", "des": "可赠送给仙友,增加5点天命", "iconId": "item_1066", "color": 3, "goodsType": 2, "type2List": [1]}, "1069": {"id": 1069, "name": "随机突破宝箱", "des": "打开随机同概率获得卓越佩剑、卓越头盔、卓越护甲的一种", "iconId": "item_1069", "color": 3, "goodsType": 2, "type1": 4, "type1Son1List": [[1051, 1], [1052, 1], [1053, 1]], "type2List": [1]}, "1070": {"id": 1070, "name": "灵兽丹", "des": "用于古境商店兑换灵兽或道具", "iconId": "item_1070", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [21, 44, 63, 54]}, "1071": {"id": 1071, "name": "灵兽果", "des": "用于提升灵兽等级", "iconId": "item_1071", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [21, 44, 47, 62]}, "1072": {"id": 1072, "name": "洗练石", "des": "用于洗练灵兽技能", "iconId": "item_1072", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [21, 44]}, "1073": {"id": 1073, "name": "觉醒果", "des": "用于觉醒灵兽的技能", "iconId": "item_1073", "color": 5, "goodsType": 2, "type2List": [1], "jumplist": [44, 61, 49]}, "1075": {"id": 1075, "name": "五彩石", "des": "用于升级神器消耗", "iconId": "item_1075", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [53, 63, 94]}, "1076": {"id": 1076, "name": "七彩石", "des": "用于升阶神器消耗", "iconId": "item_1076", "color": 4, "goodsType": 2, "type2List": [1], "jumplist": [53, 63, 94]}, "1079": {"id": 1079, "name": "换颜珠", "des": "用于激活主角皮肤，亦可使用，立即获得500仙玉", "iconId": "item_1079", "color": 4, "goodsType": 2, "type1": 3, "type1Son1List": [[6, 500]], "type2List": [1], "jumplist": [52]}, "1081": {"id": 1081, "name": "演武币", "des": "用于演武商店购买货币", "iconId": "item_1081", "color": 3, "goodsType": 2}, "1082": {"id": 1082, "name": "挑战券", "des": "用于在演武场中主动挑战对手,增加积分和战将战力", "iconId": "item_1082", "color": 3, "goodsType": 2, "type2List": [1], "jumplist": [41, 52]}, "1085": {"id": 1085, "name": "古境积分", "des": "用于天荒古境商店购买货币", "iconId": "item_1085", "color": 3, "goodsType": 2}, "1086": {"id": 1086, "name": "番天印", "des": "用于召唤牛魔王和升级牛魔王光环的必备道具", "iconId": "item_1086", "color": 5, "goodsType": 2, "type2List": [1]}, "1091": {"id": 1091, "des": "用于领地皮肤装扮", "iconId": "item_1091", "color": 4, "goodsType": 7}, "1101": {"id": 1101, "name": "银叶子", "des": "用于兑换商店购买普通道具", "iconId": "item_1101", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [93]}, "1102": {"id": 1102, "name": "金叶子", "des": "用于兑换商店购买稀有道具", "iconId": "item_1102", "color": 5, "goodsType": 2, "type2List": [1], "jumplist": [93]}, "1103": {"id": 1103, "name": "试炼币", "des": "用于三界试炼商店购买道具", "iconId": "item_1103", "color": 2, "goodsType": 2, "type2List": [1]}, "1104": {"id": 1104, "name": "挑战币", "des": "用于每日挑战商店购买道具", "iconId": "item_1104", "color": 2, "goodsType": 2, "type2List": [1]}, "1105": {"id": 1105, "name": "战盟贡献", "des": "用于战盟商店购买", "iconId": "item_1105", "color": 2, "goodsType": 2, "jumplist": [91, 92]}, "1106": {"id": 1106, "name": "战盟经验", "des": "用于战盟升级", "iconId": "item_1106", "color": 2, "goodsType": 2}, "1107": {"id": 1107, "name": "战盟活跃", "des": "用于统计个人战盟活跃度", "iconId": "item_1107", "color": 2, "goodsType": 2}, "1201": {"id": 1201, "name": "魂石", "des": "用于兽魂升级消耗", "iconId": "item_1201", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [48]}, "1202": {"id": 1202, "name": "铸魂令", "des": "用于兽魂刷新、购买和洗炼消耗", "iconId": "item_1202", "color": 2, "goodsType": 2, "type2List": [1], "jumplist": [52, 47, 49]}, "1501": {"id": 1501, "name": "头像框1", "des": "战将生命:+1000", "iconId": "item_1501", "color": 2, "goodsType": 12}, "1502": {"id": 1502, "name": "头像框2", "des": "战将攻击:+500", "iconId": "item_1502", "color": 2, "goodsType": 12}, "1503": {"id": 1503, "name": "头像框3", "des": "战将攻击:+500", "iconId": "item_1503", "color": 2, "goodsType": 12}, "1504": {"id": 1504, "name": "头像框4", "des": "战将防御:+250", "iconId": "item_1504", "color": 2, "goodsType": 12}, "1531": {"id": 1531, "name": "气泡框1", "des": "战将生命:+1000", "iconId": "item_1531", "color": 2, "goodsType": 13}, "1532": {"id": 1532, "name": "气泡框2", "des": "战将攻击:+500", "iconId": "item_1532", "color": 2, "goodsType": 13}, "1533": {"id": 1533, "name": "气泡框3", "des": "战将攻击:+500", "iconId": "item_1533", "color": 2, "goodsType": 13}, "1534": {"id": 1534, "name": "气泡框4", "des": "战将防御:+250", "iconId": "item_1534", "color": 2, "goodsType": 13}, "1561": {"id": 1561, "name": "天命所归", "des": "加成:生命 +1000 攻击 +500 防御 +500\n时效:永久生效[/br]获取:终身卡获得", "iconId": "item_1561", "color": 5, "goodsType": 14}, "1562": {"id": 1562, "name": "史前之王", "des": "加成:生命 +2000 攻击 +800 防御 +600 暴击+5%\n时效:下期同类型活动结束之后重置[/br]圣殿:获得该称号可晋升至名就宫[/br]获取:运营活动", "iconId": "item_1562", "color": 5, "goodsType": 14}, "1563": {"id": 1563, "name": "山海霸主", "des": "加成:生命 +2000 攻击 +800 防御 +600 闪避+2%\n时效:下期同类型活动结束之后重置[/br]圣殿:获得该称号可晋升至无望宫[/br]获取:运营活动", "iconId": "item_1563", "color": 5, "goodsType": 14}, "1591": {"id": 1591, "name": "觉醒境", "des": "  生命 +200 攻击 +100 防御 +100", "iconId": "title_1591", "color": 2, "goodsType": 14}, "1592": {"id": 1592, "name": "荣耀境", "des": "  生命 +400 攻击 +200 防御 +200", "iconId": "title_1592", "color": 2, "goodsType": 14}, "1593": {"id": 1593, "name": "法相境", "des": "  生命 +600 攻击 +300 防御 +300", "iconId": "title_1593", "color": 3, "goodsType": 14}, "1594": {"id": 1594, "name": "天位境", "des": "  生命 +800 攻击 +400 防御 +400", "iconId": "title_1594", "color": 3, "goodsType": 14}, "1595": {"id": 1595, "name": "王侯境", "des": "  生命 +1000 攻击 +500 防御 +500", "iconId": "title_1595", "color": 4, "goodsType": 14}, "1596": {"id": 1596, "name": "贤者境", "des": "  生命 +1200 攻击 +600 防御 +600", "iconId": "title_1596", "color": 4, "goodsType": 14}, "1597": {"id": 1597, "name": "圣境", "des": "  生命 +1400 攻击 +700 防御 +700", "iconId": "title_1597", "color": 5, "goodsType": 14}, "1598": {"id": 1598, "name": "人皇境", "des": "  生命 +1600 攻击 +800 防御 +800", "iconId": "title_1598", "color": 5, "goodsType": 14}, "1599": {"id": 1599, "name": "帝境", "des": "  生命 +2000 攻击 +1000 防御 +1000", "iconId": "title_1599", "color": 5, "goodsType": 14}, "1601": {"id": 1601, "name": "头像1", "iconId": "item_1601", "color": 2, "goodsType": 12}, "1602": {"id": 1602, "name": "头像2", "iconId": "item_1602", "color": 2, "goodsType": 12}, "1603": {"id": 1603, "name": "头像3", "iconId": "item_1603", "color": 2, "goodsType": 12}, "1604": {"id": 1604, "name": "头像4", "iconId": "item_1604", "color": 2, "goodsType": 12}, "1611": {"id": 1611, "name": "头像5", "iconId": "item_1611", "color": 2, "goodsType": 12}, "1612": {"id": 1612, "name": "头像6", "iconId": "item_1612", "color": 2, "goodsType": 12}, "1613": {"id": 1613, "name": "头像7", "iconId": "item_1613", "color": 2, "goodsType": 12}, "1614": {"id": 1614, "name": "头像8", "iconId": "item_1614", "color": 2, "goodsType": 12}, "1701": {"id": 1701, "iconId": "item_1701", "color": 5, "goodsType": 11}, "1702": {"id": 1702, "iconId": "item_1702", "color": 5, "goodsType": 11}, "1703": {"id": 1703, "iconId": "item_1703", "color": 5, "goodsType": 11}, "1704": {"id": 1704, "iconId": "item_1704", "color": 5, "goodsType": 11}, "1705": {"id": 1705, "iconId": "item_1705", "color": 5, "goodsType": 11}, "1706": {"id": 1706, "iconId": "item_1706", "color": 5, "goodsType": 11}, "1711": {"id": 1711, "iconId": "item_1711", "color": 5, "goodsType": 11}, "1712": {"id": 1712, "iconId": "item_1712", "color": 5, "goodsType": 11}, "1713": {"id": 1713, "iconId": "item_1713", "color": 5, "goodsType": 11}, "1714": {"id": 1714, "iconId": "item_1714", "color": 5, "goodsType": 11}, "1715": {"id": 1715, "iconId": "item_1715", "color": 5, "goodsType": 11}, "1716": {"id": 1716, "iconId": "item_1716", "color": 5, "goodsType": 11}, "1801": {"id": 1801, "iconId": "item_1801", "color": 5, "goodsType": 15}, "1802": {"id": 1802, "iconId": "item_1802", "color": 5, "goodsType": 15}, "1803": {"id": 1803, "iconId": "item_1803", "color": 5, "goodsType": 15}, "1804": {"id": 1804, "iconId": "item_1804", "color": 5, "goodsType": 15}, "1805": {"id": 1805, "iconId": "item_1805", "color": 5, "goodsType": 15}, "1806": {"id": 1806, "iconId": "item_1806", "color": 5, "goodsType": 15}, "1807": {"id": 1807, "iconId": "item_1807", "color": 5, "goodsType": 15}, "1808": {"id": 1808, "iconId": "item_1808", "color": 5, "goodsType": 15}, "1809": {"id": 1809, "iconId": "item_1809", "color": 5, "goodsType": 15}, "1810": {"id": 1810, "iconId": "item_1810", "color": 5, "goodsType": 15}, "1811": {"id": 1811, "iconId": "item_1811", "color": 5, "goodsType": 15}, "1812": {"id": 1812, "iconId": "item_1812", "color": 5, "goodsType": 15}, "1813": {"id": 1813, "iconId": "item_1813", "color": 5, "goodsType": 15}, "1814": {"id": 1814, "iconId": "item_1814", "color": 5, "goodsType": 15}, "1815": {"id": 1815, "iconId": "item_1815", "color": 5, "goodsType": 15}, "1816": {"id": 1816, "iconId": "item_1816", "color": 5, "goodsType": 15}, "1817": {"id": 1817, "iconId": "item_1817", "color": 5, "goodsType": 15}, "1818": {"id": 1818, "iconId": "item_1818", "color": 5, "goodsType": 15}, "1819": {"id": 1819, "iconId": "item_1819", "color": 5, "goodsType": 15}, "1820": {"id": 1820, "iconId": "item_1820", "color": 5, "goodsType": 15}, "1821": {"id": 1821, "iconId": "item_1821", "color": 5, "goodsType": 15}, "1822": {"id": 1822, "iconId": "item_1822", "color": 5, "goodsType": 15}, "1823": {"id": 1823, "iconId": "item_1823", "color": 5, "goodsType": 15}, "1824": {"id": 1824, "iconId": "item_1824", "color": 5, "goodsType": 15}, "2001": {"id": 2001, "iconId": "item_2001", "color": 5, "goodsType": 15}, "2002": {"id": 2002, "iconId": "item_2002", "color": 5, "goodsType": 15}, "10101": {"id": 10101, "name": "鬼新娘", "iconId": "item_10101", "color": 1, "goodsType": 3}, "10102": {"id": 10102, "name": "千机道长", "iconId": "item_10102", "color": 1, "goodsType": 3}, "10103": {"id": 10103, "name": "巡山小妖", "iconId": "item_10103", "color": 1, "goodsType": 3}, "10105": {"id": 10105, "name": "寿星", "iconId": "item_10105", "color": 1, "goodsType": 3}, "10201": {"id": 10201, "name": "无常", "iconId": "item_10201", "color": 3, "goodsType": 3}, "10202": {"id": 10202, "name": "聂小芊", "iconId": "item_10202", "color": 2, "goodsType": 3}, "10203": {"id": 10203, "name": "钟馗", "iconId": "item_10203", "color": 2, "goodsType": 3}, "10204": {"id": 10204, "name": "铁拐李", "iconId": "item_10204", "color": 3, "goodsType": 3}, "10205": {"id": 10205, "name": "雷震子", "iconId": "item_10205", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10206": {"id": 10206, "name": "太乙", "iconId": "item_10206", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10207": {"id": 10207, "name": "干将莫邪", "iconId": "item_10207", "color": 2, "goodsType": 3}, "10208": {"id": 10208, "name": "燕大侠", "iconId": "item_10208", "color": 2, "goodsType": 3}, "10209": {"id": 10209, "name": "智公", "iconId": "item_10209", "color": 2, "goodsType": 3}, "10210": {"id": 10210, "name": "蛇妖", "iconId": "item_10210", "color": 2, "goodsType": 3}, "10211": {"id": 10211, "name": "蜘蛛精", "iconId": "item_10211", "color": 2, "goodsType": 3}, "10212": {"id": 10212, "name": "金银角", "iconId": "item_10212", "color": 3, "goodsType": 3}, "10213": {"id": 10213, "name": "华神医", "iconId": "item_10213", "color": 2, "goodsType": 3}, "10215": {"id": 10215, "name": "鬼谷子", "iconId": "item_10215", "color": 2, "goodsType": 3}, "10301": {"id": 10301, "name": "白起", "iconId": "item_10301", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10304": {"id": 10304, "name": "土行孙", "iconId": "item_10304", "color": 3, "goodsType": 3}, "10305": {"id": 10305, "name": "扫地僧", "iconId": "item_10305", "color": 3, "goodsType": 3}, "10306": {"id": 10306, "name": "<PERSON>白", "iconId": "item_10306", "color": 3, "goodsType": 3}, "10308": {"id": 10308, "name": "大仑明王", "iconId": "item_10308", "color": 3, "goodsType": 3}, "10310": {"id": 10310, "name": "杨戬", "iconId": "item_10310", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10311": {"id": 10311, "name": "哪吒", "iconId": "item_10311", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10313": {"id": 10313, "name": "青毛狮子", "iconId": "item_10313", "color": 3, "goodsType": 3}, "10314": {"id": 10314, "name": "黄牙老象", "iconId": "item_10314", "color": 3, "goodsType": 3}, "10315": {"id": 10315, "name": "金翅大鹏", "iconId": "item_10315", "color": 3, "goodsType": 3}, "10401": {"id": 10401, "name": "大侠", "iconId": "item_10401", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10403": {"id": 10403, "name": "司马懿", "iconId": "item_10403", "color": 3, "goodsType": 3}, "10404": {"id": 10404, "name": "孟婆", "iconId": "item_10404", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10406": {"id": 10406, "name": "持国天王", "iconId": "item_10406", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10407": {"id": 10407, "name": "增长天王", "iconId": "item_10407", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10408": {"id": 10408, "name": "广目天王", "iconId": "item_10408", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10409": {"id": 10409, "name": "多闻天王", "iconId": "item_10409", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10410": {"id": 10410, "name": "李靖", "iconId": "item_10410", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10411": {"id": 10411, "name": "卧龙", "iconId": "item_10411", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10412": {"id": 10412, "name": "苏妲己", "iconId": "item_10412", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10413": {"id": 10413, "name": "东方", "iconId": "item_10413", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10414": {"id": 10414, "name": "孔宣", "iconId": "item_10414", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10415": {"id": 10415, "name": "燃灯道人", "iconId": "item_10415", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10416": {"id": 10416, "name": "天蓬元帅", "iconId": "item_10416", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10417": {"id": 10417, "name": "斗战圣佛", "iconId": "item_10417", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10418": {"id": 10418, "name": "卷帘大将", "iconId": "item_10418", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10419": {"id": 10419, "name": "白龙护法", "iconId": "item_10419", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10420": {"id": 10420, "name": "金蝉子", "iconId": "item_10420", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10421": {"id": 10421, "name": "通天教主", "iconId": "item_10421", "color": 4, "closeSpecial": 1, "goodsType": 3}, "10426": {"id": 10426, "name": "牛魔王", "iconId": "item_10426", "color": 4, "closeSpecial": 1, "goodsType": 3}, "20001": {"id": 20001, "name": "修行积分", "des": "修行积分", "iconId": "item_20001", "color": 2, "goodsType": 2}, "20002": {"id": 20002, "name": "繁荣币", "des": "用于繁荣度冲榜商店兑换", "iconId": "item_20002", "color": 2, "goodsType": 2}, "20003": {"id": 20003, "name": "破天箭", "des": "山海探险活动消耗道具", "iconId": "item_20003", "color": 2, "goodsType": 2}, "20004": {"id": 20004, "name": "天晶石", "des": "山海探险活动商店货币", "iconId": "item_20004", "color": 2, "goodsType": 2}, "20005": {"id": 20005, "name": "圣殿币", "des": "用于圣殿兑换主角皮肤", "iconId": "item_20005", "color": 2, "goodsType": 2}, "20006": {"id": 20006, "name": "运势", "des": "逆袭之路成就累计资源", "iconId": "item_20006", "color": 2, "goodsType": 2}, "20010": {"id": 20010, "name": "通幽积分", "des": "用于曲径通幽商店兑换", "iconId": "item_20010", "color": 2, "goodsType": 2}, "20011": {"id": 20011, "name": "手编草履", "des": "【说明】\n曲径通幽活动道具，使用后必得300活动积分和信物*1", "iconId": "item_20011", "color": 2, "goodsType": 2}, "20012": {"id": 20012, "name": "简易布鞋", "des": "【说明】\n曲径通幽活动道具，使用后必得600活动积分，同时获得一个随机道具[/br]【随机道具概率】[/br]史书*1   20%[/br]信物*1   80%", "iconId": "item_20012", "color": 2, "goodsType": 2}, "20013": {"id": 20013, "name": "唐昌布鞋", "des": "【说明】\n曲径通幽活动道具，使用后必得1000活动积分，同时获得一个随机道具[/br]【随机道具概率】[/br]史书*2   20%[/br]信物*2   80%", "iconId": "item_20013", "color": 3, "goodsType": 2}, "20014": {"id": 20014, "name": "高级皮靴", "des": "【说明】\n曲径通幽活动道具，使用后必得1200活动积分，同时获得一个随机道具[/br]【随机道具概率】[/br]典籍*3   20%[/br]史书*2   80%", "iconId": "item_20014", "color": 4, "goodsType": 2}, "20015": {"id": 20015, "name": "斗牛积分", "des": "用于牛气冲天商店兑换", "color": 2, "goodsType": 2}, "20016": {"id": 20016, "name": "斗牛号角", "des": "【说明】\n牛气冲天活动道具，使用后必得300活动积分和信物*1", "iconId": "item_20016", "color": 2, "goodsType": 2}, "20017": {"id": 20017, "name": "冲锋号角", "des": "【说明】\n牛气冲天活动道具，使用后必得600活动积分，同时获得一个随机道具[/br]【随机道具概率】[/br]史书*1   20%[/br]信物*1   80%", "iconId": "item_20017", "color": 2, "goodsType": 2}, "20018": {"id": 20018, "name": "追击号角", "des": "【说明】\n牛气冲天活动道具，使用后必得1000活动积分，同时获得一个随机道具[/br]【随机道具概率】[/br]史书*2   20%[/br]信物*2   80%", "iconId": "item_20018", "color": 3, "goodsType": 2}, "20019": {"id": 20019, "name": "绝杀号角", "des": "【说明】\n牛气冲天活动道具，使用后必得1200活动积分，同时获得一个随机道具[/br]【随机道具概率】[/br]典籍*3   20%[/br]史书*2   80%", "iconId": "item_20019", "color": 4, "goodsType": 2}, "20020": {"id": 20020, "name": "祈福神香", "des": "祈福神树活动消耗道具", "iconId": "item_20020", "color": 2, "goodsType": 2}, "20021": {"id": 20021, "name": "福币", "des": "用于祈福神树商店兑换", "iconId": "item_20021", "color": 2, "goodsType": 2}, "20022": {"id": 20022, "name": "龙爪", "des": "时空裂隙活动消耗道具", "iconId": "item_20022", "color": 2, "goodsType": 2}, "20023": {"id": 20023, "name": "时空币", "des": "用于时空裂隙商店兑换", "iconId": "item_20023", "color": 2, "goodsType": 2}, "20024": {"id": 20024, "name": "积分宝箱", "des": "积分宝箱", "iconId": "item_20024", "color": 2, "goodsType": 2}, "20025": {"id": 20025, "name": "道具宝箱", "des": "道具宝箱", "iconId": "item_20025", "color": 3, "goodsType": 2}, "20026": {"id": 20026, "name": "高级宝箱", "des": "高级宝箱", "iconId": "item_20026", "color": 4, "goodsType": 2}, "20101": {"id": 20101, "name": "耳鼠", "des": "鬼新娘所属灵兽", "iconId": "item_pet_20101", "color": 1, "goodsType": 5}, "20102": {"id": 20102, "name": "蛊雕", "des": "巡山小妖所属灵兽", "iconId": "item_pet_20102", "color": 1, "goodsType": 5}, "20103": {"id": 20103, "name": "黑狗", "des": "千机道长所属灵兽", "iconId": "item_pet_20103", "color": 1, "goodsType": 5}, "20105": {"id": 20105, "name": "文鳐", "des": "寿星所属灵兽", "iconId": "item_pet_20104", "color": 1, "goodsType": 5}, "20106": {"id": 20106, "name": "青鸾", "des": "无常所属灵兽", "iconId": "item_pet_20105", "color": 3, "goodsType": 5}, "20107": {"id": 20107, "name": "蛮牛", "des": "聂小芊所属灵兽", "iconId": "item_pet_20110", "color": 2, "goodsType": 5}, "20108": {"id": 20108, "name": "山膏", "des": "钟馗所属灵兽", "iconId": "item_pet_20111", "color": 2, "goodsType": 5}, "20109": {"id": 20109, "name": "獬豸", "des": "铁拐李所属灵兽", "iconId": "item_pet_20106", "color": 3, "goodsType": 5}, "20110": {"id": 20110, "name": "穷奇", "des": "雷震子所属灵兽", "iconId": "item_pet_20108", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20111": {"id": 20111, "name": "青龙", "des": "太乙所属灵兽", "iconId": "item_pet_20401", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20112": {"id": 20112, "name": "羬羊", "des": "干将莫邪所属灵兽", "iconId": "item_pet_20112", "color": 2, "goodsType": 5}, "20113": {"id": 20113, "name": "蛮牛", "des": "燕大侠所属灵兽", "iconId": "item_pet_20110", "color": 2, "goodsType": 5}, "20114": {"id": 20114, "name": "山膏", "des": "智公所属灵兽", "iconId": "item_pet_20111", "color": 2, "goodsType": 5}, "20115": {"id": 20115, "name": "羬羊", "des": "蛇妖所属灵兽", "iconId": "item_pet_20112", "color": 2, "goodsType": 5}, "20116": {"id": 20116, "name": "蛮牛", "des": "蜘蛛精所属灵兽", "iconId": "item_pet_20110", "color": 2, "goodsType": 5}, "20117": {"id": 20117, "name": "山膏", "des": "金银角所属灵兽", "iconId": "item_pet_20111", "color": 2, "goodsType": 5}, "20118": {"id": 20118, "name": "羬羊", "des": "华神医所属灵兽", "iconId": "item_pet_20112", "color": 2, "goodsType": 5}, "20120": {"id": 20120, "name": "蛮牛", "des": "鬼谷子所属灵兽", "iconId": "item_pet_20110", "color": 2, "goodsType": 5}, "20121": {"id": 20121, "name": "玄武", "des": "白起所属灵兽", "iconId": "item_pet_20402", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20123": {"id": 20123, "name": "数斯", "des": "土行孙所属灵兽", "iconId": "item_pet_20107", "color": 3, "goodsType": 5}, "20124": {"id": 20124, "name": "巴蛇", "des": "扫地僧所属灵兽", "iconId": "item_pet_20109", "color": 3, "goodsType": 5}, "20126": {"id": 20126, "name": "獬豸", "des": "大仑明王所属灵兽", "iconId": "item_pet_20106", "color": 3, "goodsType": 5}, "20127": {"id": 20127, "name": "穷奇", "des": "杨戬所属灵兽", "iconId": "item_pet_20108", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20128": {"id": 20128, "name": "青龙", "des": "哪吒所属灵兽", "iconId": "item_pet_20401", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20129": {"id": 20129, "name": "数斯", "des": "青毛狮子所属灵兽", "iconId": "item_pet_20107", "color": 3, "goodsType": 5}, "20130": {"id": 20130, "name": "巴蛇", "des": "黄牙老象所属灵兽", "iconId": "item_pet_20109", "color": 3, "goodsType": 5}, "20202": {"id": 20202, "name": "青栾", "des": "李白所属灵兽", "iconId": "item_pet_20105", "color": 3, "goodsType": 5}, "20205": {"id": 20205, "name": "青栾", "des": "金翅大鹏所属灵兽", "iconId": "item_pet_20105", "color": 3, "goodsType": 5}, "20206": {"id": 20206, "name": "玄武", "des": "牛魔王所属灵兽", "iconId": "item_pet_20402", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20207": {"id": 20207, "name": "穷奇", "des": "大侠所属灵兽", "iconId": "item_pet_20108", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20303": {"id": 20303, "name": "獬豸", "des": "司马懿所属灵兽", "iconId": "item_pet_20106", "color": 3, "goodsType": 5}, "20304": {"id": 20304, "name": "青龙", "des": "孟婆所属灵兽", "iconId": "item_pet_20401", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20306": {"id": 20306, "name": "穷奇", "des": "李靖所属灵兽", "iconId": "item_pet_20108", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20307": {"id": 20307, "name": "青龙", "des": "卧龙所属灵兽", "iconId": "item_pet_20401", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20308": {"id": 20308, "name": "玄武", "des": "苏妲己所属灵兽", "iconId": "item_pet_20402", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20309": {"id": 20309, "name": "穷奇", "des": "东方所属灵兽", "iconId": "item_pet_20108", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20310": {"id": 20310, "name": "青龙", "des": "孔宣所属灵兽", "iconId": "item_pet_20401", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20311": {"id": 20311, "name": "玄武", "des": "燃灯道人所属灵兽", "iconId": "item_pet_20402", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20401": {"id": 20401, "name": "穷奇", "des": "天蓬元帅所属灵兽", "iconId": "item_pet_20108", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20402": {"id": 20402, "name": "青龙", "des": "斗战圣佛所属灵兽", "iconId": "item_pet_20401", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20403": {"id": 20403, "name": "玄武", "des": "卷帘大将所属灵兽", "iconId": "item_pet_20402", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20404": {"id": 20404, "name": "穷奇", "des": "白龙护法所属灵兽", "iconId": "item_pet_20108", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20405": {"id": 20405, "name": "青龙", "des": "金蝉子所属灵兽", "iconId": "item_pet_20401", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20411": {"id": 20411, "name": "玄武", "des": "持国天王所属灵兽", "iconId": "item_pet_20402", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20412": {"id": 20412, "name": "穷奇", "des": "增长天王所属灵兽", "iconId": "item_pet_20108", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20413": {"id": 20413, "name": "青龙", "des": "广目天王所属灵兽", "iconId": "item_pet_20401", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20414": {"id": 20414, "name": "玄武", "des": "多闻天王所属灵兽", "iconId": "item_pet_20402", "color": 4, "closeSpecial": 1, "goodsType": 5}, "20421": {"id": 20421, "name": "玄武", "des": "通天教主所属灵兽", "iconId": "item_pet_20402", "color": 4, "closeSpecial": 1, "goodsType": 5}, "30101": {"id": 30101, "name": "猫女", "iconId": "item_fr_30101", "color": 1, "goodsType": 6}, "30102": {"id": 30102, "name": "青蛇", "iconId": "item_fr_30102", "color": 1, "goodsType": 6}, "30103": {"id": 30103, "name": "琵琶精", "iconId": "item_fr_30103", "color": 1, "goodsType": 6}, "30105": {"id": 30105, "name": "蔡文姬", "iconId": "item_fr_30105", "color": 2, "goodsType": 6}, "30106": {"id": 30106, "name": "小乔", "iconId": "item_fr_30106", "color": 2, "goodsType": 6}, "30107": {"id": 30107, "name": "紫霞仙子", "iconId": "item_fr_30107", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30202": {"id": 30202, "name": "赵飞燕", "iconId": "item_fr_30202", "color": 2, "goodsType": 6}, "30203": {"id": 30203, "name": "芈月", "iconId": "item_fr_30203", "color": 3, "goodsType": 6}, "30204": {"id": 30204, "name": "虞姬", "iconId": "item_fr_30204", "color": 3, "goodsType": 6}, "30205": {"id": 30205, "name": "孙尚香", "iconId": "item_fr_30205", "color": 3, "goodsType": 6}, "30206": {"id": 30206, "name": "牡丹仙子", "iconId": "item_fr_30206", "color": 3, "goodsType": 6}, "30207": {"id": 30207, "name": "何仙姑", "iconId": "item_fr_30207", "color": 2, "goodsType": 6}, "30301": {"id": 30301, "name": "孔雀公主", "iconId": "item_fr_30301", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30302": {"id": 30302, "name": "苏妲己", "iconId": "item_fr_30302", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30401": {"id": 30401, "name": "李清照", "iconId": "item_fr_30401", "color": 2, "goodsType": 6}, "30402": {"id": 30402, "name": "蝶恋花", "iconId": "item_fr_30402", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30403": {"id": 30403, "name": "女娲", "iconId": "item_fr_30403", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30404": {"id": 30404, "name": "蝎尾针", "iconId": "item_fr_30404", "color": 3, "goodsType": 6}, "30405": {"id": 30405, "name": "貂蝉", "iconId": "item_fr_30405", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30406": {"id": 30406, "name": "小龙女", "iconId": "item_fr_30406", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30407": {"id": 30407, "name": "嫦娥", "iconId": "item_fr_30407", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30408": {"id": 30408, "name": "九天玄女", "iconId": "item_fr_30408", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30410": {"id": 30410, "name": "西施", "iconId": "item_fr_30410", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30411": {"id": 30411, "name": "杨玉环", "iconId": "item_fr_30411", "color": 4, "closeSpecial": 1, "goodsType": 6}, "30412": {"id": 30412, "name": "王昭君", "iconId": "item_fr_30412", "color": 4, "closeSpecial": 1, "goodsType": 6}, "40001": {"id": 40001, "name": "彩云", "des": "普普通通的神器", "iconId": "item_40001", "color": 3, "goodsType": 8}, "40002": {"id": 40002, "name": "芭蕉扇", "des": "抗击晕神器", "iconId": "item_40002", "color": 4, "closeSpecial": 1, "goodsType": 8}, "40003": {"id": 40003, "name": "魔毯", "des": "抗闪避神器", "iconId": "item_40003", "color": 4, "closeSpecial": 1, "goodsType": 8}, "40004": {"id": 40004, "name": "风火轮", "des": "抗连击神器", "iconId": "item_40004", "color": 4, "closeSpecial": 1, "goodsType": 8}, "40005": {"id": 40005, "name": "御剑", "des": "抗反击神器", "iconId": "item_40005", "color": 4, "closeSpecial": 1, "goodsType": 8}, "40006": {"id": 40006, "name": "莲花座", "des": "抗暴击神器", "iconId": "item_40006", "color": 4, "closeSpecial": 1, "goodsType": 8}, "41001": {"id": 41001, "name": "绿色龙魂", "des": "绿色龙魂", "iconId": "item_41001", "color": 1, "goodsType": 10}, "41002": {"id": 41002, "name": "绿色凤凰", "des": "绿色凤凰", "iconId": "item_41002", "color": 1, "goodsType": 10}, "41003": {"id": 41003, "name": "绿色虎魂", "des": "绿色虎魂", "iconId": "item_41003", "color": 1, "goodsType": 10}, "41004": {"id": 41004, "name": "绿色玄武", "des": "绿色玄武", "iconId": "item_41004", "color": 1, "goodsType": 10}, "41005": {"id": 41005, "name": "绿色麒麟", "des": "绿色麒麟", "iconId": "item_41005", "color": 1, "goodsType": 10}, "41011": {"id": 41011, "name": "蓝色龙魂", "des": "蓝色龙魂", "iconId": "item_41011", "color": 2, "goodsType": 10}, "41012": {"id": 41012, "name": "蓝色凤凰", "des": "蓝色凤凰", "iconId": "item_41012", "color": 2, "goodsType": 10}, "41013": {"id": 41013, "name": "蓝色虎魂", "des": "蓝色虎魂", "iconId": "item_41013", "color": 2, "goodsType": 10}, "41014": {"id": 41014, "name": "蓝色玄武", "des": "蓝色玄武", "iconId": "item_41014", "color": 2, "goodsType": 10}, "41015": {"id": 41015, "name": "蓝色麒麟", "des": "蓝色麒麟", "iconId": "item_41015", "color": 2, "goodsType": 10}, "41021": {"id": 41021, "name": "紫色龙魂", "des": "紫色龙魂", "iconId": "item_41021", "color": 3, "goodsType": 10}, "41022": {"id": 41022, "name": "紫色凤凰", "des": "紫色凤凰", "iconId": "item_41022", "color": 3, "goodsType": 10}, "41023": {"id": 41023, "name": "紫色虎魂", "des": "紫色虎魂", "iconId": "item_41023", "color": 3, "goodsType": 10}, "41024": {"id": 41024, "name": "紫色玄武", "des": "紫色玄武", "iconId": "item_41024", "color": 3, "goodsType": 10}, "41025": {"id": 41025, "name": "紫色麒麟", "des": "紫色麒麟", "iconId": "item_41025", "color": 3, "goodsType": 10}, "41031": {"id": 41031, "name": "橙色龙魂", "des": "橙色龙魂", "iconId": "item_41031", "color": 4, "closeSpecial": 1, "goodsType": 10}, "41032": {"id": 41032, "name": "橙色凤凰", "des": "橙色凤凰", "iconId": "item_41032", "color": 4, "closeSpecial": 1, "goodsType": 10}, "41033": {"id": 41033, "name": "橙色虎魂", "des": "橙色虎魂", "iconId": "item_41033", "color": 4, "closeSpecial": 1, "goodsType": 10}, "41034": {"id": 41034, "name": "橙色玄武", "des": "橙色玄武", "iconId": "item_41034", "color": 4, "closeSpecial": 1, "goodsType": 10}, "41035": {"id": 41035, "name": "橙色麒麟", "des": "橙色麒麟", "iconId": "item_41035", "color": 4, "closeSpecial": 1, "goodsType": 10}, "41041": {"id": 41041, "name": "红色龙魂", "des": "红色龙魂", "iconId": "item_41041", "color": 5, "closeSpecial": 1, "goodsType": 10}, "41042": {"id": 41042, "name": "红色凤凰", "des": "红色凤凰", "iconId": "item_41042", "color": 5, "closeSpecial": 1, "goodsType": 10}, "41043": {"id": 41043, "name": "红色虎魂", "des": "红色虎魂", "iconId": "item_41043", "color": 5, "closeSpecial": 1, "goodsType": 10}, "41044": {"id": 41044, "name": "红色玄武", "des": "红色玄武", "iconId": "item_41044", "color": 5, "closeSpecial": 1, "goodsType": 10}, "41045": {"id": 41045, "name": "红色麒麟", "des": "红色麒麟", "iconId": "item_41045", "color": 5, "closeSpecial": 1, "goodsType": 10}, "-1": {"id": -1, "name": "", "des": "", "iconId": "", "color": 0, "closeSpecial": 0, "goodsType": 0, "type1": 0, "type1Son1List": [], "type2List": [], "type2Son": 0, "jumplist": []}}