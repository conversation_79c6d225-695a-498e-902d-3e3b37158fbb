import { _decorator, sp } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { GuideRouteEnum } from "./GuideDefine";
import { SpineUtil } from "../../platform/src/lib/utils/SpineUtil";
import { Sleep } from "../game/GameDefine";
import { FightModule, FightRouteItem } from "../module/fight/src/FightModule";
import { SpGuide103002 } from "../module/fight/src/FightConstant";
import { BattleReplayMessage } from "../game/net/protocol/Comm";
import { FightData } from "../game/fight/FightDefine";
import { UIMgr } from "../lib/ui/UIMgr";
import { AudioMgr } from "../../platform/src/AudioHelper";
const { ccclass, property } = _decorator;

@ccclass("TopHeroHelp")
export class TopHeroHelp extends BaseCtrl {
  spine_sc_xiong_mao: sp.Skeleton = null;

  start() {
    super.start();
    this.spine_sc_xiong_mao = this.getNode("spine_sc_xiong_mao").getComponent(sp.Skeleton);

    this.spine_sc_xiong_mao.node.active = false;
    TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopEventRoute, { from: 601, to: 601 }, async () => {
      this.spine_sc_xiong_mao.node.active = true;
      this.showAni();
      await Sleep(2);
      TipsMgr.topRouteCtrl.show(GuideRouteEnum.TopEventRoute, { from: 602, to: 602 }, async () => {
        FightModule.data.stepPass1030002 = SpGuide103002.SECOND_FIGHT;
        this.openFight();
        this.closeBack();
      });
    });
  }

  private openFight() {
    if (FightModule.data.stepPass1030002 == SpGuide103002.SECOND_FIGHT) {
      FightModule.api.passDiffBossChapter((res: BattleReplayMessage) => {
        let data: FightData = {
          fightData: JSON.parse(res.replay),
          win: res.win,
        };
        UIMgr.instance.showDialog(FightRouteItem.UIFightPage, { data }, null, () => {
          TipsMgr.setEnableTouch(true);
        });
      });
      return;
    }
  }

  showAni() {
    SpineUtil.playOneByOne(this.spine_sc_xiong_mao, "panda_appear", "panda_idle");
    AudioMgr.instance.playEffect(521);
  }

  on_click_btn_next() {}
}
