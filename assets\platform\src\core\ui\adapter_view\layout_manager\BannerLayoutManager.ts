import { _decorator, Node, UITransform } from "cc";

import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { LayoutManager } from "../LayoutManager";
import { ViewHolder } from "../ListAdapter";
import { Rect } from "../../../../lib/utils/Rect";
const log = Logger.getLoger(LOG_LEVEL.STOP);
export type OnPageScroll = (node: Node, position: number, currentPos: number, positionOffset: number) => void;

const { ccclass, property } = _decorator;
@ccclass("BannerLayoutManager")
export class BannerLayoutManager extends LayoutManager {
  private _curNode: Node;
  private _nextNode: Node;
  private _positionOffset: number = 0;
  private _onPageScroll: OnPageScroll;
  private _lastFoucsNode: Node;
  private _foucsPosition: number = 0;
  private _updateLayout: boolean = true;

  public set foucsPosition(value: number) {
    log.log(`select position ${value}`);
    this._foucsPosition = value;
    this._updateLayout = true;
    this.removeAllChildren();
    this.requestLayout();
  }
  public get foucsPosition(): number {
    return this._foucsPosition;
  }
  protected onAttach(): void {
    this.viewholders.wrapAroundIndex = true;
    this.scroller.damping = 0.1;
  }

  protected onLayout(changed: boolean, lastRect: Rect, offsetX: number, offsetY: number, isFling: boolean): Rect {
    for (let i = 0; i < this.children.size(); i++) {
      let child = this.children.get(i);
      let height = child.getComponent(UITransform).height;
      let anchorY = child.getComponent(UITransform).anchorY;
      let childX = child.position.x + offsetX;
      let y = this.getBorderBottom() + height * anchorY;
      this.updateChildPosition(child, childX, y);
    }
    if (this.children.size() == 0 || this._updateLayout) {
      this._updateLayout = false;
      let newNode = this.addNode(this._foucsPosition);
      if (newNode) {
        let width = newNode.getComponent(UITransform).width;
        let height = newNode.getComponent(UITransform).height;
        let anchorX = newNode.getComponent(UITransform).anchorX;
        let anchorY = newNode.getComponent(UITransform).anchorY;
        let x = this.getBorderLeft() + anchorX * width;
        let y = this.getBorderBottom() + height * anchorY;
        this.updateChildPosition(newNode, x, y);
        this._lastFoucsNode = newNode;
      } else {
        return lastRect;
      }
    }
    return new Rect(
      this.getBorderLeft(),
      this.getNodeTop(this.children.peekFront()),
      this.getBorderRight(),
      this.getNodeBottom(this.children.peekRear())
    );
    // if (offsetX > 0) {
    //   // 向右滚动
    //   let first = this.children.peekFront();
    //   for (; first && this.getNodeLeft(first) > this.getBorderLeft(); first = this.children.peekFront()) {
    //     let newNode = this.addNodeToHead();
    //     if (newNode) {
    //       let width = newNode.getComponent(UITransform).width;
    //       let height = newNode.getComponent(UITransform).height;
    //       let anchorX = newNode.getComponent(UITransform).anchorX;
    //       let anchorY = newNode.getComponent(UITransform).anchorY;
    //       let x = this.getNodeLeft(first) - (1 - anchorX) * width;
    //       let y = this.getBorderBottom() + anchorY * height;
    //       this.setChildPosition(newNode, x, y);
    //     } else {
    //       break;
    //     }
    //   }
    // } else {
    //   // 向左滚动
    //   let rear = this.children.peekRear();
    //   for (; rear && this.getNodeRight(rear) < this.getBorderRight(); rear = this.children.peekRear()) {
    //     let newNode = this.addNodeToRear();
    //     if (newNode) {
    //       let width = newNode.getComponent(UITransform).width;
    //       let height = newNode.getComponent(UITransform).height;
    //       let anchorX = newNode.getComponent(UITransform).anchorX;
    //       let anchorY = newNode.getComponent(UITransform).anchorY;
    //       let x = this.getNodeRight(rear) + anchorX * width;
    //       let y = this.getBorderBottom() + anchorY * height;
    //       log.log(`set child position:${x}-${y}`);
    //       this.setChildPosition(newNode, x, y);
    //     } else {
    //       break;
    //     }
    //   }
    // }
  }
  protected updateChildPosition(child: Node, x: number, y: number): void {
    super.updateChildPosition(child, x, y);
    this.onChildrenScroll(child);
  }

  protected onChildrenScroll(child: Node): void {
    let width = child.getComponent(UITransform).width;
    let offsetX = 0 - child.position.x;
    let positionOffset = offsetX / (width - 5); //-5是为了容错
    if (positionOffset >= 1 || positionOffset <= -1) {
      positionOffset = positionOffset > 0 ? 1 : -1;
    }
    //   child.getComponent(UIOpacity).opacity = 55;
    // } else {
    //   let opacity = 55 + (1 - Math.abs(positionOffset)) * 200;
    //   child.getComponent(UIOpacity).opacity = opacity;
    // }
    if (Math.abs(positionOffset) < 0.5) {
      let index = child.parent.children.length - 1;
      child.setSiblingIndex(index);
    }
    this._onPageScroll?.(child, child.getComponent(ViewHolder).position, this.foucsPosition, positionOffset);
  }
  protected onFlingComplete(): void {}
  protected onScrollComplete(): void {}

  public set onPageScroll(onPageScroll: OnPageScroll) {
    this._onPageScroll = onPageScroll;
  }
}
