import { _decorator, Label, sp, tween, UIOpacity, Node } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { PupilMessage } from "../game/net/protocol/Pupil";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { PupilHeader } from "../module/pupil/src/prefab/ui/PupilHeader";
import { PupilModule } from "../module/pupil/src/PupilModule";
import FmUtils from "../lib/utils/FmUtils";
import { SpineUtil } from "../../platform/src/lib/utils/SpineUtil";
const { ccclass, property } = _decorator;

@ccclass("TopPupilMarry")
export class TopPupilMarry extends BaseCtrl {
  private _pupilMessage: PupilMessage = null;

  init(args: any): void {
    this._pupilMessage = args;
  }

  protected onLoad(): void {}

  start() {
    super.start();

    // 动画结束，关闭页面
    const dt = SpineUtil.getSpineDuration(this.getNode("弟子被结伴成功").getComponent(sp.Skeleton), "animation");
    tween(this.node)
      .delay(dt)
      .call(() => {
        this.closeBack();
      })
      .start();

    const name1 = JsonMgr.instance.jsonList.c_pupilName[this._pupilMessage.ownInfo.nameId];
    this.getNode("lbl_name_left").getComponent(Label).string = name1?.des;

    const name2 = JsonMgr.instance.jsonList.c_pupilName[this._pupilMessage.partnerInfo.nameId];
    this.getNode("lbl_name_right").getComponent(Label).string = name2?.des;

    const nodeHeadLeft = this.getNode("PupilHeader_left");
    const nodeHeadRight = this.getNode("PupilHeader_right");

    nodeHeadLeft.getComponent(PupilHeader).setHeaderByNameId(this._pupilMessage.ownInfo.nameId);
    nodeHeadRight.getComponent(PupilHeader).setHeaderByNameId(this._pupilMessage.partnerInfo.nameId);

    // 自己的奖励道具
    let config1 = PupilModule.data.getConfigPupil(this._pupilMessage.ownInfo.talentId);
    FmUtils.setItemIcon(this.getNode("bg_icon_left"), config1.rewardList[0]);
    this.getNode("lbl_reward_num_left").getComponent(Label).string = `+${config1.rewardList[1]}`;

    // 对方的奖励道具
    let config2 = PupilModule.data.getConfigPupil(this._pupilMessage.partnerInfo.talentId);
    FmUtils.setItemIcon(this.getNode("bg_icon_right"), config2.rewardList[0]);
    this.getNode("lbl_reward_num_right").getComponent(Label).string = `+${config2.rewardList[1]}`;

    this.aniOpacity(this.getNode("bg_reward_item"));
    this.aniOpacity(nodeHeadLeft);
    this.aniOpacity(nodeHeadRight);
    this.aniOpacity(this.getNode("lbl_name_left"));
    this.aniOpacity(this.getNode("lbl_name_right"));
  }

  private aniOpacity(node: Node) {
    const opacityItem = node.getComponent(UIOpacity);
    opacityItem.opacity = 0;
    tween(opacityItem).delay(0.3).to(0.3, { opacity: 255 }).start();
  }
}
