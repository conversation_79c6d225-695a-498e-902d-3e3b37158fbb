export class Deque<T> {
  private items: T[];

  constructor() {
    this.items = [];
  }

  // 在队列前端添加元素
  addFront(element: T): void {
    this.items.unshift(element);
  }

  // 在队列尾部添加元素
  addRear(element: T): void {
    this.items.push(element);
  }

  // 从队列前端移除元素
  removeFront(): T | undefined {
    return this.items.shift();
  }

  // 从队列尾部移除元素
  removeRear(): T | undefined {
    return this.items.pop();
  }

  // 获取队列前端的元素
  peekFront(): T | undefined {
    return this.items[0];
  }

  // 获取队列尾部的元素
  peekRear(): T | undefined {
    return this.items[this.items.length - 1];
  }

  // 判断队列是否为空
  isEmpty(): boolean {
    return this.items.length === 0;
  }

  // 返回队列的大小
  size(): number {
    return this.items.length;
  }

  // 清空队列
  clear(): void {
    this.items = [];
  }

  // 打印队列元素
  print(): void {
    console.log(this.items);
  }
  get(index: number): T {
    return this.items[index];
  }
}

// // 使用示例
// const deque = new Deque<number>();
// deque.addFront(1);
// deque.addRear(2);
// deque.addFront(0);
// deque.print(); // 输出: [0, 1, 2]
// deque.print(); // 输出: [1]

export class SyncQueue<T> {
  private queue: T[] = [];
  private maxSize: number;
  private waitingProducers: (() => void)[] = [];
  private waitingConsumers: (() => void)[] = [];

  constructor(maxSize: number) {
    this.maxSize = maxSize;
  }

  // 生产者：添加一个元素到队列
  async enqueue(item: T, continueOnFailure: boolean = true): Promise<void> {
    if (this.queue.length >= this.maxSize) {
      if (!continueOnFailure) {
        // 队列已满且不继续等待，直接返回失败信息
        return;
      }

      // 队列已满，生产者需要等待
      await new Promise<void>((resolve) => {
        this.waitingProducers.push(resolve);
      });
    }

    // 插入数据
    this.queue.push(item);

    // 如果有消费者在等待，唤醒一个消费者
    if (this.waitingConsumers.length > 0) {
      const consumerResolve = this.waitingConsumers.shift();
      consumerResolve?.();
    }
  }

  // 消费者：从队列中消费一个元素
  async dequeue(): Promise<T> {
    if (this.queue.length === 0) {
      // 队列为空，消费者需要等待
      await new Promise<void>((resolve) => {
        this.waitingConsumers.push(resolve);
      });
    }

    const item = this.queue.shift()!;

    // 如果有生产者在等待，唤醒一个生产者
    if (this.waitingProducers.length > 0) {
      const producerResolve = this.waitingProducers.shift();
      producerResolve?.();
    }

    return item;
  }
}

// 模拟生产者和消费者
// const queue = new SyncQueue<number>(5); // 创建一个最大容量为5的队列
// // 生产者线程
// async function producer() {
//   let item = 1;
//   while (true) {
//     await queue.produce(item, false); // 这里传递 `false` 表示队列满时直接返回失败信息
//     item++;
//     await new Promise((resolve) => setTimeout(resolve, 1000)); // 模拟生产间隔
//   }
// }
//
// // 消费者线程
// async function consumer() {
//   while (true) {
//     const item = await queue.consume();
//     await new Promise((resolve) => setTimeout(resolve, 1500)); // 模拟消费间隔
//   }
// }
//
// 启动生产者和消费者
// producer();
// consumer();
