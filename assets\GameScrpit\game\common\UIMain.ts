import {
  _decorator,
  instantiate,
  Label,
  math,
  Node,
  ProgressBar,
  RichText,
  sp,
  Tween,
  tween,
  UITransform,
  v2,
  v3,
  Vec3,
} from "cc";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { PlayerModule } from "../../module/player/PlayerModule";
import { ItemEnum } from "../../lib/common/ItemEnum";
import Formate from "../../lib/utils/Formate";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { BadgeMgr, BadgeType } from "../mgr/BadgeMgr";
import ToolExt from "./ToolExt";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { UIMgr } from "../../lib/ui/UIMgr";
import { PlayerRouteName } from "../../module/player/PlayerConstant";
import { CityRouteName } from "../../module/city/CityConstant";
import { MailRouteItem } from "../../module/mail/MailRoute";
import { GameDirector } from "../GameDirector";
import { SystemOpenEnum } from "../GameDefine";
import TipMgr from "../../lib/tips/TipMgr";
import { FightRouteItem } from "../../module/fight/src/FightModule";
import MsgMgr from "../../lib/event/MsgMgr";
import MsgEnum from "../event/MsgEnum";
import { HeroRouteItem } from "../../module/hero/HeroRoute";
import { ClubModule } from "../../module/club/ClubModule";
import { ClubRouteItem } from "../../module/club/ClubRoute";
import { PlayerEvent } from "../../module/player/PlayerEvent";
import { MainTaskModule } from "../../module/mainTask/MainTaskModule";
import { MainTaskRouteName } from "../../module/mainTask/MainTaskRoute";
import { MainTaskPassResponse } from "../net/protocol/MainTask";
import { NodeTool } from "../../lib/utils/NodeTool";
import { ShengDianRouteItem } from "../../module/sheng_dian/ShengDianRoute";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { MailAudioName } from "../../module/mail/MailConfig";
import { KnappsackAudioName } from "../../module/player/PlayerConfig";
import { HeroAudioName } from "../../module/hero/HeroConfig";
import { ClubAudioName } from "../../module/club/ClubConfig";
import { RewardRouteEnum } from "../../ext_reward/RewardDefine";
import { VecUtil } from "../../lib/utils/VecUtil";
import { SpineUtil } from "../../../platform/src/lib/utils/SpineUtil";
import { TaskType } from "../../module/mainTask/MainTaskData";
import { JsonMgr } from "../mgr/JsonMgr";
import { LangMgr } from "../mgr/LangMgr";
import { CityModule } from "../../module/city/CityModule";
import { EventActionModule } from "../../module/event_action/src/EventActionModule";
import { StartUp } from "../../lib/StartUp";
import GuideMgr from "../../ext_guide/GuideMgr";
import FmUtils from "../../lib/utils/FmUtils";
import { MessageComponent } from "../../../platform/src/core/ui/components/MessageComponent";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { UIMainRank } from "../ui/ui_main_rank/UIMainRank";
import { UIDailyChallenge } from "../../module/daily_challenge/src/prefab/ui/UIDailyChallenge";
const { ccclass, property } = _decorator;

const log = Logger.getLoger(LOG_LEVEL.DEBUG);

@ccclass("UIMain")
export class UIMain extends BaseCtrl {
  protected autoBind: boolean = true;

  @property(Node)
  nodeTop: Node = null;

  @property(Node)
  nodeRight: Node = null;

  @property(Node)
  nodeBottom: Node = null;

  @property(Node)
  nodeEnergyItem: Node;

  @property(Node)
  lbl_azst_num: Node;

  nodeLeft: Node = null;

  /** 气运数量 */
  lblItem1: Label;

  /** 仙玉数量 */
  lblItem6: Label;

  /** 繁荣度 */
  lblBloom: Label;

  /** 战力 */
  lblPower: Label;

  /** 名字 */
  lblName: Label;

  /** 等级 */
  lblLevel: RichText;

  /** 五族荣耀 */
  btnWuZuRongYao: Node;

  /** 五族荣耀 */
  btnWuZuYuLan: Node;

  /** 邮件 */
  btnEmail: Node;

  /** 战斗 */
  btnFight: Node;

  /** 背包 */
  btnKnapsack: Node;

  /** 气运飞行终点 */
  energyFlyToPosition: Vec3;

  /** 挑战关闭按钮 */
  btnTiaoZhanClose: Node;

  /** 菜单导航 */
  layoutNav: Node;

  /** 当前选中菜单 */
  _curBtnIndex: number = -1;

  /** 游历 */
  btnYouLi: Node;

  /** 演武场 */
  btnYanWuChang: Node;

  /** 天荒古镜 */
  btnTianHuangGuJing: Node;

  // 无操作时间
  idleTime = 0;

  // 三界小家
  btnSanjiexiaojia: Node;

  // 自动点击任务解锁
  btn_task_auto: Node;

  spine_task_sweep: sp.Skeleton;

  protected onLoad(): void {
    super.onLoad();

    // 绑定组件
    this.lblItem1 = this.getNode("lbl_item1").getComponent(Label);
    this.lblItem6 = this.getNode("lbl_item6").getComponent(Label);
    this.lblBloom = this.getNode("lbl_bloom").getComponent(Label);
    this.lblName = NodeTool.findCompent(this.nodeTop, "lbl_name", Label);
    this.lblPower = this.getNode("lbl_power").getComponent(Label);
    this.lblLevel = this.getNode("lbl_level").getComponent(RichText);
    this.node.getComponentInChildren(Label);
    this.btnWuZuRongYao = this.getNode("btn_wu_zu_rong_yao");
    this.btnWuZuYuLan = this.getNode("btn_wu_zu_yu_lan");
    this.btnEmail = this.getNode("btn_email");
    this.btnFight = this.getNode("btn_fight");
    this.btnKnapsack = this.getNode("btn_knapsack");

    this.btnTiaoZhanClose = this.getNode("btn_tiao_zhan_close");
    this.layoutNav = this.getNode("layout_nav");

    this.btnYouLi = this.getNode("btn_you_li");
    this.btnYanWuChang = this.getNode("btn_yan_wu_chang");
    this.btnTianHuangGuJing = this.getNode("btn_tian_huang_gu_jing");

    this.nodeLeft = this.getNode("node_left");
    this.btnSanjiexiaojia = this.getNode("btn_sanjiexiaojia");

    this.spine_task_sweep = this.getNode("spine_task_sweep").getComponent(sp.Skeleton);
    this.btn_task_auto = this.getNode("btn_task_auto");

    // 初始化状态
    this.btnTiaoZhanClose.active = false;

    // 红点
    BadgeMgr.instance.setBadgeId(this.btnWuZuRongYao, BadgeType.UIMajorCity.btn_wuzurongyao.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_territory"), BadgeType.UITerritory.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_home"), BadgeType.UIMajorCity.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_hero"), BadgeType.UIHeroMain.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_tiao_zhan"), BadgeType.btn_tiao_zhan.id);
    BadgeMgr.instance.setBadgeId(this.btnYanWuChang, BadgeType.btn_tiao_zhan.btn_yan_wu_chang.id);
    BadgeMgr.instance.setBadgeId(this.btnYouLi, BadgeType.btn_tiao_zhan.btn_youli.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_club"), BadgeType.UIClubMain.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_post"), BadgeType.btn_tiao_zhan.btn_post.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_email"), BadgeType.UIMain.btn_email.id);
    BadgeMgr.instance.setBadgeId(this.btnSanjiexiaojia, BadgeType.UIMajorCity.btn_sanjiexiaojia.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_wu_zu_yu_lan"), BadgeType.UIMajorCity.btn_wu_zu_yu_lan.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_pai_hang"), BadgeType.UIMajorCity.btn_pai_hang.id);
  }

  start() {
    super.start();

    this.energyFlyToPosition = this.getNode("icon_item1").worldPosition;
    this.energyFlyToPosition = this.node.getComponent(UITransform).convertToNodeSpaceAR(this.energyFlyToPosition);

    this.getNode("btn_task").getChildByName("finger").active = false;

    // 更新用户信息
    this.updateUserInfo();

    this.updatePower();
    this.onPlayerLevelUpdate();
    this.changeBtnXiaoTouIcon();

    this.setCurBtnState(UIMgr.instance.getLastPageInfo().name);

    this.getNode("lbl_task_auto").getComponent(Label).string = LangMgr.txMsgCode(179, []);

    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.updateUserInfo, this);
    MsgMgr.on(PlayerEvent.ON_PLAYER_AVATARLIST_CHANGE, this.updateUserInfo, this);
    MsgMgr.on(MsgEnum.UIReady, this.updatePower, this);
    MsgMgr.on(MsgEnum.ON_UINAVIGATE_MAIN_CLOSE_GO_ZHAOGE, this.setCurBtnState, this);
    MsgMgr.on(MsgEnum.ON_USER_CLICK, this.userClick, this);
    MsgMgr.on(MsgEnum.ON_MAINTASK_UPDATE, this.updateMainTask, this);
    MsgMgr.on(MsgEnum.ON_PLAYER_LEVEL_UP, this.onPlayerLevelUpdate, this);
    MsgMgr.on(MsgEnum.ON_ENERGYFACTORY_FLY, this.flyEnergyFun, this);
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upItem, this);
    MsgMgr.on(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this.changeBtnXiaoTouIcon, this);
    MsgMgr.on(MsgEnum.ON_UIMAIN_HIDE_ANI, this.aniHide, this);
    MsgMgr.on(MsgEnum.ON_UIMAIN_SHOW_ANI, this.aniShow, this);
  }

  protected onDestroy(): void {
    super.onDestroy();
    MsgMgr.off(MsgEnum.ON_SANJIE_EVENT_DATA_UPDATE, this.changeBtnXiaoTouIcon, this);
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.updateUserInfo, this);
    MsgMgr.off(PlayerEvent.ON_PLAYER_AVATARLIST_CHANGE, this.updateUserInfo, this);
    MsgMgr.off(MsgEnum.UIReady, this.updatePower, this);
    MsgMgr.off(MsgEnum.ON_UINAVIGATE_MAIN_CLOSE_GO_ZHAOGE, this.setCurBtnState, this);

    MsgMgr.off(MsgEnum.ON_USER_CLICK, this.userClick, this);
    MsgMgr.off(MsgEnum.ON_MAINTASK_UPDATE, this.updateMainTask, this);
    MsgMgr.off(MsgEnum.ON_PLAYER_LEVEL_UP, this.onPlayerLevelUpdate, this);
    MsgMgr.off(MsgEnum.ON_ENERGYFACTORY_FLY, this.flyEnergyFun, this);
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.upItem, this);
    MsgMgr.off(MsgEnum.ON_UIMAIN_HIDE_ANI, this.aniHide, this);
    MsgMgr.off(MsgEnum.ON_UIMAIN_SHOW_ANI, this.aniShow, this);
  }

  private changeBtnXiaoTouIcon() {
    let pageInfo = UIMgr.instance.getLastPageInfo();
    if (pageInfo.name !== CityRouteName.UIGameMap) {
      // this.getNode("btn_xiao_tou_icon").active = false;
      return;
    }
    let eventMap = EventActionModule.data.eventTrainMessage.eventMap;
    let db = JsonMgr.instance.jsonList.c_event2;
    for (let i in eventMap) {
      if (db[i].type == 1) {
        // this.getNode("btn_xiao_tou_icon").active = true;
        return;
      }
    }

    // this.getNode("btn_xiao_tou_icon").active = false;
  }

  private upItem() {
    this.lbl_azst_num
      .getComponent(MessageComponent)
      .setMessageId(535, [Formate.format(PlayerModule.data.getItemNum(1082))]);

    if (PlayerModule.data.getItemNum(1082) > 0) {
      this.lbl_azst_num.getComponent(Label).color = math.color("00af04");
    } else {
      this.lbl_azst_num.getComponent(Label).color = math.color("e64d4d");
    }
  }

  protected update(dt: number): void {
    if (GuideMgr.isGuiding) {
      this.getNode("btn_task").getChildByName("finger").active = false;
      this.idleTime = 0;
      return;
    }

    if (UIMgr.instance.getLastPageInfo().name !== CityRouteName.UIGameMap) {
      return;
    }

    this.idleTime += dt;
    if (TipsMgr.topRouteCtrl.routeHistory.length) {
      this.idleTime = 0;
      return;
    }

    if (this.idleTime > 10) {
      this.idleTime = 0;

      this.getNode("btn_task").getChildByName("finger").active = true;
    }
  }

  /**
   * 玩家等级更新
   */
  onPlayerLevelUpdate() {
    if (UIMgr.instance.getLastPageInfo().name === CityRouteName.UIGameMap) {
      this.btnWuZuRongYao.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.WZRY_五族荣耀);
      this.btnSanjiexiaojia.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.SJXJ_三界小家);
      this.getNode("btn_pai_hang").active = GameDirector.instance.isSystemOpen(SystemOpenEnum.RANK_排行);
    }
  }

  /**飞行气运 */
  private flyEnergyFun(energyPosition: Vec3) {
    const endPos = this.getNode("lbl_item1").getWorldPosition();
    let nodeNew = instantiate(this.nodeEnergyItem);
    nodeNew.active = true;
    nodeNew.parent = this.node;
    nodeNew.setWorldPosition(v3(energyPosition.x, energyPosition.y, 0));

    // 旋转角度
    let vDirection = v2(endPos.x - energyPosition.x, endPos.y - energyPosition.y);
    nodeNew.angle = VecUtil.calculateAngleWithXAxis(vDirection) - 90;

    // 收拖尾
    tween(NodeTool.findByName(nodeNew, "bg_qiyun_tuowei"))
      .set({ scale: v3(0, 0.5, 1) })
      .to(0.1, { scale: v3(1.2, 0.75, 1) })
      .delay(0.3)
      .to(0.2, { scale: v3(0, 0.75, 1) })
      .start();

    tween(nodeNew)
      .to(1, { worldPosition: endPos }, { easing: "quintOut" })
      .to(0.2, { scale: v3(0.6, 0.6, 1) })
      .destroySelf()
      .start();
  }

  userClick() {
    this.idleTime = 0;
    this.getNode("btn_task").getChildByName("finger").active = false;
  }

  /**
   * 更新用户信息，包含道具
   */
  updateUserInfo(id: number = 0) {
    this.lblItem1.string = Formate.format(PlayerModule.data.getItemNum(ItemEnum.气运_1));
    this.lblItem6.string = Formate.format(PlayerModule.data.getItemNum(ItemEnum.仙玉_6));
    this.lblBloom.string = "繁荣度：" + Formate.format(PlayerModule.data.playerBattleAttrResponse.speed);
    this.lblName.string = PlayerModule.data.getPlayerInfo().nickname;
    this.lblPower.string = Formate.format(PlayerModule.data.playerBattleAttrResponse.power);

    const configLeader = PlayerModule.data.getConfigLeaderData(PlayerModule.data.getPlayerInfo().level);
    this.lblLevel.string = configLeader.jingjie2;

    let data = ToolExt.newPlayerBaseMessage();
    data.avatarList = PlayerModule.data.getMyAvatarList();
    data.vipLevel = PlayerModule.data.getPlayerInfo().vipLevel;
    FmUtils.setHeaderNode(this.getNode("btn_header"), data);

    if (id === 1) {
      // 动画播放
      if (PlayerModule.data.getItemNum(ItemEnum.气运_1) > 1) {
        tween(this.lblItem1.node)
          .to(0.1, { scale: v3(1.5, 1.5, 1) })
          .to(0.1, { scale: v3(1, 1, 1) })
          .start();
      }
    }
  }

  updateMainTask() {
    let needCount = MainTaskModule.service.getNeedCount();
    let complateNum = MainTaskModule.service.getCompleteNum();

    const lbl_task_finish = this.getNode("lbl_task_finish");
    const lbl_task = this.getNode("lbl_task");

    lbl_task_finish.active = false;
    lbl_task.active = false;

    if (needCount <= complateNum) {
      lbl_task_finish.active = true;
      lbl_task_finish.getComponent(Label).string = MainTaskModule.service.getTaskDesc();
      SpineUtil.playSpine(this.spine_task_sweep, "task_lasts", true);
    } else {
      lbl_task.active = true;
      lbl_task.getComponent(Label).string = MainTaskModule.service.getTaskDesc();
      this.spine_task_sweep.node.active = false;
    }

    // 更新女娲自动点击图标
    this.updateAutoTaskIcon();

    // 更新自动点击任务
    this.updateTaskClick();
  }

  // 女娲自动点击图标
  private updateAutoTaskIcon() {
    let pageInfo = UIMgr.instance.getLastPageInfo();
    if (pageInfo.name !== CityRouteName.UIGameMap) {
      // this.getNode("btn_xiao_tou_icon").active = false;
      return;
    }

    const cfgBuildCrypt = JsonMgr.instance.getDefault(JsonMgr.instance.jsonList.c_buildCrystal);

    if (!CityModule.data.energyFactoryMsg.autoTake) {
      if (CityModule.data.energyFactoryMsg.autoStateCount > cfgBuildCrypt.openIcon) {
        this.btn_task_auto.active = true;
        if (!this.btn_task_auto.active) {
          SpineUtil.playSpine(this.getNode("spine_tubiaoin_task").getComponent(sp.Skeleton), "action", false);
        }
        return;
      }
    }
  }

  /**
   * 显示点击女娲任务
   */
  private updateTaskClick() {
    const cfgTaskMain = MainTaskModule.data.getConfigTaskMain(MainTaskModule.data.mainTaskMsg.taskTimelineId);
    if ([TaskType.点击女娲_1, TaskType.点击女娲_64].includes(cfgTaskMain.taskId) == false) {
      return;
    }

    log.info("updateTaskClick");
    let complateNum = MainTaskModule.service.getCompleteNum();
    if (complateNum == 0) {
      return;
    }

    let needCount = MainTaskModule.service.getNeedCount();
    if (complateNum > needCount) {
      complateNum = needCount;
    }
    const node_task_click = this.getNode("node_task_click");
    node_task_click.getChildByName("progress_task_auto").getComponent(ProgressBar).progress = complateNum / needCount;
    const cfgTask = JsonMgr.instance.jsonList.c_task[TaskType.点击女娲_64];
    node_task_click.getChildByName("lbl_progress").getComponent(Label).string = cfgTask.des.replace(
      "s%",
      `${complateNum}/${needCount}`
    );

    // 出现动画
    if (!node_task_click.active) {
      node_task_click.active = true;
      const spine_tubiaoin = this.getNode("spine_tubiaoin").getComponent(sp.Skeleton);
      SpineUtil.playSpine(spine_tubiaoin, "action", false);
    }

    Tween.stopAllByTarget(node_task_click);
    tween(node_task_click)
      .delay(3)
      .call(() => {
        node_task_click.active = false;
      })
      .start();
  }

  updatePower() {
    this.idleTime = 0;
    let pageInfo = UIMgr.instance.getLastPageInfo();
    if (pageInfo.name === CityRouteName.UIGameMap) {
      this.btnSanjiexiaojia.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.SJXJ_三界小家);
      this.getNode("btn_pai_hang").active = GameDirector.instance.isSystemOpen(SystemOpenEnum.RANK_排行);
      this.btnWuZuRongYao.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.WZRY_五族荣耀);
      this.btnWuZuYuLan.active = GameDirector.instance.isSystemOpen(SystemOpenEnum.WZYL_五族预览);
      this.btnEmail.active = false;
      this.btnFight.active = true;
      this.btnKnapsack.active = true;
      this.getNode("btn_gong_lue").active = false;
      // this.updateMainTask();
      this.changeBtnXiaoTouIcon();
    } else if (pageInfo.name === PlayerRouteName.UITerritory) {
      this.btnWuZuRongYao.active = false;
      this.btn_task_auto.active = false;
      this.btnWuZuYuLan.active = false;
      this.btnEmail.active = true;
      this.btnFight.active = false;
      this.btnKnapsack.active = true;
      this.btnSanjiexiaojia.active = false;
      this.getNode("btn_pai_hang").active = false;
      this.getNode("btn_gong_lue").active = true;
      this.changeBtnXiaoTouIcon();
    }
    this.updateMainTask();
    // 只显示导航
    if (["", ClubRouteItem.UIClubMain].includes(pageInfo.name)) {
      this.nodeTop.active = false;
      this.nodeRight.active = false;
      this.nodeBottom.active = true;
      this.getNode("node_left").active = false;
      this.getNode("btn_task").active = false;
    }

    if (["", CityRouteName.UICityDetail].includes(pageInfo.name)) {
      this.setHideAll(false);
    }

    if (["", PlayerRouteName.UITerritory, CityRouteName.UIGameMap].includes(pageInfo.name)) {
      this.setHideAll(true);
    }
  }

  setHideAll(value: boolean) {
    this.nodeTop.active = value;
    this.nodeRight.active = value;
    this.nodeLeft.active = value;
    this.nodeBottom.active = value;
    this.getNode("btn_task").active = value;
  }

  async on_click_btn_bloom_bg() {
    const pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_MAINPAGE, "prefab/TipBloom");

    TipsMgr.showTipPage(BundleEnum.BUNDLE_G_MAINPAGE, "prefab/TipBloom", () => {
      this.assetMgr.releaseOne(BundleEnum.BUNDLE_G_MAINPAGE, "prefab/TipBloom");
    });
  }

  on_click_btn_header() {
    UIMgr.instance.showDialog(PlayerRouteName.UIPlayerMsg);
  }

  on_click_btn_wu_zu_rong_yao() {
    AudioMgr.instance.playEffect(1880);
    UIMgr.instance.showDialog(CityRouteName.UICityLvReward);
  }

  on_click_btn_email() {
    AudioMgr.instance.playEffect(MailAudioName.Effect.点击邮件图标);
    UIMgr.instance.showDialog(MailRouteItem.UIMailBox);
  }

  on_click_btn_fight() {
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.FIGHT_关卡系统)) {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.FIGHT_关卡系统));
      return;
    }

    TipsMgr.setEnableTouch(false, 1);
    UIMgr.instance.showDialog(FightRouteItem.UILevelGame, null, null, () => {
      TipsMgr.setEnableTouch(true);
    });
  }

  on_click_btn_knapsack() {
    AudioMgr.instance.playEffect(KnappsackAudioName.Effect.点击背包图标);
    UIMgr.instance.showDialog("UIKnappsack");
  }

  on_click_btn_tiao_zhan_close() {
    this.btnTiaoZhanClose.active = false;
  }

  private setCurBtnState(pageName: string) {
    let tabId: number = -1;
    if (pageName === "UITerritory") {
      tabId = 0;
    } else if (pageName === "UIHeroMain") {
      tabId = 1;
    } else if (pageName === "UIGameMap") {
      tabId = 2;
    } else if (pageName === "btnTiaoZhan") {
      tabId = 3;
    } else if (pageName === "UIClubMain") {
      tabId = 4;
    } else {
      return;
    }

    this._curBtnIndex = tabId;

    this.updateTabStatus(
      this.layoutNav.children[0],
      0,
      GameDirector.instance.isSystemOpen(SystemOpenEnum.TERRAIN_领地),
      tabId
    );
    this.updateTabStatus(
      this.layoutNav.children[1],
      1,
      GameDirector.instance.isSystemOpen(SystemOpenEnum.HERO_战将系统),
      tabId
    );
    this.updateTabStatus(this.layoutNav.children[2], 2, true, tabId);

    this.updateTabStatus(
      this.layoutNav.children[4],
      4,
      GameDirector.instance.isSystemOpen(SystemOpenEnum.CLUB_战盟系统),
      tabId
    );

    this.btnTiaoZhanClose.active = tabId == 3;

    let isOpen = !GameDirector.instance.isSystemOpen(SystemOpenEnum.TRAVEL_游历系统);
    this.btnYouLi.getChildByName("bg_mask").active = isOpen;
    if (isOpen) {
      this.btnYouLi.getChildByPath("bg_mask/lbl_jiesuotiaojian").getComponent(Label).string =
        GameDirector.instance.getUnLockMsg(SystemOpenEnum.TRAVEL_游历系统);
    }

    isOpen = !GameDirector.instance.isSystemOpen(SystemOpenEnum.AZST_演武场);
    this.btnYanWuChang.getChildByName("bg_mask").active = isOpen;
    if (isOpen) {
      this.btnYanWuChang.getChildByPath("bg_mask/lbl_jiesuotiaojian").getComponent(Label).string =
        GameDirector.instance.getUnLockMsg(SystemOpenEnum.AZST_演武场);
    }

    isOpen = !GameDirector.instance.isSystemOpen(SystemOpenEnum.HUNT_天荒古境);
    this.btnTianHuangGuJing.getChildByName("bg_mask").active = isOpen;
    if (isOpen) {
      this.btnTianHuangGuJing.getChildByPath("bg_mask/lbl_jiesuotiaojian").getComponent(Label).string =
        GameDirector.instance.getUnLockMsg(SystemOpenEnum.HUNT_天荒古境);
    }

    isOpen = !GameDirector.instance.isSystemOpen(SystemOpenEnum.SD_圣殿);
    this.getNode("btn_sheng_dian").getChildByName("bg_mask").active = isOpen;
    if (isOpen) {
      this.getNode("btn_sheng_dian").getChildByPath("bg_mask/lbl_jiesuotiaojian").getComponent(Label).string =
        GameDirector.instance.getUnLockMsg(SystemOpenEnum.SD_圣殿);
    }

    isOpen = !GameDirector.instance.isSystemOpen(SystemOpenEnum.POST_驿站系统);
    this.getNode("btn_post").getChildByName("bg_mask").active = isOpen;
    if (isOpen) {
      this.getNode("btn_post").getChildByPath("bg_mask/lbl_jie_suo_yi_zhan").getComponent(Label).string =
        GameDirector.instance.getUnLockMsg(SystemOpenEnum.POST_驿站系统);
    }

    let tiaoZhanBool = false;
    let list = [
      SystemOpenEnum.TRAVEL_游历系统,
      SystemOpenEnum.AZST_演武场,
      SystemOpenEnum.HUNT_天荒古境,
      SystemOpenEnum.SD_圣殿,
      SystemOpenEnum.POST_驿站系统,
    ];

    for (let i = 0; i < list.length; i++) {
      tiaoZhanBool = GameDirector.instance.isSystemOpen(list[i]);
      if (tiaoZhanBool) {
        break;
      }
    }

    this.updateTabStatus(this.layoutNav.children[3], 3, tiaoZhanBool, tabId);
  }

  private updateTabStatus(nodeTab: Node, nodeTabId: number, isUnlock: boolean, tabId: number) {
    if (!isUnlock) {
      nodeTab.getChildByName("node_lock").active = true;
      nodeTab.getChildByName("node_unselect").active = false;
      nodeTab.getChildByName("node_select").active = false;
    } else {
      nodeTab.getChildByName("node_lock").active = false;
      nodeTab.getChildByName("node_unselect").active = tabId != nodeTabId;
      nodeTab.getChildByName("node_select").active = tabId == nodeTabId;
    }
  }

  private on_click_btn_territory() {
    AudioMgr.instance.playEffect(516);
    if (this._curBtnIndex == 0) {
      return;
    }

    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.TERRAIN_领地)) {
      UIMgr.instance.showPage(PlayerRouteName.UITerritory);
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.TERRAIN_领地));
    }
  }

  private on_click_btn_hero() {
    AudioMgr.instance.playEffect(HeroAudioName.Effect.点击下方战将页签);
    if (this._curBtnIndex == 1) {
      return;
    }

    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.HERO_战将系统)) {
      UIMgr.instance.showDialog(HeroRouteItem.UIHeroMain);
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.HERO_战将系统));
    }
  }

  private on_click_btn_home() {
    AudioMgr.instance.playEffect(517);
    if (this._curBtnIndex == 2) {
      return;
    }
    UIMgr.instance.showPage(CityRouteName.UIGameMap);
  }

  private on_click_btn_tiao_zhan() {
    AudioMgr.instance.playEffect(514);
    this.upItem();
    this.btnTiaoZhanClose.active = true;
  }
  private on_click_btn_daily_challenge() {
    AudioMgr.instance.playEffect(514);
    RouteManager.uiRouteCtrl.showRoute(UIDailyChallenge);
  }
  private on_click_btn_sanjie_trial() {
    AudioMgr.instance.playEffect(514);
  }

  private on_click_btn_post() {
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.POST_驿站系统)) {
      this.on_click_btn_tiao_zhan_close();
      UIMgr.instance.showDialog("UIPost");
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.POST_驿站系统));
    }
  }

  private on_click_btn_sheng_dian() {
    AudioMgr.instance.playEffect(1130);
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.SD_圣殿)) {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.SD_圣殿));
      return;
    }

    this.on_click_btn_tiao_zhan_close();
    UIMgr.instance.showDialog(ShengDianRouteItem.UIShengDianMain);
  }
  private on_click_btn_you_li() {
    AudioMgr.instance.playEffect(1104);
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.TRAVEL_游历系统)) {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.TRAVEL_游历系统));
      return;
    }

    this.on_click_btn_tiao_zhan_close();
    UIMgr.instance.showDialog("UITravel");
  }

  private on_click_btn_tian_huang_gu_jing() {
    AudioMgr.instance.playEffect(1196);
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.HUNT_天荒古境)) {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.HUNT_天荒古境));
      return;
    }

    this.on_click_btn_tiao_zhan_close();
    UIMgr.instance.showDialog("UIHuntMain");
  }

  private on_click_btn_yan_wu_chang() {
    AudioMgr.instance.playEffect(1089);
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.AZST_演武场)) {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.AZST_演武场));
      return;
    }
    this.on_click_btn_tiao_zhan_close();
    UIMgr.instance.showDialog("UIAzst");
  }

  private on_click_btn_pai_hang() {
    // 排行榜数据与圣殿一起
    AudioMgr.instance.playEffect(1130);
    if (!GameDirector.instance.isSystemOpen(SystemOpenEnum.RANK_排行)) {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.RANK_排行));
      return;
    }
    this.on_click_btn_tiao_zhan_close();
    RouteManager.uiRouteCtrl.showRoute(UIMainRank);
  }

  private on_click_btn_club() {
    AudioMgr.instance.playEffect(ClubAudioName.Effect.点击下方战盟页签);
    if (this._curBtnIndex == 4) {
      return;
    }

    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.CLUB_战盟系统)) {
      if (ClubModule.data?.clubFormMessage?.popUpMessage) {
        // 处理被踢的消息
        ClubModule.service.showExitedTips(true);
      } else if (!ClubModule.data.clubMessage) {
        UIMgr.instance.showDialog(ClubRouteItem.UIClubList);
      } else {
        UIMgr.instance.showPage(ClubRouteItem.UIClubMain);
      }
    } else {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.CLUB_战盟系统));
    }
  }

  on_click_btn_task() {
    TipsMgr.setEnableTouch(false, 3);
    this.getNode("btn_task").walk((child) => (child.layer = 8));

    let needCount = MainTaskModule.service.getNeedCount();
    let complateNum = MainTaskModule.service.getCompleteNum();
    if (needCount <= complateNum) {
      AudioMgr.instance.playEffect(1582);

      // 任务已完成
      TipsMgr.setEnableTouch(false, 1, false);

      const lastTaskId = MainTaskModule.data.mainTaskMsg.taskTimelineId;
      MainTaskModule.api.passMainTask((data: MainTaskPassResponse) => {
        // 当前任务配置
        const configTaskMain = MainTaskModule.data.getConfigTaskMain(MainTaskModule.data.mainTaskMsg.taskTimelineId);
        const isForce = configTaskMain.guide == 1;

        if (lastTaskId == MainTaskModule.data.mainTaskMsg.taskTimelineId) {
          log.error("领取奖励，但服务端告知任务未完成，可能任务配置表不同步", lastTaskId);
          TipsMgr.setEnableTouch(true);
        } else {
          log.info("任务完成，领取奖励", lastTaskId);

          // 按钮闪光动画
          let spine_task_sweep_get = this.getNode("spine_task_sweep_get").getComponent(sp.Skeleton);
          SpineUtil.playSpine(spine_task_sweep_get, "task_disappear", false);

          // 奖品飞行动画
          TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopItemFlyAni, {
            itemList: data.resAddList,
            wPosition: this.getNode("btn_task").worldPosition,
          });

          // 0.5秒进入下一个引导
          tween(this.node)
            .delay(0.2)
            .call(() => {
              TipsMgr.setEnableTouch(true);
              MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "REWARD_END");

              if (isForce) {
                TipsMgr.setEnableTouch(false, 3, false);
                let needCount = MainTaskModule.service.getNeedCount();
                let complateNum = MainTaskModule.service.getCompleteNum();

                if (needCount <= complateNum) {
                  GuideMgr.startGuide({ stepId: 50, isForce: true });
                } else {
                  GuideMgr.startGuide({ stepId: 49, isForce: true });
                }
              }
            })
            .start();
        }
      });
    } else {
      AudioMgr.instance.playEffect(1581);
      UIMgr.instance.showDialog(MainTaskRouteName.UITaskMainPopup, { hideMask: true }, null, () => {});
      TipsMgr.setEnableTouch(false, 1);
    }
  }

  on_click_btn_wu_zu_yu_lan() {
    AudioMgr.instance.playEffect(1900);
    UIMgr.instance.showDialog(CityRouteName.UIWorldPreview);
  }

  on_click_btn_sanjiexiaojia() {
    AudioMgr.instance.playEffect(1804);
    if (GameDirector.instance.isSystemOpen(SystemOpenEnum.SJXJ_三界小家) == false) {
      TipMgr.showTip(GameDirector.instance.getUnLockMsg(SystemOpenEnum.SJXJ_三界小家));
      return;
    }
    UIMgr.instance.showDialog(CityRouteName.UISanJieXiaoJia);
  }

  on_click_btn_task_auto() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);

    const cfg = JsonMgr.instance.getDefault(JsonMgr.instance.jsonList.c_buildCrystal);

    if (!CityModule.data.energyFactoryMsg.autoTake) {
      if (CityModule.data.energyFactoryMsg.autoStateCount >= cfg.unlock) {
        this.btn_task_auto.active = false;
        CityModule.api.takeAutoReward((resp) => {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, resp);
        });
        return;
      }
    }

    UIMgr.instance.showDialog(MainTaskRouteName.UITaskAutoClickPop, {
      totalSuccessCount: MainTaskModule.data.mainTaskMsg.totalSuccessCount,
      totalCount: cfg.unlock,
      rewardItemsList: cfg.rewardItemsList,
    });
  }

  on_click_btn_xiao_tou_icon() {
    AudioMgr.instance.playEffect(1724);
    UIMgr.instance.showDialog(PlayerRouteName.UIThief);
    // const nodeGround = NodeTool.findByName(director.getScene(), "node_ground");
    // for (let i = 0; i < nodeGround.children.length; i++) {
    //   if (nodeGround.children[i].name.startsWith("path_thief")) {
    //     nodeGround.children[i].getComponent(path_thief_ctrl).showUI();
    //     break;
    //   }
    // }
  }

  on_click_btn_gong_lue() {
    AudioMgr.instance.playEffect(1860);
    UIMgr.instance.showDialog(PlayerRouteName.UIStrategy);
  }

  aniHide() {
    const dt = 0.5;
    const screenWidth = StartUp.instance.getVisibleSize().width;
    const screenHeight = StartUp.instance.getVisibleSize().height;
    const easing = "backIn";

    tween(this.getNode("node_top"))
      .set({ position: v3(0, screenHeight / 2, 0) })
      .to(dt, { position: v3(0, screenHeight / 2 + 200, 0) }, { easing })
      .start();

    tween(this.getNode("node_bottom"))
      .set({ position: v3(0, -screenHeight / 2, 0) })
      .to(dt, { position: v3(0, -screenHeight / 2 - 200, 0) }, { easing })
      .start();

    tween(this.getNode("node_left"))
      .set({ position: v3(-screenWidth / 2, 0, 0) })
      .to(dt, { position: v3(-screenWidth / 2 - 200, 0, 0) }, { easing })
      .start();

    tween(this.getNode("node_right"))
      .set({ position: v3(screenWidth / 2, 0, 0) })
      .to(dt, { position: v3(screenWidth / 2 + 200, 0, 0) }, { easing })
      .start();

    tween(this.getNode("btn_task"))
      .set({ position: v3(-170, -474, 0) })
      .to(dt, { position: v3(-170 - 420, 0, 0) }, { easing })
      .start();
  }

  aniShow() {
    const dt = 0.5;
    const easing = "backOut";
    const screenWidth = StartUp.instance.getVisibleSize().width;
    const screenHeight = StartUp.instance.getVisibleSize().height;

    tween(this.getNode("node_top"))
      .to(dt, { position: v3(0, screenHeight / 2, 0) }, { easing })
      .start();

    tween(this.getNode("node_bottom"))
      .to(dt, { position: v3(0, -screenHeight / 2, 0) }, { easing })
      .start();

    tween(this.getNode("node_left"))
      .to(dt, { position: v3(-screenWidth / 2, 0, 0) }, { easing })
      .start();

    tween(this.getNode("node_right"))
      .to(dt, { position: v3(screenWidth / 2, 0, 0) }, { easing })
      .start();

    tween(this.getNode("btn_task"))
      .to(dt, { position: v3(-170, -474, 0) }, { easing })
      .start();
  }
}
