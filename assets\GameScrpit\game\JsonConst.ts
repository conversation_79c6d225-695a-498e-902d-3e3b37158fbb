import {
  IConfigAct,
  IConfigActionEffect,
  IConfigActionSkill,
  IConfigAttribute,
  IConfigBackground,
  IConfigBlessLand,
  IConfigBlessLandBee,
  IConfigBlessLandFund,
  IConfigBloodPlace,
  IConfigBubble,
  IConfigBuff,
  IConfigBuild,
  IConfigBuildBox,
  IConfigBuildCrystal,
  IConfigBuildLv,
  IConfigBuildLvReward,
  IConfigBuildShow,
  IConfigBuildTrimReward,
  IConfigBuildWorkerReward,
  IConfigBulletShow,
  IConfigBuyFirst,
  IConfigBuyId,
  IConfigBuyType,
  IConfigCityTransform,
  IConfigColor,
  IConfigCompete,
  IConfigCompeteRobot,
  IConfigCopyMain,
  IConfigDailyChallenge,
  IConfigDailyChallengeRank,
  IConfigEffect,
  IConfigErrorcode,
  IConfigEvent,
  IConfigEvent2,
  IConfigFightCharacter,
  IConfigFracture,
  IConfigFracture_draw,
  IConfigFracture_even,
  IConfigFriednFame,
  IConfigFriednFameLv,
  IConfigFriednReward,
  IConfigFriend,
  IConfigFriendHeroSkill,
  IConfigFriendPicture,
  IConfigFriendSkill,
  IConfigFriendSkillLv,
  IConfigGain,
  IConfigGuide,
  IConfigGuideGameTalk,
  IConfigGuideGameTast,
  IConfigGuidePath,
  IConfigGuideTalk,
  IConfigGuideV2,
  IConfigHead,
  IConfigHeadShow,
  IConfigHelp,
  IConfigHero,
  IConfigHeroBreak,
  IConfigHeroLv,
  IConfigHeroPicture,
  IConfigHeroSkill,
  IConfigHeroSkillLv,
  IConfigHeroTalentSkill,
  IConfigHome,
  IConfigHorse,
  IConfigHorseLv,
  IConfigHunt,
  IConfigHuntBoss,
  IConfigHuntBossRank,
  IConfigItem,
  IConfigJump,
  IConfigLand,
  IConfigLeader,
  IConfigLeaderName,
  IConfigLeaderSkin,
  IConfigLuckDraw,
  IConfigMail,
  IConfigMessage,
  IConfigMonster,
  IConfigMonsterMatrix,
  IConfigMonsterShow,
  IConfigMusic,
  IConfigPet,
  IConfigPetSkill,
  IConfigPetSkillRefresh,
  IConfigPetSkin,
  IConfigPost,
  IConfigPupil,
  IConfigPupilDrop,
  IConfigPupilName,
  IConfigPupilSkin,
  IConfigRight,
  IConfigSetUp,
  IConfigShop,
  IConfigSkillShow,
  IConfigSoul,
  IConfigSoulPicture,
  IConfigSpineShow,
  IConfigSystemOpen,
  IConfigTalk,
  IConfigTask,
  IConfigTaskMain,
  IConfigTemple,
  IConfigTempleShenji,
  IConfigTempleTree,
  IConfigTitle,
  IConfigTrain,
  IConfigTravle,
  IConfigTravlePlace,
  IConfigUnion,
  IConfigUnionBoss,
  IConfigUnionBossReward,
  IConfigUnionBuy,
  IConfigUnionDonate,
  IConfigUnionTask,
  IConfigUnlockType,
  IConfigVipReward,
} from "./JsonDefine";

export class JsonConst {
  c_act: { [key: number]: IConfigAct };
  c_actionEffect: { [key: number]: IConfigActionEffect };
  c_actionSkill: { [key: number]: IConfigActionSkill };
  c_attribute: { [key: number]: IConfigAttribute };
  c_background: { [key: number]: IConfigBackground };
  c_blessLand: { [key: number]: IConfigBlessLand };
  c_blessLandBee: { [key: number]: IConfigBlessLandBee };
  c_blessLandFund: { [key: number]: IConfigBlessLandFund };
  c_bloodPlace: { [key: number]: IConfigBloodPlace };
  c_bubble: { [key: number]: IConfigBubble };
  c_buff: { [key: number]: IConfigBuff };
  c_build: { [key: number]: IConfigBuild };
  c_buildBox: { [key: number]: IConfigBuildBox };
  c_buildCrystal: { [key: number]: IConfigBuildCrystal };
  c_buildLv: { [key: number]: IConfigBuildLv };
  c_buildLvReward: { [key: number]: IConfigBuildLvReward };
  c_buildShow: { [key: number]: IConfigBuildShow };
  c_buildTrimReward: { [key: number]: IConfigBuildTrimReward };
  c_buildWorkerReward: { [key: number]: IConfigBuildWorkerReward };
  c_bulletShow: { [key: number]: IConfigBulletShow };
  c_buyFirst: { [key: number]: IConfigBuyFirst };
  c_buyId: { [key: number]: IConfigBuyId };
  c_buyType: { [key: number]: IConfigBuyType };
  c_cityTransform: { [key: number]: IConfigCityTransform };
  c_color: { [key: number]: IConfigColor };
  c_compete: { [key: number]: IConfigCompete };
  c_competeRobot: { [key: number]: IConfigCompeteRobot };
  c_copyMain: { [key: number]: IConfigCopyMain };
  c_dailyChallenge: { [key: number]: IConfigDailyChallenge };
  c_dailyChallengeRank: { [key: number]: IConfigDailyChallengeRank };
  c_effect: { [key: number]: IConfigEffect };
  c_errorcode: { [key: number]: IConfigErrorcode };
  c_event: { [key: number]: IConfigEvent };
  c_event2: { [key: number]: IConfigEvent2 };
  c_fightCharacter: { [key: number]: IConfigFightCharacter };
  c_fracture: { [key: number]: IConfigFracture };
  c_fracture_draw: { [key: number]: IConfigFracture_draw };
  c_fracture_even: { [key: number]: IConfigFracture_even };
  c_friednFame: { [key: number]: IConfigFriednFame };
  c_friednFameLv: { [key: number]: IConfigFriednFameLv };
  c_friednReward: { [key: number]: IConfigFriednReward };
  c_friend: { [key: number]: IConfigFriend };
  c_friendHeroSkill: { [key: number]: IConfigFriendHeroSkill };
  c_friendPicture: { [key: number]: IConfigFriendPicture };
  c_friendSkill: { [key: number]: IConfigFriendSkill };
  c_friendSkillLv: { [key: number]: IConfigFriendSkillLv };
  c_gain: { [key: number]: IConfigGain };
  c_guide: { [key: number]: IConfigGuide };
  c_guideGameTalk: { [key: number]: IConfigGuideGameTalk };
  c_guideGameTast: { [key: number]: IConfigGuideGameTast };
  c_guidePath: { [key: number]: IConfigGuidePath };
  c_guideTalk: { [key: number]: IConfigGuideTalk };
  c_guideV2: { [key: number]: IConfigGuideV2 };
  c_head: { [key: number]: IConfigHead };
  c_headShow: { [key: number]: IConfigHeadShow };
  c_help: { [key: number]: IConfigHelp };
  c_hero: { [key: number]: IConfigHero };
  c_heroBreak: { [key: number]: IConfigHeroBreak };
  c_heroLv: { [key: number]: IConfigHeroLv };
  c_heroPicture: { [key: number]: IConfigHeroPicture };
  c_heroSkill: { [key: number]: IConfigHeroSkill };
  c_heroSkillLv: { [key: number]: IConfigHeroSkillLv };
  c_heroTalentSkill: { [key: number]: IConfigHeroTalentSkill };
  c_home: { [key: number]: IConfigHome };
  c_horse: { [key: number]: IConfigHorse };
  c_horseLv: { [key: number]: IConfigHorseLv };
  c_hunt: { [key: number]: IConfigHunt };
  c_huntBoss: { [key: number]: IConfigHuntBoss };
  c_huntBossRank: { [key: number]: IConfigHuntBossRank };
  c_item: { [key: number]: IConfigItem };
  c_jump: { [key: number]: IConfigJump };
  c_land: { [key: number]: IConfigLand };
  c_leader: { [key: number]: IConfigLeader };
  c_leaderName: { [key: number]: IConfigLeaderName };
  c_leaderSkin: { [key: number]: IConfigLeaderSkin };
  c_luckDraw: { [key: number]: IConfigLuckDraw };
  c_mail: { [key: number]: IConfigMail };
  c_message: { [key: number]: IConfigMessage };
  c_monster: { [key: number]: IConfigMonster };
  c_monsterMatrix: { [key: number]: IConfigMonsterMatrix };
  c_monsterShow: { [key: number]: IConfigMonsterShow };
  c_music: { [key: number]: IConfigMusic };
  c_pet: { [key: number]: IConfigPet };
  c_petSkill: { [key: number]: IConfigPetSkill };
  c_petSkillRefresh: { [key: number]: IConfigPetSkillRefresh };
  c_petSkin: { [key: number]: IConfigPetSkin };
  c_post: { [key: number]: IConfigPost };
  c_pupil: { [key: number]: IConfigPupil };
  c_pupilDrop: { [key: number]: IConfigPupilDrop };
  c_pupilName: { [key: number]: IConfigPupilName };
  c_pupilSkin: { [key: number]: IConfigPupilSkin };
  c_right: { [key: number]: IConfigRight };
  c_setUp: { [key: number]: IConfigSetUp };
  c_shop: { [key: number]: IConfigShop };
  c_skillShow: { [key: number]: IConfigSkillShow };
  c_soul: { [key: number]: IConfigSoul };
  c_soulPicture: { [key: number]: IConfigSoulPicture };
  c_spineShow: { [key: number]: IConfigSpineShow };
  c_systemOpen: { [key: number]: IConfigSystemOpen };
  c_talk: { [key: number]: IConfigTalk };
  c_task: { [key: number]: IConfigTask };
  c_taskMain: { [key: number]: IConfigTaskMain };
  c_temple: { [key: number]: IConfigTemple };
  c_templeShenji: { [key: number]: IConfigTempleShenji };
  c_templeTree: { [key: number]: IConfigTempleTree };
  c_title: { [key: number]: IConfigTitle };
  c_train: { [key: number]: IConfigTrain };
  c_travle: { [key: number]: IConfigTravle };
  c_travlePlace: { [key: number]: IConfigTravlePlace };
  c_union: { [key: number]: IConfigUnion };
  c_unionBoss: { [key: number]: IConfigUnionBoss };
  c_unionBossReward: { [key: number]: IConfigUnionBossReward };
  c_unionBuy: { [key: number]: IConfigUnionBuy };
  c_unionDonate: { [key: number]: IConfigUnionDonate };
  c_unionTask: { [key: number]: IConfigUnionTask };
  c_unlockType: { [key: number]: IConfigUnlockType };
  c_vipReward: { [key: number]: IConfigVipReward };
}