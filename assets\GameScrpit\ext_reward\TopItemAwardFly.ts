import { _decorator, math, Node, sp, tween, v2, Vec2, Vec3 } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { SpineUtil } from "../../platform/src/lib/utils/SpineUtil";

const { ccclass, property } = _decorator;

@ccclass("TopItemAwardFly")
export class TopItemAwardFly extends BaseCtrl {
  @property(Node)
  skt: Node = null;

  private _startWordPos: Vec3;
  private _endWordPos: Vec3;

  async start() {
    super.start();

    this.skt.active = true;

    this.skt.setWorldPosition(this._startWordPos.x, this._startWordPos.y, this._startWordPos.z);

    let spine = this.skt.getComponent(sp.Skeleton);

    SpineUtil.playSpine(spine, "kuang", false);

    // 发光时间
    let dt = SpineUtil.getSpineDuration(spine, "kuang");
    // 飞行时间
    let dtFly = Vec3.distance(this._startWordPos, this._endWordPos) / 2500;
    // 消失时间
    let dt3 = SpineUtil.getSpineDuration(spine, "quan");

    tween(this.skt)
      .delay(dt)
      .call(() => {
        SpineUtil.playSpine(spine, "tuowei", true);

        const angleRad = Math.atan2(
          this._endWordPos.y - this._startWordPos.y,
          this._endWordPos.x - this._startWordPos.x
        );

        this.skt.angle = math.toDegree(angleRad);
      })
      .to(dtFly, { worldPosition: this._endWordPos }, { easing: "sineIn" })
      .call(() => {
        SpineUtil.playSpine(spine, "quan", false);
      })
      .delay(dt3)
      .call(() => {
        setTimeout(() => {
          this.closeBack();
        }, 1);
      })
      .start();
  }

  async init(args: any) {
    this._startWordPos = args.startWordPos;
    this._endWordPos = args.endWordPos;
  }
}
