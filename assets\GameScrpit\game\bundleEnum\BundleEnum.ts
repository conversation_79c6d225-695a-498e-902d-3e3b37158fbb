/**分包名字 -- 优先级高的放在前面~~~~~~~ */
export enum BundleEnum {
  /**内置分包优先级 8  */
  RESOURCES = "resources",

  /**优先级7=============公用资源 */

  BUNDLE_CITY_100 = "bundle_city_100",
  BUNDLE_CITY_101 = "bundle_city_101",
  BUNDLE_CITY_102 = "bundle_city_102",
  BUNDLE_CITY_103 = "bundle_city_103",
  BUNDLE_CITY_104 = "bundle_city_104",
  BUNDLE_CITY_105 = "bundle_city_105",
  BUNDLE_CITY_106 = "bundle_city_106",
  BUNDLE_CITY_107 = "bundle_city_107",
  BUNDLE_CITY_108 = "bundle_city_108",
  BUNDLE_CITY_109 = "bundle_city_109",
  BUNDLE_CITY_110 = "bundle_city_110",
  BUNDLE_CITY_111 = "bundle_city_111",
  BUNDLE_CITY_112 = "bundle_city_112",
  BUNDLE_CITY_113 = "bundle_city_113",
  BUNDLE_CITY_114 = "bundle_city_114",
  BUNDLE_CITY_115 = "bundle_city_115",

  /**文字资源 */
  BUNDLE_COMMON_FONT = "bundle_common_font",

  /**仙友的公共资源 */
  BUNDLE_COMMON_FRIEND = "bundle_common_friend",

  /**英雄的公共资源 ---- 全身像*/
  BUNDLE_COMMON_HERO_FULL = "bundle_common_hero_full",

  /**英雄的公共资源 ---- 半身像*/
  BUNDLE_COMMON_HERO_HALF = "bundle_common_hero_half",

  /**英雄的公共资源 ---- 图标*/
  BUNDLE_COMMON_HERO_ICON = "bundle_common_hero_icon",

  /**道具图标，道具框 */
  BUNDLE_COMMON_ITEM = "bundle_common_item",

  /**灵宠公共资源 */
  BUNDLE_COMMON_PET = "bundle_common_pet",

  /**用户头像 ---- 公共资源 */
  BUNDLE_COMMON_PLAYERHEAD = "bundle_common_playerHead",

  /**公共的UI按钮图标资源 */
  BUNDLE_COMMON_UI = "bundle_common_ui",

  /**json配置文件 */
  BUNDLE_COMMON_JSON = "bundle_common_json",

  //=====优先级6 --- 初始化加载 --- 公共资源======

  /**通用窗口界面分包 */
  BUNDLE_G_COMMON_MAIN = "bundle_g_common_main",

  //======优先级5======
  /**主界面分包 */
  BUNDLE_G_MAINPAGE = "bundle_g_mainPage",

  /**主界面地图分包 */
  BUNDLE_G_GAME_MAP = "bundle_g_gameMap",

  /**主城据点分包 */
  BUNDLE_G_MAJORCITY = "bundle_g_majorCity",

  /**战斗分包 */
  BUNDLE_G_FIGHT = "bundle_g_fight",

  /**英雄分包 */
  BUNDLE_G_HERO = "bundle_g_hero",

  /**灵宠分包 */
  BUNDLE_G_PET = "bundle_g_pet",

  /**背包分包 */
  BUNDLE_G_KNAPSACK = "bundle_g_knapsack",

  /**主线任务分包 */
  BUNDLE_G_MAINTASK = "bundle_g_mainTask",

  /**玩家信息分包 */
  BUNDLE_G_PLAYER = "bundle_g_player",

  /**坐骑分包 */
  BUNDLE_G_HORSE = "bundle_g_horse",

  /**幸运夺宝分包 */
  BUNDLE_G_LOTTERY = "bundle_g_lottery",

  /**武魂分包 */
  BUNDLE_G_SOUL = "bundle_g_soul",

  /**仙友 */
  BUNDLE_G_FRIEND = "bundle_g_friend",

  /**弟子分包 */
  BUNDLE_G_PUPIL = "bundle_g_pupil",

  /**演武场 */
  BUNDLE_G_AZST = "bundle_g_azst",

  /**领地 */
  BUNDLE_G_TERRITORY = "bundle_g_territory",

  /**攻略 */
  BUNDLE_G_STRATEGY = "bundle_g_strategy",

  /**驿站 */
  BUNDLE_G_POST = "bundle_g_post",

  /**商城商店 */
  BUNDLE_G_SHOP = "bundle_g_shop",

  /**游历 */
  BUNDLE_G_TRAVEL = "bundle_g_travel",

  BUNDLE_G_SAN_JIE_XIAO_JIA = "bundle_g_san_jie_xiao_jia",

  /**狩猎 */
  BUNDLE_G_HUNT = "bundle_g_hunt",

  /** 福地 */
  BUNDLE_G_FARM = "bundle_g_farm",

  /**邮件 */
  BUNDLE_G_MAIL = "bundle_g_mail",

  //仙友品质背景
  BUNDLE_COMMON_FRIENDICON = "bundle_common_friendIcon",

  //仙盟
  BUNDLE_G_CLUB = "bundle_g_club",

  //贵族豪礼
  BUNDLE_G_VIP = "bundle_g_vip",

  /**图鉴 */
  BUNDLE_G_COLLECT = "bundle_g_collect",
  /**聊天 */
  BUNDLE_G_CHAT = "bundle_g_chat",
  /**月卡充值 */
  BUNDLE_G_CARD = "bundle_g_monthCard",

  /**圣殿 */
  BUNDLE_G_SHENGDIAN = "bundle_g_shengdian",

  /** 七日活动 */
  BUNDLE_HD_SEVEN = "bundle_hd_seven",
  /** 首充 */
  BUNDLE_HD_SHOUCHONG = "bundle_hd_shouchong",
  /** 仙友目标 */
  BUNDLE_HD_FRIENDMUBIAO = "bundle_hd_friendmubiao",

  /**基金 */
  BUNDLE_HD_FUND = "bundle_hd_fund",

  /**山海 */
  BUNDLE_HD_SONHAI = "bundle_hd_sonhai",

  /**条件礼包 */
  BUNDLE_HD_TIAOJIANLIBAO = "bundle_hd_tiaojianlibao",

  /** 扩展-引导 */
  BUNDLE_EXT_GUIDE = "bundle_ext_guide",

  /** 扩展-奖励 */
  BUNDLE_EXT_REWARD = "bundle_ext_reward",

  BUNDLE_PUB = "bundle_pub",

  BUNDLE_INIT_LOGIN = "bundle_init_login",

  BUNDLE_GAME_HEALTH = "bundle_game_health",

  /**繁荣比拼 */
  BUNDLE_HD_FRBP = "bundle_hd_frbp",

  /**
   * 时空裂缝
   */
  BUNDLE_G_FRACTURE = "bundle_g_fracture",

  /**
   * 主界面排行
   */
  BUNDLE_G_MAIN_RANK = "bundle_g_main_rank",

  /**
   * 限时任务
   */
  BUNDLE_G_TIME_TASK = "bundle_g_time_task",

  /**
   * 限时任务测试版
   */
  BUNDLE_G_TIME_TEST = "bundle_g_time_test",

  /**
   * 事件
   */
  BUNDLE_G_EVENT_ACTION = "bundle_g_event_action",

  /**
   * 每日挑战
   */
  BUNDLE_DAILY_CHALLENGE = "bundle_daily_challenge",

  /**
   * 商店
   */
  BUNDLE_COMMON = "bundle_common",
}
