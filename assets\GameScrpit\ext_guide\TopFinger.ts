import {
  Button,
  EventTouch,
  Input,
  instantiate,
  isValid,
  math,
  sp,
  Sprite,
  tween,
  UIOpacity,
  UITransform,
  v3,
  Vec3,
  view,
} from "cc";
import { _decorator, Node } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { NodeTool } from "../lib/utils/NodeTool";
import { BoutStartUp, dtTime } from "../game/BoutStartUp";
import MsgEnum from "../game/event/MsgEnum";
import MsgMgr from "../lib/event/MsgMgr";
import { UIMgr } from "../lib/ui/UIMgr";
import { CityRouteName } from "../module/city/CityConstant";
import { IConfigGuidePath } from "../game/JsonDefine";
import { StartUp } from "../lib/StartUp";
import { TipsMgr } from "../../platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import { Sleep } from "../game/GameDefine";
import { SpineUtil } from "../../platform/src/lib/utils/SpineUtil";
import GuideMgr from "./GuideMgr";
import { RouteManager } from "../../platform/src/core/managers/RouteManager";

const { ccclass, property } = _decorator;

enum FingerStatusEnum {
  WaitStart = 1, // 等待开始
  WaitNodeFind = 2, // 等待开始
  WaitClick = 3, // 等待点击
  WaitMsg = 4, // 等待消息
  Moving = 5, // 移动中
  WaitNext = 6, // 等待下一个
}

const CLICK_END = "CLICK_END";

const log = Logger.getLoger(LOG_LEVEL.WARN);

interface INodeAlpha {
  node: Node;
  nodeCopy: Node;
  num: number;
}

@ccclass("TopFinger")
export class TopFinger extends BaseCtrl {
  // 之前的层级状态
  public static nodeClickHideList: INodeAlpha[] = [];

  // 不自动设置层级
  autoSetLayer = false;

  args: { stepId: number; args?: any; isForce?: boolean; startPos?: Vec3 };

  btn_click_area: Node;

  sprite_mask: Sprite;

  fingerStatus: FingerStatusEnum = FingerStatusEnum.WaitStart;

  isReady = false;

  // 是否强制
  public isForce: boolean = true;

  // 引导步骤列表
  public guidePathList: IConfigGuidePath[] = [];

  // 当前引导索引
  private currentIdx = 0;

  // 要点击的节点的父节点
  private nodeParent: Node;

  // 要点击的节点
  private nodeClick: Node;

  // 复制的点击节点
  private nodeClickCopy: Node;

  // 循环间隔
  private cd = 0;

  // 生命时长
  private lifeTime = 0;

  // 初始位置
  private startPos: Vec3;

  // 结束位置
  private endPos: Vec3;

  // 手指位置
  private spineFinger: sp.Skeleton;

  // 当前nodeClickCopy等待点击的序号，与当前序号不一致，就要释放

  public init(args: { stepId: number; args?: any; isForce?: boolean; startPos?: Vec3 }) {
    log.info("initStep " + args.stepId);
    //打印执行的stepiD
    console.log("执行的stepiD:", args.stepId);
    this.args = args;
    this.lifeTime = 0;
    this.currentIdx = 0;
    this.nodeParent = null;
    this.nodeClick = null;
    this.btn_click_area.active = false;
    this.startPos = args.startPos;

    // 强制引导
    this.isForce = args.isForce === true;

    // 克隆引导步骤
    let list = JsonMgr.instance.getConfigGuidePathList(args.stepId);
    let cloneList: IConfigGuidePath[] = JSON.parse(JSON.stringify(list));

    // 替换参数
    for (let idx in cloneList) {
      const step = cloneList[idx];
      for (let key in args.args) {
        step.nodeClickName = step.nodeClickName.replace(`\${${key}}`, args.args[key]);
        step.parentName = step.parentName.replace(`\${${key}}`, args.args[key]);
        if (step.redo == 1) {
          step.redo = args.args.redo;
        }
      }
    }

    this.guidePathList = cloneList;
  }

  protected onEnable(): void {
    if (isValid(this.nodeClickCopy)) {
      log.warn("onEnable 上一次复制节点未释放");
      this.nodeClickCopy.destroy();
    }
  }

  // 开始引导
  public async startGuide() {
    log.info("startGuide " + this.args?.stepId);

    if (!this.args?.stepId) {
      return;
    }

    TipsMgr.setEnableTouch(false, 0.3);
    // 重置原结点
    this.recoverNode();
    this.setFingerStatus(FingerStatusEnum.WaitStart);

    this.sprite_mask.enabled = this.args.isForce;
    // 兼容BaseCtrl
    RouteManager.uiRouteCtrl.removeAllRouteNode();

    let routeInfo = UIMgr.instance.getLastPageInfo();
    let inList = this.guidePathList.filter((e) => e.routeNameList.includes(routeInfo?.name));
    if (inList.length) {
      while (this.guidePathList.length) {
        if (this.guidePathList[0].routeNameList.includes(routeInfo?.name)) {
          break;
        } else {
          this.guidePathList.splice(0, 1);
          continue;
        }
      }
    } else {
      if (UIMgr.instance.getByName(CityRouteName.UICityDetail)) {
        TipsMgr.setEnableTouch(false, 0.3);
        MsgMgr.emit(MsgEnum.ON_CITY_UN_FOCOUS);
        await Sleep(0.3);
      }

      // 当前界面不在路由列表中，先跳到第一个路由
      UIMgr.instance.showPage(UIMgr.instance.defaultPageName);

      if (this.guidePathList.length) {
        if (UIMgr.instance.defaultPageName != this.guidePathList[0].parentName) {
          const uiMain = NodeTool.findByName(StartUp.instance.uiRoot, "UIMain");
          const btnHome = NodeTool.findByName(uiMain, "btn_home");
          NodeTool.fakeClick(btnHome);
        }
      } else {
        log.error("startGuide this.guidePathList.length", this.guidePathList.length, this.args.stepId);
      }
    }
  }

  start() {
    super.start();
    log.info("start " + this.guidePathList[0]?.typeId);

    this.sprite_mask = this.getNode("node_block_event").getComponent(Sprite);
    this.btn_click_area = this.getNode("btn_click_area");
    this.spineFinger = NodeTool.findByName(this.btn_click_area, "spine").getComponent(sp.Skeleton);

    this.sprite_mask.enabled = false;

    const btn_mask_click = this.getNode("btn_mask_click");
    btn_mask_click.on(Input.EventType.TOUCH_START, this.on_btn_mask_click, this);
    btn_mask_click.on(Input.EventType.TOUCH_MOVE, this.on_btn_mask_click, this);
    btn_mask_click.on(Input.EventType.TOUCH_CANCEL, this.on_btn_mask_click, this);
    btn_mask_click.on(Input.EventType.TOUCH_END, this.on_btn_mask_click, this);

    // 如果已在这个路由地址，就不在跳转
    this.startGuide();

    MsgMgr.on(MsgEnum.ON_GUIDE_NEXT, this.nextStep, this);

    this.isReady = true;
  }

  private async on_btn_mask_click(event: EventTouch) {
    event.preventSwallow = !this.isForce;
    if (event.type == "touch-end" && this.isForce !== true) {
      log.info("on_btn_mask_click out");
      GuideMgr.clearAll();
      this.stopAndClose();
    }
  }

  onDestroy() {
    MsgMgr.off(MsgEnum.ON_GUIDE_NEXT, this.nextStep, this);
    super.onDestroy();
  }

  private nextStep(msg: string): boolean {
    if (this.node.active == false) {
      return;
    }

    if (this.fingerStatus == FingerStatusEnum.WaitNext) {
      return;
    }

    let configGuidePath = this.guidePathList[this.currentIdx];
    if (!configGuidePath) {
      log.info("nextStep 引导已经结束" + " " + this.guidePathList[0]?.typeId);
      this.stopAndClose();
      return;
    }

    if (configGuidePath.checkType === 2) {
      log.info("nextStep 收到消息 " + msg + " 监听消息 " + configGuidePath.msg + " " + this.guidePathList[0]?.typeId);
      if (msg !== configGuidePath.msg) {
        log.info(
          "nextStep 不是要的消息" +
            this.guidePathList[0]?.typeId +
            ` msg:${msg}, configGuidePath.msg:${configGuidePath.msg}`
        );
        return;
      }
    } else if (configGuidePath.checkType === 1) {
      log.info("nextStep 待点击");
      if (msg !== CLICK_END) {
        log.info("nextStep 不是点击结束" + this.guidePathList[0]?.typeId);
        return;
      }
    }

    if (configGuidePath.redo > 0) {
      configGuidePath.redo--;
      log.info("nextStep 剩余点击次数:" + configGuidePath.redo);
    }

    if (configGuidePath.redo > 0) {
      log.info("nextStep 点击任务未完成" + this.guidePathList[0]?.typeId);
      return;
    }

    this.currentIdx++;
    log.info("nextStep FingerStatusEnum.WaitStart" + this.guidePathList[0]?.typeId);
    this.setFingerStatus(FingerStatusEnum.WaitStart);
    this.checkStatus(0.01);
  }

  updateNodeFinger(nodeFrom: Node, nodeTo: Node) {
    if (!isValid(nodeFrom)) {
      if (isValid(this.nodeClickCopy)) {
        this.nodeClickCopy.destroy();
      }

      this.currentIdx++;
      log.warn("updateNodeFinger FingerStatusEnum.WaitStart" + this.guidePathList[0]?.typeId);
      this.setFingerStatus(FingerStatusEnum.WaitStart);
      return;
    }

    let pos = NodeTool.getNodeCenterWorldPos(nodeFrom);

    nodeTo.setWorldPosition(pos);
    nodeTo.active = nodeFrom.activeInHierarchy;

    // 设置复制节点
    if (isValid(this.nodeClickCopy)) {
      this.nodeClickCopy.setWorldPosition(nodeFrom.getWorldPosition());
      if (nodeFrom.active != this.nodeClickCopy.active) {
        this.nodeClickCopy.active = nodeFrom.active;
      }
    }
  }

  private checkStatus(deltaTime: number) {
    if (this.fingerStatus == FingerStatusEnum.WaitNext) {
      return;
    }

    let configGuidePath = this.guidePathList[this.currentIdx];

    if (this.fingerStatus == FingerStatusEnum.WaitStart) {
      // 没有下一步了，正常不会走这步，容错处理
      if (!configGuidePath) {
        TipsMgr.setEnableTouch(true);

        this.stopAndClose();
        log.info("checkStatus 结束引导" + this.args?.stepId);
        return;
      }

      if (!configGuidePath.nodeClickName) {
        log.info("checkStatus 无节点，就等待回调消息,不显示手指和阴影" + this.guidePathList[0]?.typeId);
        this.sprite_mask.enabled = false;
        this.setFingerStatus(FingerStatusEnum.WaitMsg);
        return;
      } else {
        log.info("checkStatus 显示手指和阴影" + this.guidePathList[0]?.typeId);
        this.sprite_mask.enabled = this.isForce;
      }

      if (!this.nodeParent) {
        this.nodeParent = NodeTool.findByName(BoutStartUp.instance.gameRoot, configGuidePath.parentName);
        if (!this.nodeParent) {
          this.nodeParent = NodeTool.findByName(BoutStartUp.instance.uiRoot, configGuidePath.parentName);
        }
        if (this.nodeParent) {
          this.setFingerStatus(FingerStatusEnum.WaitNodeFind);
        }
      }
    }

    if (this.fingerStatus == FingerStatusEnum.WaitNodeFind) {
      log.info("checkStatus 找到 父结点" + this.guidePathList[0]?.typeId);

      if (!this.nodeParent) {
        log.warn("checkStatus 父节点无效，结束引导" + this.guidePathList[0]?.typeId);
        this.stopAndClose();
        return;
      }

      this.nodeClick = NodeTool.findByName(this.nodeParent, configGuidePath.nodeClickName, false);
      if (!this.nodeClick) {
        log.info("checkStatus 未找到 点击结点" + this.guidePathList[0]?.typeId);
        return;
      }

      let routeLast = UIMgr.instance.getLastPageInfo();
      if (routeLast.name === CityRouteName.UIGameMap) {
        log.info("checkStatus 需要先还原位置，可能会有缩放");
        MsgMgr.emit(MsgEnum.ON_CITY_UN_FOCOUS);
      }

      this.endPos = NodeTool.getNodeCenterWorldPos(this.nodeClick);
      let dt = 0;
      // 城市节点
      if (configGuidePath.nodeClickName.startsWith("city_")) {
        this.endPos = this.moveToCetnerFinally(this.nodeClick);
        dt = 0.5;
        MsgMgr.emit(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, configGuidePath.nodeClickName);
      }

      // 装饰结点
      if (configGuidePath.parentName.startsWith("trim_")) {
        this.endPos = this.moveToCetnerFinally(this.nodeClick);
        dt = 0.5;
        MsgMgr.emit(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, configGuidePath.parentName, configGuidePath.nodeClickName);
      }

      // 需要移动地图的节点
      const aniNodeList = ["btn_pupil", "btn_level_up", "btn_farm", "btn_pet", "btn_fracture"];
      if (aniNodeList.includes(configGuidePath.nodeClickName)) {
        this.endPos = this.moveToCetnerFinally(this.nodeClick);
        dt = 0.5;
        MsgMgr.emit(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, configGuidePath.nodeClickName);
      }

      // 事件结点
      const eventNode = ["path_thief_101", "path_thief_201"];
      if (eventNode.includes(configGuidePath.parentName)) {
        this.endPos = this.moveToCetnerFinally(this.nodeClick);
        dt = 0.5;
        MsgMgr.emit(MsgEnum.ON_ZHAOGE_CITY_FOCOUS, configGuidePath.parentName);
      }

      if (this.startPos && !configGuidePath.noFly) {
        this.setFingerStatus(FingerStatusEnum.Moving);
        this.btn_click_area.setWorldPosition(this.startPos);

        this.btn_click_area.setScale(0, 0, 1);
        if (dt <= 0) {
          dt = Vec3.distance(this.endPos, this.startPos) / 1600;
        }
        let t1 = tween(this.btn_click_area).to(1, { scale: v3(1, 1, 1) }, { easing: "backOut" });
        let t2 = tween(this.btn_click_area)
          .to(dt, { worldPosition: this.endPos }, { easing: "sineInOut" })
          .call(() => {
            this.copyNode(configGuidePath);
            this.setFingerStatus(FingerStatusEnum.WaitClick);
          });
        tween(this.btn_click_area).parallel(t1, t2).start();

        return;
      } else {
        this.btn_click_area.setScale(0, 0, 1);
        tween(this.btn_click_area)
          .to(0.5, { scale: v3(1, 1, 1) }, { easing: "backOut" })
          .start();
        this.copyNode(configGuidePath);
        this.setFingerStatus(FingerStatusEnum.WaitClick);
      }

      // 进入等待点击状态
      this.updateNodeFinger(this.nodeClick, this.btn_click_area);
    } else if (this.fingerStatus == FingerStatusEnum.WaitMsg) {
      // TODO: 可能无效，时间可能被重置
      log.info("checkStatus 等待消息");
      this.lifeTime += deltaTime;
      if (this.lifeTime > 10) {
        this.stopAndClose();
        log.info("checkStatus 等待消息 超时");
        return;
      }
    }
  }

  private copyNode(configGuidePath: IConfigGuidePath) {
    this.recoverNode();

    if (!isValid(this.nodeClick)) {
      this.stopAndClose();
      return;
    }

    // 复制节点
    log.info("checkStatus 复制占位节点" + this.guidePathList[0]?.typeId);

    this.nodeClickCopy = instantiate(this.nodeClick);
    this.nodeClickCopy.parent = this.node;
    this.nodeClickCopy.setSiblingIndex(this.btn_click_area.getSiblingIndex());
    this.nodeClickCopy.setWorldPosition(this.nodeClick.worldPosition);
    this.nodeClickCopy.walk((child) => {
      child.layer = this.node.layer;
    });

    // 不可见
    if (this.isForce == false || configGuidePath.hideShadow) {
      const uiOpacity = this.nodeClickCopy.addComponent(UIOpacity);
      uiOpacity.opacity = 0;
      TopFinger.nodeClickHideList.push({ node: null, nodeCopy: this.nodeClickCopy, num: -1 });
    } else {
      // 原节点不可见
      let uiOpacity = this.nodeClick.getComponent(UIOpacity);
      if (uiOpacity) {
        TopFinger.nodeClickHideList.push({
          node: this.nodeClick,
          nodeCopy: this.nodeClickCopy,
          num: uiOpacity.opacity,
        });
      } else {
        uiOpacity = this.nodeClick.addComponent(UIOpacity);
        TopFinger.nodeClickHideList.push({ node: this.nodeClick, nodeCopy: this.nodeClickCopy, num: -1 });
      }
      uiOpacity.opacity = 0;
    }

    // 移除事件
    const cptButton = this.nodeClickCopy.getComponent(Button);
    cptButton && cptButton.destroy();
    this.nodeClickCopy.off(Input.EventType.TOUCH_START);
    this.nodeClickCopy.off(Input.EventType.TOUCH_MOVE);
    this.nodeClickCopy.off(Input.EventType.TOUCH_CANCEL);
    this.nodeClickCopy.off(Input.EventType.TOUCH_END);

    // 绑定事件
    this.nodeClickCopy.on(Input.EventType.TOUCH_START, this.onBtnClickNotEnd, this);
    this.nodeClickCopy.on(Input.EventType.TOUCH_MOVE, this.onBtnClickNotEnd, this);
    this.nodeClickCopy.on(Input.EventType.TOUCH_CANCEL, this.onBtnClickNotEnd, this);
    this.nodeClickCopy.on(Input.EventType.TOUCH_END, this.onBtnClickEnd, this);
    this.nodeClickCopy.walk((child) => {
      child.layer = child.parent.layer;
    });
  }

  /**
   * 计算移动后的位置，用于无法移到中心时的位置计算
   * @param node 要移动的节点
   * @returns 最终目标节点的世界坐标
   */
  private moveToCetnerFinally(node: Node): Vec3 {
    // 一个代码耦合,找到主地图的node_ground节点
    // 计算中心点位置
    const pos = NodeTool.getNodeCenterPos(node);
    let parentSize: math.Size;
    let nodeCurrent: Node = node.getParent();
    for (let i = 0; i < 3; i++) {
      if (nodeCurrent.name == "btn_map") {
        nodeCurrent = nodeCurrent.getChildByName("node_ground");
        const uiTransform = nodeCurrent.getParent().getComponent(UITransform);
        parentSize = uiTransform.contentSize;
        break;
      } else if (nodeCurrent.name == "node_ground") {
        const uiTransform = nodeCurrent.getParent().getComponent(UITransform);
        parentSize = uiTransform.contentSize;
        break;
      } else {
        pos.x += nodeCurrent.getPosition().x;
        pos.y += nodeCurrent.getPosition().y;
      }
      nodeCurrent = nodeCurrent.getParent();
    }

    // 屏幕宽高
    const width = StartUp.instance.getVisibleSize().width;
    const height = StartUp.instance.getVisibleSize().height;
    let x = width / 2;
    let y = height / 2;

    // 位置修复
    const posToParent = v3(pos.x + nodeCurrent.getPosition().x, pos.y + nodeCurrent.getPosition().y);
    if (posToParent.x < 0 && parentSize.width / 2 + posToParent.x < width / 2) {
      x = parentSize.width / 2 + pos.x;
    }

    if (posToParent.x > 0 && parentSize.width / 2 - posToParent.x < width / 2) {
      x = width - (parentSize.width / 2 - pos.x);
    }

    if (posToParent.y < 0 && parentSize.height / 2 + posToParent.y < height / 2) {
      y = parentSize.height / 2 + pos.y;
    }

    if (posToParent.y > 0 && parentSize.height / 2 - posToParent.y < height / 2) {
      y = height - (parentSize.height / 2 - pos.y);
    }

    // 长屏转世界坐标修复
    const screenHeight = view.getVisibleSize().height;
    if (screenHeight > StartUp.instance.getVisibleSize().height) {
      y += (screenHeight - StartUp.instance.getVisibleSize().height) / 2;
    }
    return v3(x, y, 1);
  }

  update(deltaTime: number) {
    // 等待消息时长重置
    // if (this.isForce) {
    //   this.lifeTime += deltaTime;
    //   if (this.lifeTime > 5) {
    //     this.isForce = false;
    //   }
    // }

    if (this.fingerStatus == FingerStatusEnum.WaitClick) {
      if (!isValid(this.nodeParent)) {
        log.warn("update 父节点无效，结束引导");
        this.stopAndClose();
        return;
      }

      if (!isValid(this.nodeClick)) {
        log.warn("update 子节点无效，结束引导");
        if (isValid(this.nodeClickCopy)) {
          this.nodeClickCopy.destroy();
        }
        this.stopAndClose();
        return;
      }

      this.updateNodeFinger(this.nodeClick, this.btn_click_area);
    }

    this.cd += deltaTime;
    if (this.cd < 0.1) {
      return;
    }
    this.cd = 0;

    this.checkStatus(dtTime);
  }

  public setFingerStatus(status: FingerStatusEnum) {
    log.info("setFingerStatus: ", status);

    this.lifeTime = 0;
    this.fingerStatus = status;

    if (status === FingerStatusEnum.WaitStart) {
      // 等待找到按钮才可以点击
      TipsMgr.setEnableTouch(false, 3, false);
      this.btn_click_area.active = false;
      this.nodeParent = null;
      this.nodeClick = null;

      this.sprite_mask.enabled = false;

      // 释放原来的复制节点
      this.recoverNode();
    } else if (status === FingerStatusEnum.WaitClick) {
      // 开放点击
      TipsMgr.setEnableTouch(true);
      this.btn_click_area.active = true;
      SpineUtil.playSpine(this.spineFinger, "animation", true);
      if (this.isForce) {
        this.sprite_mask.enabled = true;
        let configGuidePath = this.guidePathList[this.currentIdx];
        if (configGuidePath.hideShadow == 1) {
          this.sprite_mask.enabled = false;
        }
      }
    } else if (status === FingerStatusEnum.WaitMsg) {
      // 等待消息回调禁用点击
      TipsMgr.setEnableTouch(false, 3, false);
      this.btn_click_area.active = false;
      this.sprite_mask.enabled = false;
    } else if (status === FingerStatusEnum.Moving) {
      // 移动中
      TipsMgr.setEnableTouch(false, 3, false);

      this.btn_click_area.active = true;

      SpineUtil.stopAtFrame(this.spineFinger, "animation", 0);
    }
  }

  public stopAndClose() {
    log.info("stopAndClose");
    TipsMgr.setEnableTouch(false, 0.2);
    GuideMgr.endGuide();
  }

  private onBtnClickNotEnd(event: EventTouch) {
    if (isValid(this.nodeClick)) {
      this.nodeClick.dispatchEvent(event);
    }
  }

  // 点击事件反馈
  private onBtnClickEnd(event: EventTouch) {
    if (isValid(this.nodeClick)) {
      this.startPos = this.nodeClickCopy.getWorldPosition();

      this.nodeClick.dispatchEvent(event);

      // 第一时间同步释放，否则可能引用资源被释放问题
      if (!isValid(this.nodeClick)) {
        this.nodeClickCopy.destroy();
      }

      this.nextStep(CLICK_END);
    } else {
      this.stopAndClose();
      log.warn("onBtnClick nodeClick invalid" + " " + this.guidePathList[0]?.typeId);
      return;
    }
  }

  private recoverNode() {
    while (TopFinger.nodeClickHideList.length) {
      let obj = TopFinger.nodeClickHideList.pop();

      if (isValid(obj.node)) {
        const uiOpacity = obj.node.getComponent(UIOpacity);
        if (uiOpacity) {
          if (obj.num == -1) {
            uiOpacity.destroy();
          } else {
            uiOpacity.opacity = obj.num;
          }
        }
      }

      if (isValid(obj.nodeCopy)) {
        obj.nodeCopy.destroy();
      }
    }
  }
}
