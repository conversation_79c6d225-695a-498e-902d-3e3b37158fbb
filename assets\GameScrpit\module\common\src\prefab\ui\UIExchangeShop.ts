import { _decorator, Component, instantiate, Node } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
import { ClubModule } from "../../../../club/ClubModule";
import { ExchangeShopAdapter } from "../../adapter/ExchangeShopViewHolder";
import { ItemCost } from "db://assets/GameScrpit/game/common/ItemCost";
import { GoodsModule } from "../../../../goods/GoodsModule";
import { CommonModule } from "../../CommonModule";
const { ccclass, property } = _decorator;

@ccclass("UIExchangeShop")
@routeConfig({
  bundle: BundleEnum.BUNDLE_COMMON,
  url: "prefab/ui/UIExchangeShop",
  nextHop: [],
  exit: "dialog_close",
})
export class UIExchangeShop extends BaseCtrl {
  public playShowAni: boolean = true;
  private shopList = [
    {
      name: "古镜商店",
      types: [3, 4],
      typesName: ["道具", "灵兽"],
    },
    {
      name: "福地商店",
      types: [10],
      typesName: [""],
    },
    {
      name: "战盟商店",
      types: [11],
      typesName: [""],
    },
    {
      name: "试练商店",
      types: [13],
      typesName: [""],
    },
    {
      name: "每日挑战商店",
      types: [14],
      typesName: [""],
    },
  ];
  private currentShop;
  init(args: RouteShowArgs): void {
    //
    let shopType = args.payload.type;
    this.currentShop = this.shopList.find((obj) => obj.types.includes(shopType));
    if (this.currentShop) {
      // 后续可根据需求使用 targetShop 进行相关操作
    }
  }
  start() {
    super.start();

    let shopDatas = CommonModule.config.getShopConfig(this.currentShop.types[0]);
    let adapter = new ExchangeShopAdapter(this.getNode("viewholder_shop"));
    this.getNode("node_list").getComponent(AdapterView).setAdapter(adapter);
    GoodsModule.api.buyInfo((data) => {
      adapter.setData(shopDatas);
    });

    let coinsSet = new Set<number>();
    for (let i = 0; i < shopDatas.length; i++) {
      let item = shopDatas[i];
      coinsSet.add(item.cointype);
    }
    let coinsList = [];
    coinsSet.forEach((value) => {
      coinsList.push(value);
    });
    this.getNode("item_coins").children.forEach((item) => {
      item.active = false;
    });
    for (let i = 0; i < coinsList.length; i++) {
      let coinId = coinsList[i];
      let item = this.getNode("item_coins").children[i];
      if (!item) {
        item = instantiate(this.getNode("item_coins").children[0]);
        item.parent = this.getNode("item_coins");
      }
      item.active = true;
      item.getComponentInChildren(ItemCost).setItemId(coinId);
    }
  }

  update(deltaTime: number) {
    //
  }
}
