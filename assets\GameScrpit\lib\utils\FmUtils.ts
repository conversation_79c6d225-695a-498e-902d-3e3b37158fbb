import { sp, tween, UITransform, Vec3 } from "cc";
import { Node, Color, Sprite } from "cc";
import { BundleEnum } from "../../game/bundleEnum/BundleEnum";
import ResMgr from "../common/ResMgr";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { RightConditionEnum } from "../../game/GameDefine";

export interface ItemSetInfo {
  id: number;
  num?: number;
  showNum?: boolean;
}

/**  */
export interface HeadInfo {
  userId: number;
  /** 角色名称 */
  nickname: string;
  /** 头像 */
  avatarList: number[];
  /** 性别 */
  sex: number;
  /** 当前等级 */
  level: number;
  /** 会员等级 */
  vipLevel: number;
}

export default class FmUtils {
  public static isDebug: boolean = true;

  static sceneMain: string = "main";

  /**获取一切动画在倍数下的时间 */
  public static getActTimeScale(time: number, scale: number) {
    return time / scale;
  }

  /**测试接口 --- 添加资源*/
  // public static addResSocket(resId: number, num: number) {
  //   let info: GoodsAddRequest = { resId: resId, num: num };
  //   console.log("添加资源", info);
  //   SendMgr.send(GoodsSubCmd.addRes, GoodsAddRequest.encode(info));
  // }

  /**闯关气运损耗显示万分比
   * @param a 初始消耗
   * @param b 消耗系数
   * @param c BOSS战力
   * @param d 玩家战力
   * @param e 繁荣度
   */
  public static passChapterCost(a: number, b: number, c: number, d: number, e: number) {
    ///INT(e*a*b*(1+min(c/d*0.8,0.99)))
    let param1 = e * a * b * (1 + Math.min((c / d) * 0.8, 0.99));
    let param2 = Math.floor(param1);
    return param2;
  }

  /**闯关气运损耗显示固定值
   * @param a 固定消耗
   * @param b 消耗系数
   * @param c BOSS战力
   * @param d 玩家战力
   */
  public static passChapterCostFix(a: number, b: number, c: number, d: number) {
    ///INT(a*b*(1+min(c/d*0.8,0.99)))
    let param1 = a * b * (1 + Math.min((c / d) * 0.8, 0.99));
    let param2 = Math.floor(param1);
    return param2;
  }

  /**
   * 闯关气运消耗百分比
   * @param a boos战力
   * @param b 玩家战力
   * @param c 损耗系数
   */
  public static passChapterPercent(a: number, b: number, c: number) {
    let param1 = Math.min((a / b) * 0.8, 0.99);
    let param2 = Math.max(0.5, param1);
    let param3 = Math.floor(param2 * c);
    return param3;
  }

  /**转换成另一个节点坐标系
   *@param node 需要被转换的节点
   *@param converNode 转换到哪个坐标系节点
   */
  public static transferOfAxes(node: Node, converNode: Node) {
    let worldSpace = node.parent.getComponent(UITransform).convertToWorldSpaceAR(node.position);
    let nodeSpace = converNode.getComponent(UITransform).convertToNodeSpaceAR(worldSpace);
    return nodeSpace;
  }

  /**
   *
   * 只有道具，没有其他的东西的物品设置，如果没有给个默认 item_1.png
   *
   * @param sp 要换的精灵
   * @param itemId 物品ID
   */
  public static async setItemIcon(node: Node, itemId: number) {
    if (!node) {
      console.error("node is null");
      return;
    }
    let sp = node.getComponent(Sprite);
    if (!sp) {
      console.error("setItemIcon: node is not have Sprite");
      return;
    }
    try {
      ResMgr.loadImage(`${BundleEnum.BUNDLE_COMMON_ITEM}?autoItem/item_${itemId}`, sp);
    } catch (e) {
      ResMgr.loadImage(`${BundleEnum.BUNDLE_COMMON_ITEM}?autoItem/item_0`, sp);
    }
  }

  /**
   *
   * 设置物品信息通用结点，
   * 使用方法 assets\bundle_common_ui\prefabs\Item.prefab 拖到界面，然后配置好，调用这个方法
   *
   * @param itemNode 物品结点，预置体路径
   * @param id 物品ID
   * @param num 数量
   * @param stopPop 是否停用弹窗
   */
  public static setItemNode(
    itemNode: Node,
    id: number,
    num: number = -1,
    stopPop: boolean = false,
    tipPaddingLeft?: number,
    tipPaddingRight?: number
  ) {
    const itemCtrl: any = itemNode.getComponent("ItemCtrl");
    if (!itemCtrl) {
      console.error("ItemCtrl not found");
    }

    itemCtrl.setItemId(id, num, stopPop, tipPaddingLeft, tipPaddingRight);
  }

  /**
   * 设置窗口标题
   * @param node 窗口结点
   * @param title 标题
   * @returns
   */
  public static setDialogTitle(node: Node, title: string) {
    const dialogTitleCtrl: any = node.getComponent("DialogEvent");
    if (!dialogTitleCtrl) {
      console.error("DialogEvent not found");
      return;
    }
    dialogTitleCtrl.setTitle(title);
  }

  public static setDialogTitleX(node: Node, code: number, args: any[] = [], defaultMsg: string = "") {
    const dialogTitleCtrl: any = node.getComponent("DialogEvent");
    if (!dialogTitleCtrl) {
      console.error("DialogEvent not found");
      return;
    }
    dialogTitleCtrl.setTitleX(code, args, defaultMsg);
  }

  /**
   * 设置头像
   * 使用方法 assets\bundle_common_ui\prefabs\BtnHeader.prefab
   * @param headNode 头像结点
   * @param args 参数
   */
  public static setHeaderNode(headNode: Node, args: HeadInfo, isRobot: boolean = false) {
    const btnHeaderCtrl: any = headNode.getComponent("BtnHeaderCtrl");
    if (!btnHeaderCtrl) {
      console.error("btnHeaderCtrl not found");
    }

    btnHeaderCtrl.setHeader(args, isRobot);
  }

  /**
   * 节点变暗功能
   * @param node 要变暗的节点
   * @param darkValue 可以配置的值，默认为100
   */
  public static setNodeDark(node: Node, darkValue: number = 100) {
    let color = new Color(darkValue, darkValue, darkValue, 255);

    function setColor(node: Node) {
      let sp = node.getComponent(Sprite);
      sp && (sp.color = color);
    }

    setColor(node);

    node.children.forEach((child) => {
      FmUtils.setNodeDark(child, darkValue);
    });
  }

  /**
   * 设置倒计时
   * @param node 传入的设置时间节点
   * @param ts 未来时间戳毫秒ms
   * @param endHide 结束是否隐藏
   * @param callBack 关闭后的回调事件
   */
  public static setCd(node: Node, ts: number, endHide: boolean = false, callBack: Function = null) {
    const lblCd: any = node.getComponent("LblCd");
    if (!lblCd) {
      console.error("lblCd not found");
    }

    lblCd.setCd(ts, endHide, callBack);
  }

  /**
   * 获取权限需要的条件值
   *
   * @param id 权限ID
   * @param type 条件ID
   * @returns
   */
  public static getRightCondition(id: number, type: RightConditionEnum): number {
    let cfg = JsonMgr.instance.jsonList.c_right[id];
    if (!cfg) {
      console.error("c_right not found " + id);
      return 0;
    }

    for (let i = 0; i < cfg.openList.length; i++) {
      if (cfg.openList[i][0] == type) {
        return cfg.openList[i][1];
      }
    }
    return 0;
  }

  /**道具飞行光效动作 */
  public static async itemFlyEffect(node: Node, pos: Vec3, callback?: Function) {
    node.active = true;
    node.getComponent(sp.Skeleton).setAnimation(0, "kuang", false);
    await new Promise(async (res) => {
      node.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == "kuang") {
          node.getComponent(sp.Skeleton).setCompleteListener(null);
          node.getComponent(sp.Skeleton).setAnimation(0, "tuowei", false);
          res(true);
        }
      });
    });
    await new Promise(async (res) => {
      tween(node)
        .to(0.35, { position: pos })
        .call(() => {
          node.getComponent(sp.Skeleton).setAnimation(0, "quan", false);
          res(true);
        })
        .start();
    });
    await new Promise(async (res) => {
      node.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == "skt") {
          node.active = false;
          res(true);
        }
      });
    });

    callback && callback();
  }

  /**
   * 判断两个数组是不是不同
   * @param oldArray 数组1
   * @param newArray 数组2
   * @returns
   */
  public static isArrayDiff<T>(oldArray: T[], newArray: T[]): boolean {
    if (oldArray.length !== newArray.length) return true;

    // 使用 Set 进行浅比较（不考虑顺序）
    const oldSet = new Set(oldArray);
    const newSet = new Set(newArray);

    if (oldSet.size !== newSet.size) return true;

    for (const item of oldSet) {
      if (!newSet.has(item)) return true;
    }

    return false;
  }
}
