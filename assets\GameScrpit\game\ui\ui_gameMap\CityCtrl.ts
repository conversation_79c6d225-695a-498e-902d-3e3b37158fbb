import { _decorator, Component, director, find, is<PERSON><PERSON><PERSON>, <PERSON><PERSON>, RichText, <PERSON><PERSON><PERSON>, twe<PERSON>, UITransform } from "cc";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { CityModule } from "../../../module/city/CityModule";
import { CityAudioName, ConfigBuildRecord } from "../../../module/city/CityConstant";
import { Sprite } from "cc";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { Label } from "cc";
import { FightModule } from "../../../module/fight/src/FightModule";
import { JsonMgr } from "../../mgr/JsonMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { CityRouteName } from "../../../module/city/CityConstant";
import { sp } from "cc";
import { instantiate } from "cc";
import { AssetMgr } from "../../../../platform/src/ResHelper";
import { Button } from "cc";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { CityEvent } from "../../../module/city/CityEvent";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { AddNumCtrl } from "./AddNumCtrl";
import Formate from "../../../lib/utils/Formate";
import { HeartBeatSec, Sleep } from "../../GameDefine";
import { BoxShow } from "../city/component/BoxShow";
import { NodeTool } from "../../../lib/utils/NodeTool";
import { PlayerEvent } from "../../../module/player/PlayerEvent";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { CityCreateResponse } from "../../net/protocol/City";
import { RewardRouteEnum } from "../../../ext_reward/RewardDefine";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const enum CityStatusEnum {
  RUINS,
  TO_FIGHT,
  TO_BUILD,
  TO_MANAGE,
}

export interface ArgsCityCtrl {
  // 样式等级
  cityLevel: number;
  // 隐藏ui组件
  hideUI: boolean;
  // 显示标签
  showName?: boolean;
}

const aniNameList = ["a0", "a01", "a02", "a03", "a04", "a05", "a06"];

@ccclass("CityCtrl")
export class CityCtrl extends Component {
  private _showBgNum: boolean = true;
  public get showBgNum() {
    return this._showBgNum;
  }
  public set showBgNum(value) {
    this._showBgNum = value;
    this.initCity(this._cityId);
  }

  //---------------------------------------分割线----------------------------------------------
  private assetMgr: AssetMgr = null;
  private _cityStatus: CityStatusEnum = CityStatusEnum.RUINS;
  private _cityId: number = 0;
  public get cityId() {
    return this._cityId;
  }

  public set cityId(value) {
    this._cityId = value;
  }

  // 当前形象等级
  private _imgIndex: number = null;
  private get imgIndex() {
    return this._imgIndex || 0;
  }

  private set imgIndex(value) {
    this._imgIndex = value;
  }

  // 当前等级
  private _level: number = 0;
  private get level() {
    return this._level;
  }
  private set level(value) {
    this._level = value;
  }

  private _build_need_item_lay: Node;

  private _btn_manage: Node = null;

  private _node_effect_ruins: Node = null;

  private _btn_build: Node = null;
  private _btn_fight: Node = null;

  private _ani_build: Node = null;
  private _lbl_name: Node = null;

  /**人口数节点背景 */
  private _populace: Node = null;
  /**人口数量lab */
  private _lbl_populace: Node = null;

  private _isLoadEnd: boolean = false;

  private _argsCityCtrl: ArgsCityCtrl = {
    cityLevel: null,
    hideUI: false,
  };

  public setOnlyShow(args: ArgsCityCtrl = null) {
    /**为展示而创建的建筑，立刻调用onDisable注销监听事件 */
    this.onDisable();
    this.showBgNum = false;
    this._argsCityCtrl = Object.assign(this._argsCityCtrl, args);
    this.node.children.forEach((child) => {
      if (child.name == "btn_manage") {
        return;
      }
      child.active = false;
    });

    this.node.getComponentsInChildren(Button).forEach((btn) => ((btn.interactable = false), (btn.enabled = false)));
  }
  protected onEnable(): void {
    if (this._isLoadEnd == true) {
      this.initCity(this.cityId);
    }

    if (this._argsCityCtrl.hideUI) {
      return;
    }

    MsgMgr.on(MsgEnum.ON_CITY_UPDATE, this.initCity, this);
    MsgMgr.on(CityEvent.ON_CITY_ANI, this.playAddPeople, this);
    MsgMgr.on(MsgEnum.ON_HEART_BEAT, this.onHartBeat, this);
    MsgMgr.on(MsgEnum.ON_CITY_HAO_ZHAO_BOX_ADD, this.boxShowAni, this);
    const cfg = JsonMgr.instance.jsonList.c_build[this.cityId];
    if (
      !CityModule.data.cityMessageMap.get(this.cityId)?.level &&
      cfg.goldSpeed &&
      Number(cfg.goldSpeed) > PlayerModule.data.getItemNum(ItemEnum.繁荣度_2)
    ) {
      MsgMgr.on(PlayerEvent.ON_PLAYER_BLOOM_CHANGE, this.stateUp, this);
    }
  }
  protected onDisable(): void {
    if (this._argsCityCtrl.hideUI) {
      return;
    }
    MsgMgr.off(MsgEnum.ON_CITY_UPDATE, this.initCity, this);
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.upCityNeed, this);
    MsgMgr.off(CityEvent.ON_CITY_ANI, this.playAddPeople, this);
    MsgMgr.off(MsgEnum.ON_HEART_BEAT, this.onHartBeat, this);
    MsgMgr.off(MsgEnum.ON_CITY_HAO_ZHAO_BOX_ADD, this.boxShowAni, this);

    MsgMgr.off(PlayerEvent.ON_PLAYER_BLOOM_CHANGE, this.stateUp, this);
  }

  protected onDestroy(): void {
    this.assetMgr.release();
    MsgMgr.off(MsgEnum.ON_CHAPTER_PASS, this.stateUp, this);
  }
  protected onLoad(): void {
    MsgMgr.on(MsgEnum.ON_CHAPTER_PASS, this.stateUp, this);
    //log.log("展示建筑的uuid===", this.node.uuid);
    this.assetMgr = AssetMgr.create();
    this.cityId = Number(this.node.name.split("_")[1]);
    //log.log("初始化的据点====", this.node.name);

    this._btn_manage = find("btn_manage", this.node);
    this._btn_manage.active = true;
    //this._btn_manage.on(Input.EventType.TOUCH_END, ToolExt.tryFunc(this.onClick.bind(this)), this);

    this._node_effect_ruins = find("node_effect_ruins", this.node);

    this._btn_build = find("btn_build", this.node);
    this._btn_build.active = false;

    this._build_need_item_lay = find("Layout", this._btn_build);

    this._btn_fight = find("btn_fight", this.node);
    this._btn_fight.active = false;

    this._ani_build = find("ani_build", this.node);
    this._ani_build.active = false;
    this._ani_build.getComponent(sp.Skeleton).useTint = true;

    this._lbl_name = find("btn_name/lbl_name", this.node);

    this._populace = find("bg_num", this.node);

    this._lbl_populace = find("lbl_num", this._populace);

    this._isLoadEnd = true;

    this.level = CityModule.data.cityMessageMap.get(this.cityId)?.level || 0;
    this.imgIndex = CityModule.service.getCityImgIndex(this.level, this.cityId);
  }

  protected start(): void {
    this.initCity(this.cityId);
    this.addRdBadge();
  }

  private initCity(id: number) {
    if (id != this.cityId || this._cityId == 0) {
      return;
    }

    this.stateUp();
    this.setCityDetail();

    this.upCityNeed();
  }

  /**添加红点 */
  private addRdBadge() {
    // 名字红点
    BadgeMgr.instance.setBadgeId(
      this.node.getChildByName("btn_name"),
      BadgeType.UIMajorCity["btn_name" + this.cityId].id
    );
    // 建造红点
    BadgeMgr.instance.setBadgeId(this._btn_build, BadgeType.UIMajorCity["build" + this.cityId].id);
  }

  private setCityDetail() {
    // 建筑信息
    let cityInfo = CityModule.data.cityMessageMap.get(this.cityId);
    const configBuild: ConfigBuildRecord = CityModule.data.getConfigBuild(this.cityId);
    // 设置名称
    let str = "<color=FFF9EB>";
    for (let i = 0; i < configBuild.name.length; i++) {
      str += configBuild.name[i] + "<br/>";
    }
    this._lbl_name.getComponent(RichText).string = `${str}</color>`;

    if (cityInfo) {
      let str = "<color=FFF9EB>";
      str += `<color=fff17f>${cityInfo.level}<br/>级</color>`;
      this._lbl_name.getComponent(RichText).string += str;

      this._lbl_populace.getComponent(Label).string = String(cityInfo.energyHire + cityInfo.itemHire);
    }
    this.setBuildAni();
  }

  /**初始进入时建筑展示 */
  private async setBuildAni() {
    let data = CityModule.data.cityMessageMap.get(this.cityId);
    if (!data) {
      data = {
        cityId: this.cityId,
        level: 0,
        itemHire: 0,
        energyHire: 0,
      };
    }
    const cityInfo = JSON.parse(JSON.stringify(data));

    cityInfo.level = this._argsCityCtrl.cityLevel || data.level;

    if (this._argsCityCtrl.hideUI) {
      this._lbl_name.active = this._argsCityCtrl.showName;
    }

    if (this._argsCityCtrl.hideUI == true) {
      // 最新形象
      let imgIndex = CityModule.service.getCityImgIndex(cityInfo.level, cityInfo.cityId);
      this._imgIndex = imgIndex;
    }

    // 是否升级
    if (this.level < cityInfo.level && this._argsCityCtrl.hideUI == false) {
      // 最新形象
      let imgIndex = CityModule.service.getCityImgIndex(cityInfo.level, cityInfo.cityId);
      // 播放敲打动画
      if (this.imgIndex < imgIndex) {
        // 动画结束 更新数据,
        this._level = cityInfo.level;
        this._imgIndex = imgIndex;

        this.aniBuildCity();
        await Sleep(1);
      }

      // 播放升级闪光
      let x = this.node.getComponent(UITransform).contentSize.x;
      let wPos = this.node.getWorldPosition();
      TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopLevelUp, { width: x, wPos: wPos });

      // 动画结束 更新数据
      this._level = cityInfo.level;
      this._imgIndex = imgIndex;
    } else {
      await this.aniCitySkt("-1");
    }

    // // 最新形象
    // let imgIndex = CityModule.service.getCityImgIndex(cityInfo.level, cityInfo.cityId);

    // if (this.imgIndex >= imgIndex) {
    //   return;
    // } else if (this.imgIndex < imgIndex && this._argsCityCtrl.hideUI != true) {
    //   /**这里代表建筑形象等级在游戏过程中提升了，走建筑锤子敲打动画 */
    //   this.aniBuildCity();
    //   return;
    // }
    // this._imgIndex = imgIndex;

    // await this.aniCitySkt("-1");
  }

  private aniBuildCity() {
    this._btn_manage.active = false;
    this._ani_build.active = true;
    const spineJianZao = this._ani_build.getComponent(sp.Skeleton);

    spineJianZao.setAnimation(0, "jianzao", false);

    tween(this.node)
      .delay(0.23)
      .call(() => {
        this._btn_manage.active = true;
        this.aniCitySkt();
      })
      .delay(2.233)
      .call(() => {
        this._ani_build.active = false;
      })
      .start();
  }

  private async aniCitySkt(param: string = "") {
    const nodeSprite = this._btn_manage.getChildByName("bg");
    if (nodeSprite) {
      // 图片名称
      let nodeName = `C${this.cityId}-${this.imgIndex}`;
      // 取最高级图片
      for (let i = 0; i < 5; i++) {
        nodeName = `C${this.cityId}-${this.imgIndex - i}`;
        let spf = await this.assetMgr.loadSpriteFrameSync(`bundle_city_${this.cityId}`, `images/${nodeName}`);
        if (spf) {
          nodeSprite.getComponent(Sprite).spriteFrame = spf;
          break;
        }
      }
    } else {
      this._btn_manage.active = true;
      const ani = this._btn_manage.getChildByName("ani").getComponent(sp.Skeleton);

      if (param) {
        SpineUtil.playOneByOne(ani, aniNameList[this.imgIndex] + param);
      } else {
        SpineUtil.playOneByOne(ani, aniNameList[this.imgIndex], aniNameList[this.imgIndex] + "-1");
      }
    }
  }

  /**
   * 初始化建筑状态
   * @returns
   */
  private stateUp() {
    // 建筑信息
    let cityInfo = CityModule.data.cityMessageMap.get(this.cityId);
    // 当前通关关卡id
    let chapterId = FightModule.data.chapterId;
    // 通关信息
    let configChapter = JsonMgr.instance.getConfigCopyMain(chapterId);

    const configBuild: ConfigBuildRecord = CityModule.data.getConfigBuild(this.cityId);
    // 运营状态
    if (!cityInfo || cityInfo.level == 0) {
      if (configChapter && chapterId > configBuild.unlockLv) {
        this._cityStatus = CityStatusEnum.TO_BUILD;
      } else if (configChapter && configChapter.buildId == this.cityId) {
        this._cityStatus = CityStatusEnum.TO_FIGHT;
      } else {
        this._cityStatus = CityStatusEnum.RUINS;
      }
    } else if (cityInfo.level == 0) {
      this._cityStatus = CityStatusEnum.TO_BUILD;
    } else {
      this._cityStatus = CityStatusEnum.TO_MANAGE;
    }

    if (this._argsCityCtrl.hideUI == true) {
      return;
    }

    this.isShowBtnBuild();
    this.isShowBtnFight();
    this.isShowPopulace();
    this.isShowRunisEffect();
  }

  private isShowBtnBuild() {
    // 建造按钮
    this._btn_build.active = this._cityStatus == CityStatusEnum.TO_BUILD;
    // 繁荣度要求显示
    if (this._btn_build.active) {
      MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.upCityNeed, this);
      MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.upCityNeed, this);

      const configBuild = JsonMgr.instance.jsonList.c_build[this.cityId];
      if (PlayerModule.data.getItemNum(ItemEnum.繁荣度_2) < Number(configBuild.goldSpeed)) {
        this._btn_build.active = false;
      }
    }
  }

  private upCityNeed() {
    const configBuild = JsonMgr.instance.jsonList.c_build[this.cityId];
    let lab_qiyun = this._build_need_item_lay.getChildByName("lab_qiyun");
    let num = configBuild.buildCost;
    lab_qiyun.getComponent(Label).string = Formate.format(num);

    let myNum = PlayerModule.data.getItemNum(ItemEnum.气运_1);

    if (myNum >= num) {
      this._btn_build.getComponent(Sprite).enabled = true;
      this._btn_build.getChildByName("disable").active = false;
      this._btn_build.getChildByName("build").active = true;
    } else {
      this._btn_build.getChildByName("disable").active = true;
      this._btn_build.getChildByName("build").active = false;
      this._btn_build.getComponent(Sprite).enabled = false;
    }
  }

  private isShowBtnFight() {
    // 战斗按钮
    this._btn_fight.active = this._cityStatus == CityStatusEnum.TO_FIGHT;
  }

  private isShowPopulace() {
    // 人口数量
    this._populace.active = this._cityStatus == CityStatusEnum.TO_MANAGE && this.showBgNum;
  }

  private isShowRunisEffect() {
    // 废墟状态特效
    this._node_effect_ruins.active = this._cityStatus != CityStatusEnum.TO_MANAGE;
  }

  /**点击事件 */
  private onClick() {
    AudioMgr.instance.playEffect(CityAudioName.Effect.点击建筑图标);
    if (this._cityStatus == CityStatusEnum.TO_FIGHT) {
      UIMgr.instance.showDialog(CityRouteName.UICityFight, { cityId: this.cityId });
    } else if (this._cityStatus == CityStatusEnum.TO_BUILD) {
      // UIMgr.instance.showDialog(CityRouteName.UICityBuild, { cityId: this.cityId });
      this.goBuild();
    } else if (this._cityStatus == CityStatusEnum.TO_MANAGE) {
      UIMgr.instance.showPage(CityRouteName.UICityDetail, [this.cityId]);
    } else if (this._cityStatus == CityStatusEnum.RUINS) {
      const cfg = JsonMgr.instance.jsonList.c_build[this.cityId];
      if (!cfg) return;
      const db = JsonMgr.instance.jsonList.c_copyMain[cfg.unlockLv];
      if (!db) return;
      TipsMgr.showTipX(242, [db.des], "");
    }
  }

  // 建造
  private goBuild() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);

    let configBuild = CityModule.data.getConfigBuild(this._cityId);

    let lackItem = PlayerModule.service.checkitemEnought([
      ItemEnum.气运_1,
      configBuild.buildCost,
      ItemEnum.繁荣度_2,
      Number(configBuild.goldSpeed),
    ]);

    if (lackItem.length > 0) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: lackItem[0],
        needNum: lackItem[1],
      });
      return;
    }

    TipsMgr.setEnableTouch(false, 7, false);
    CityModule.api.createCity(this._cityId, (cityCreate: CityCreateResponse) => {
      AudioMgr.instance.playEffect(CityAudioName.Effect.解锁成功);
      // 取消繁荣度监听
      MsgMgr.off(PlayerEvent.ON_PLAYER_BLOOM_CHANGE, this.stateUp, this);
      TipsMgr.setEnableTouch(false, 7, false);
      setTimeout(() => {
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: cityCreate.rewardList });
      }, 1560);
    });
  }

  // 招募特效
  public playAddPeople(cityId: number) {
    if (this.cityId != cityId) {
      return;
    }
    // 当前建筑配置
    const configBuild: ConfigBuildRecord = CityModule.data.getConfigBuild(this.cityId);
    // 传送门节点
    let nodeAniGate = this.node.getChildByName("node_effect_call_parent").getChildByName("ani_gate");
    // 人族传送门出现动画
    let aniGate = nodeAniGate.getComponent(sp.Skeleton);
    let gateAniName3 = "animation3";

    if (!nodeAniGate.active) {
      nodeAniGate.active = true;
      // 默认动画人族
      let gateAniName1 = "animation1";
      let gateAniName2 = "animation2";
      // 妖族动画
      if (configBuild.type == 3) {
        gateAniName1 = "action1";
        gateAniName2 = "action2";
      } else if (configBuild.type == 4) {
        gateAniName1 = "guimen1";
        gateAniName2 = "guimen2";
      }
      // 播放动画出现
      aniGate.setAnimation(0, gateAniName3, false);
      aniGate.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        // if ("animation1" == trackEntry.animation.name) {
        //   aniGate.setAnimation(0, gateAniName3, false);
        // } else {
        aniGate.setCompleteListener(null);
        aniGate.setAnimation(0, gateAniName2, true);
        // }
      });
    }

    // 设置门关闭时间
    Tween.stopAllByTarget(nodeAniGate.parent);
    aniGate.setAnimation(0, gateAniName3, false);
    tween(nodeAniGate.parent)
      .delay(3)
      .call(() => {
        nodeAniGate.active = false;
      })
      .start();

    // 人物出场动画
    this.assetMgr.loadPrefab(BundleEnum.BUNDLE_G_GAME_MAP, "prefab/AniPeople", (pb) => {
      if (this.isValid == false) return;

      let nodeAni = instantiate(pb);
      this.node.getChildByName("node_effect_call_parent").addChild(nodeAni);
      nodeAni.getComponent("AniPeople").playAni(configBuild.type, nodeAniGate);
    });
  }

  // 收益跳动
  private onHartBeat() {
    if (this._argsCityCtrl.hideUI == true) {
      return;
    }

    if (CityModule.data.getCityBloom(this.cityId) > 0 && this.node.active) {
      AddNumCtrl.showByPos(
        this.node.getWorldPosition(),
        `+${Formate.format(CityModule.data.getCityBloom(this.cityId) * HeartBeatSec, 4)}`,
        150,
        false,
        2
      );
    }
  }

  /**
   * 宝箱出现动画
   * @param cityId 城市ID
   */
  private async boxShowAni(cityId: number) {
    if (this.cityId === cityId) {
      // 当前建筑配置
      AudioMgr.instance.playEffect(1508);
      const configBuild: ConfigBuildRecord = CityModule.data.getConfigBuild(this.cityId);

      let nodeUICityDetail = NodeTool.findByName(director.getScene(), CityRouteName.UICityDetail);

      const pb = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_MAJORCITY, "prefab/components/BoxShow");
      if (isValid(this.node)) {
        let nodeBox = instantiate(pb);
        TipsMgr.topRouteCtrl.showNode(nodeBox, {});
        // 门位置
        let posGate = this.node.getChildByName("node_effect_call_parent").getChildByName("ani_gate").getWorldPosition();
        let posEnd = NodeTool.findByName(nodeUICityDetail, "btn_hao_zhao_box").getWorldPosition();
        nodeBox.getComponent(BoxShow).startAni(posGate, posEnd, configBuild.type);
      }
    }
  }
}
