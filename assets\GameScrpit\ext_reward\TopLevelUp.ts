import { _decorator, sp, Vec3 } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { SpineUtil } from "../../platform/src/lib/utils/SpineUtil";
import { Sleep } from "../game/GameDefine";
const { ccclass, property } = _decorator;

@ccclass("TopLevelUp")
export class TopLevelUp extends BaseCtrl {
  wPos: Vec3;

  // 是否显示敲敲打打
  showBuild: boolean = false;

  public init(args: { wPos: Vec3; width: 100; showBuild: false }): void {
    super.init(args);

    // 设置播放位置
    this.wPos = args.wPos;

    // 设置缩放
    const s1 = (args.width || 1) / 350;
    this.node.setScale(s1, s1, 1);

    this.showBuild = args.showBuild;
  }

  protected async start(): Promise<void> {
    super.start();
    this.node.setWorldPosition(this.wPos);

    // 敲敲打打动画
    if (this.showBuild) {
      this.getNode("spine_build").active = true;
      this.getNode("spine_levelup").active = false;
      const spine = this.getNode("spine_build").getComponent(sp.Skeleton);
      const dt = SpineUtil.getSpineDuration(spine, "jianzao");
      SpineUtil.playSpine(spine, "jianzao", false);
      await Sleep(dt);
    } else {
      this.getNode("spine_build").active = false;
      this.getNode("spine_levelup").active = true;
      const spine = this.getNode("spine_levelup").getComponent(sp.Skeleton);
      SpineUtil.playSpine(spine, "action", false);
      const dt = SpineUtil.getSpineDuration(spine, "action");
      await Sleep(dt);
    }
    this.closeBack();
  }
}
