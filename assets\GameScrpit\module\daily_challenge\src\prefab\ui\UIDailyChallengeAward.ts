import { _decorator, Component, Node } from "cc";
import { JsonMgr } from "db://assets/GameScrpit/game/mgr/JsonMgr";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { DailyChallengeAwardAdapter } from "../../adapter/DailyChallengeAwardViewHolder";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
const { ccclass, property } = _decorator;

@ccclass("UIDailyChallengeAward")
@routeConfig({
  bundle: BundleEnum.BUNDLE_DAILY_CHALLENGE,
  url: "prefab/ui/UIDailyChallengeAward",
  nextHop: [],
  exit: "dialog_close",
})
export class UIDailyChallengeAward extends BaseCtrl {
  public playShowAni: boolean = true;
  start() {
    super.start();
    let data = Object.values(JsonMgr.instance.jsonList.c_dailyChallengeRank);
    let adapter = new DailyChallengeAwardAdapter(this.getNode("viewholder_award"));
    this.getNode("node_list").getComponent(AdapterView).setAdapter(adapter);
    adapter.setData(data);
  }

  update(deltaTime: number) {}
}
