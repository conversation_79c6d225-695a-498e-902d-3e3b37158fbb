import { _decorator, color, Label, Node, ProgressBar, sp, Sprite } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { ConfigSoulRecord } from "../../bundleDefine/bundle_soul_define";
import { UIMgr } from "../../../lib/ui/UIMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import Formate from "../../../lib/utils/Formate";
import { SoulModule } from "../../../module/soul/SoulModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { SoulRouteName } from "../../../module/soul/SoulRoute";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import { SoulAudioName, SoulColor } from "../../../module/soul/SoulConstant";
import { SpriteFrame } from "cc";
import { instantiate } from "cc";
import TipMgr from "../../../lib/tips/TipMgr";
import { WarriorBuySoulResponse } from "../../net/protocol/Soul";
import MsgEnum from "../../event/MsgEnum";
import { ConfirmMsg } from "../UICostConfirm";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import { ItemCost } from "../../common/ItemCost";
import { StateButton } from "db://assets/platform/src/core/ui/components/StateButton";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const colors = ["#60B1A5", "#75A4DD", "#832BB6", "#E6CF4C", "#E57D58"];
const bgs = ["S1301", "S1302", "S1303", "S0892", "S0893"];
@ccclass("UISoulList")
export class UISoulList extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_SOUL}?prefab/ui/UISoulList`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  /**基础属性获取 */
  // private _configBaseSoul: ConfigSoulRecord = null;

  /**当前购买的武魂 index */
  private _currentIndex = 0;

  public init(args: any): void {
    super.init(args);
  }

  protected onRegEvent() {
    MsgMgr.on(MsgEnum.ON_SOUL_UPDATE, this.refresh, this);
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.onItemChange, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_SOUL_UPDATE, this.refresh, this);
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.onItemChange, this);
  }

  protected onEvtShow(): void {
    this.refresh(false);

    // 绑定事件
    const nodeParent = this.getNode("layout_sell");
    for (let i = 0; i < 3; i++) {
      let nodeSoul = nodeParent.children[i];
      // 绑定切换事件
      nodeSoul.getChildByName("btn_soul_item").on(Node.EventType.TOUCH_END, () => {
        AudioMgr.instance.playEffect(SoulAudioName.Effect.点击兽魂图标);
        this.onSelectChange(i);
      });

      // 绑定购买事件
      nodeSoul.getChildByName("btn_get").on(Node.EventType.TOUCH_END, () => {
        AudioMgr.instance.playEffect(SoulAudioName.Effect.点击购买按钮);
        this.onBuy(i);
      });
    }

    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_free_refresh"),
      BadgeType.UITerritory.btn_soul.btn_tab_soul.btn_get_soul.btn_free_refresh.id
    );
  }

  onEvtClose() {
    this.assetMgr.release();
  }

  onItemChange(id: number) {
    let cfgBaseSoul = SoulModule.data.getConfigSoul();

    // 关注的id列表
    let idList = [];

    function add(id: number) {
      if (!idList.includes(id)) {
        idList.push(id);
      }
    }

    add(cfgBaseSoul.costList[0]);
    add(cfgBaseSoul.costRefreshCostList[0]);
    add(cfgBaseSoul.priceList[0]);

    if (idList.includes(id)) {
      this.refresh();
    }
  }

  /**
   *
   * @param upIs true是指刷新，false 是初始化
   */
  refresh(upIs: boolean = false) {
    // 基础配置
    let cfgBaseSoul = SoulModule.data.getConfigSoul();

    // 消耗资源数量
    this.getNode("node_res").getComponent(ItemCost).setItemId(cfgBaseSoul.costRefreshCostList[0]);

    const nodeParent = this.getNode("layout_sell");
    nodeParent.children.forEach((child) => (child.active = false));

    const sellList = SoulModule.data.warriorSoulManageMsg.refreshTemplateIdList;
    for (let i = 0; i < sellList.length; i++) {
      let templateId = sellList[i];
      let cfgSoul = SoulModule.data.getConfigSoul(templateId);

      let nodeSoul = nodeParent.children[i];

      // 高级光效
      nodeSoul.getChildByName("ani_light").active = cfgSoul.color >= 4;

      // 选中状态
      nodeSoul.getChildByPath("btn_soul_item/bg_select").active = false;

      // 设置背景
      this.assetMgr.loadSpriteFrame("bundle_g_soul", `images/WH_kuang_${cfgSoul.color}`, (spr: SpriteFrame) => {
        nodeSoul.getChildByPath("btn_soul_item/bg_color").getComponent(Sprite).spriteFrame = spr;
        // 设置武魂图标
        this.assetMgr.loadSpriteFrame("bundle_g_soul", `/images/wh_${templateId}`, (spr: SpriteFrame) => {
          nodeSoul.getChildByPath("btn_soul_item/bg_icon").getComponent(Sprite).spriteFrame = spr;
          nodeSoul.active = true;
          if (upIs == true) {
            let key = "11";
            if (cfgSoul.color >= 4) {
              key = "22";
            }

            let skt_up = nodeSoul.getChildByName("skt_up") as Node;
            skt_up.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
              if (trackEntry.animation.name == key) {
                skt_up.active = false;
                skt_up.getComponent(sp.Skeleton).setCompleteListener(null);
              }
            });
            skt_up.active = true;
            SpineUtil.playSpine(skt_up.getComponent(sp.Skeleton), key, false);
          }
        });
      });

      // 设置名称
      let lblName: Label = nodeSoul.getChildByPath("btn_soul_item/lbl_name").getComponent(Label);
      lblName.string = cfgSoul.name;
      lblName.outlineColor = color(SoulColor[cfgSoul.color]);

      nodeSoul.getChildByName("lbl_wanman").active = false;
      nodeSoul.getChildByName("lbl_buycount").active = false;

      // let index = this.getHasNum(templateId);
      let plantLength = SoulModule.data.warriorSoulManageMsg.planList.length;
      // let index = SoulModule.data.warriorSoulManageMsg.soulHoldMap[templateId];
      for (let i = 1; i < 6; i++) {
        let node = nodeSoul.getChildByPath(`node_tujian/node_tujian${i}`);
        node.getComponent(StateButton).state = 1;
      }
      for (let i = 0; i < plantLength - 1 && i < 5; i++) {
        let node = nodeSoul.getChildByPath(`node_tujian/node_tujian${i + 1}`);
        node.getComponent(StateButton).state = 0;
      }
      let curState = SoulModule.data.warriorSoulManageMsg.soulHoldSet.find((soul) => {
        return soul == templateId;
      });
      if (curState) {
        nodeSoul.getChildByPath(`node_tujian/node_tujian${plantLength}`).getComponent(StateButton).state = 1;
      }
      // let plant_length = SoulModule.data.warriorSoulManageMsg.planList.length;
      for (let i = plantLength; i < 5; i++) {
        let node = nodeSoul.getChildByPath(`node_tujian/node_tujian${i + 1}`);
        node.getComponent(StateButton).state = 2;
      }

      // 是否已购买
      let sellOut = SoulModule.data.warriorSoulManageMsg.chosenIndexList[i];
      nodeSoul.getChildByName("btn_get").active = !sellOut;
      nodeSoul.getChildByName("node_finish").active = sellOut;
      nodeSoul.getChildByName("lbl_item_cost").active = !sellOut;

      // 设置消耗
      FmUtils.setItemIcon(nodeSoul.getChildByPath("lbl_item_cost/item_icon"), cfgSoul.priceList[0]);
      nodeSoul.getChildByName("lbl_item_cost").getComponent(Label).string = Formate.format(cfgSoul.priceList[1]);
    }

    // 更新进度条
    const progressBar = this.getNode("progress_bar").getComponent(ProgressBar);
    progressBar.progress = SoulModule.data.warriorSoulManageMsg.exp / cfgBaseSoul.min;

    this.getNode("lbl_progress").getComponent(
      Label
    ).string = `${SoulModule.data.warriorSoulManageMsg.exp} / ${cfgBaseSoul.min}`;

    // 每日免费刷新状态
    let lblFreeTip = this.getNode("btn_free_refresh").getChildByName("lbl_tip").getComponent(Label);
    let freeLeft = cfgBaseSoul.freeRefreshMax - SoulModule.data.warriorSoulManageMsg.freeCount;
    if (freeLeft < 0) {
      // freeLeft = 0;
      log.error("每日免费刷新次数超限" + cfgBaseSoul.freeRefreshMax, SoulModule.data.warriorSoulManageMsg.freeCount);
    }
    lblFreeTip.string = `每天${freeLeft}/${cfgBaseSoul.freeRefreshMax}次`;

    // 每日高级刷新次数
    let lblCostTip = this.getNode("btn_cost_refresh").getChildByName("lbl_tip").getComponent(Label);
    lblCostTip.string = `剩余${cfgBaseSoul.costRefreshMax - SoulModule.data.warriorSoulManageMsg.paidCount}次`;

    // 高级刷新消耗
    let userNum = PlayerModule.data.getItemNum(cfgBaseSoul.costRefreshCostList[0]);
    let lblCost = this.getNode("btn_cost_refresh").getChildByName("lbl_cost").getComponent(Label);
    lblCost.string = `${userNum}/${cfgBaseSoul.costRefreshCostList[1]}`;

    // 消耗图标
    FmUtils.setItemIcon(
      this.getNode("btn_cost_refresh").getChildByPath("lbl_cost/bg_icon"),
      cfgBaseSoul.costRefreshCostList[0]
    );

    // 免费刷新
    this.getNode("btn_free_refresh").getComponent(Sprite).grayscale =
      cfgBaseSoul.freeRefreshMax <= SoulModule.data.warriorSoulManageMsg.freeCount;
    // 高级刷新
    this.getNode("btn_cost_refresh").getComponent(Sprite).grayscale =
      cfgBaseSoul.costRefreshMax <= SoulModule.data.warriorSoulManageMsg.paidCount;

    // 刷新选中状态
    this.onSelectChange(this._currentIndex);
  }

  private getHasNum(soulId: number) {
    let warriorSoulManageMsg = SoulModule.data.warriorSoulManageMsg;
    let index = 0;

    let list = Object.keys(warriorSoulManageMsg.soulMap);

    for (let i = 0; i < list.length; i++) {
      let data = warriorSoulManageMsg.soulMap[list[i]];
      if (data.soulTemplateId == soulId) {
        index++;
      }
    }

    return index;
  }

  // 切换选中武魂
  private onSelectChange(idx: number) {
    this._currentIndex = idx;

    // 选中状态
    const nodeSell = this.getNode("layout_sell");
    for (let i = 0; i < nodeSell.children.length; i++) {
      let nodeItem = nodeSell.children[i];
      let nodeBgActive = nodeItem.getChildByPath("btn_soul_item/bg_select");
      nodeBgActive.active = i == idx;
    }

    let templateId = SoulModule.data.warriorSoulManageMsg.refreshTemplateIdList[this._currentIndex];
    let configSoul: ConfigSoulRecord = SoulModule.data.getConfigSoul(templateId);

    // 主属性
    this.getNode("lbl_main_attr").getComponent(Label).string = Formate.formatAttribute(
      configSoul.firstMasterAttrList[0],
      configSoul.firstMasterAttrList[1] / 10000
    );

    // 基本属性
    const layout = this.getNode("layout_base_attr");
    layout.children.forEach((e) => (e.active = false));
    for (let i = 0; i < configSoul.firstAttrList.length; i++) {
      let nodeAttr: Node;
      if (i >= layout.children.length) {
        nodeAttr = instantiate(layout.children[0]);
        layout.addChild(nodeAttr);
      } else {
        nodeAttr = layout.children[i];
      }
      nodeAttr.active = true;

      // 设置属性
      nodeAttr.getComponentInChildren(Label).string = Formate.formatAttribute(
        configSoul.firstAttrList[i][0],
        configSoul.firstAttrList[i][1]
      );
    }
  }

  /**购买武魂 */
  private onBuy(idx: number) {
    this.onSelectChange(idx);
    let soulIdList = SoulModule.data.getSoulIdList();
    if (SoulModule.data.warriorSoulManageMsg.slot <= soulIdList.length) {
      TipMgr.showTip(`槽位不足`);
      return;
    }

    let templateId = SoulModule.data.warriorSoulManageMsg.refreshTemplateIdList[this._currentIndex];
    let configSoul = SoulModule.data.getConfigSoul(templateId);

    // 道具数量
    let lackItem = PlayerModule.service.checkitemEnought(configSoul.priceList);
    if (lackItem.length > 0) {
      /**道具不足 */
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: lackItem[0],
        needNum: lackItem[1],
      });
      return;
    }

    SoulModule.api.buySoul(this._currentIndex, (msg: WarriorBuySoulResponse) => {
      UIMgr.instance.showDialog(SoulRouteName.UISoulGetPop, { soulId: msg.warriorSoulMessage.id });
    });
  }

  private confirmRefresh(callBack: Function) {
    // 是否存在高级兽魂
    for (let idx in SoulModule.data.warriorSoulManageMsg.refreshTemplateIdList) {
      let sellOut = SoulModule.data.warriorSoulManageMsg.chosenIndexList[idx];
      if (sellOut) {
        continue;
      }

      let templateId = SoulModule.data.warriorSoulManageMsg.refreshTemplateIdList[idx];
      let cfgSoul = SoulModule.data.getConfigSoul(templateId);
      if (cfgSoul.color >= 4) {
        let msg: ConfirmMsg = {
          msg: `存在高级兽魂，刷新后将消失！\n确认刷新？`,
          itemList: [],
          stopHintOption: false,
        };

        UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
          if (resp?.ok) {
            callBack();
          }
        });
        return;
      }
    }

    callBack();
  }

  /** 刷新武魂列表 */
  private on_click_btn_free_refresh() {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击刷新按钮);
    // 基础配置
    let cfgBaseSoul = SoulModule.data.getConfigSoul();

    if (cfgBaseSoul.freeRefreshMax <= SoulModule.data.warriorSoulManageMsg.freeCount) {
      TipMgr.showTip(`今日普通刷新已达上限`);
      return;
    }

    this.confirmRefresh(() => {
      SoulModule.api.refreshSoul(true, () => {
        this.refresh(true);
      });
    });
  }

  private on_click_btn_cost_refresh() {
    AudioMgr.instance.playEffect(SoulAudioName.Effect.点击刷新按钮);
    let cfgBaseSoul = SoulModule.data.getConfigSoul();

    if (cfgBaseSoul.costRefreshMax <= SoulModule.data.warriorSoulManageMsg.paidCount) {
      TipMgr.showTip(`今日高级刷新已达上限`);
      return;
    }

    let lackItem = PlayerModule.service.checkitemEnought(cfgBaseSoul.costRefreshCostList);
    if (lackItem.length > 0) {
      /**道具不足 */
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: lackItem[0],
        needNum: lackItem[1],
      });
      return;
    }

    this.confirmRefresh(() => {
      SoulModule.api.refreshSoul(false, () => {
        this.refresh(true);
      });
    });
  }

  private on_click_btn_item_add() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
      itemId: SoulModule.data.getConfigSoul().priceList[0],
    });
  }

  private on_click_btn_rate() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(SoulRouteName.UISoulRatePop, this._currentIndex);
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { desId: 4 });
  }
}
