import { _decorator, Component, Node } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import MsgEnum from "db://assets/GameScrpit/game/event/MsgEnum";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { Event_102 } from "../components/Event_102";
import { HuntingBase } from "../../HuntingBase";
const { ccclass, property } = _decorator;

@ccclass("UIHuntingAtk")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_EVENT_ACTION,
  url: "prefab/ui/UIHuntingAtk",
  nextHop: [],
  exit: "dialog_close",
  transparent: true,
})
export class UIHuntingAtk extends BaseCtrl {
  private _eventNode: Node;
  init(args: RouteShowArgs): void {
    //
    this._eventNode = args.payload.eventNode;
  }
  start() {
    super.start();
    MsgMgr.emit(MsgEnum.ON_THIEF_FOCOUS, this._eventNode);
    MsgMgr.emit(MsgEnum.ON_UIMAIN_HIDE_ANI);
    this._eventNode.getComponent(HuntingBase).focusHunt();
    MsgMgr.on(MsgEnum.ON_THIEF_UNFOCOUS, this.closeBack, this);
  }
  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_THIEF_UNFOCOUS, this.closeBack, this);
    MsgMgr.emit(MsgEnum.ON_UIMAIN_SHOW_ANI);
  }

  private on_click_btn_atk() {
    //
    this._eventNode.getComponent(HuntingBase).atk();
  }

  update(deltaTime: number) {}
}
