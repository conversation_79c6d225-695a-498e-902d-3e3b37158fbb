import ToolExt from "../../game/common/ToolExt";
import MsgEnum from "../../game/event/MsgEnum";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "../../game/mgr/ApiHandler";
import { GoodsSubCmd } from "../../game/net/cmd/CmdData";
import { GoodsRedeemResponse } from "../../game/net/protocol/Goods";
import { GoodsMessage } from "../../game/net/protocol/Item";
import MsgMgr from "../../lib/event/MsgMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { GoodsModule } from "./GoodsModule";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
export class GoodsSubscriber {
  private getGoodsCallback(rs: GoodsMessage) {
    log.log("新增资源 Goods", rs);
    ToolExt.setResStock(rs.resMap);
    ToolExt.setResStock(rs.itemMap);
    ToolExt.setResStock(rs.heroMap);
    ToolExt.setResStock(rs.friendMap);
    ToolExt.setResStock(rs.petMap);
  }
  private updateGoodsCallback(rs: GoodsMessage) {
    log.log("资源更新 Goods", rs);
    ToolExt.setResStock(rs.resMap);
    ToolExt.setResStock(rs.itemMap);
  }

  private orderSubscriberCallback(rs: GoodsRedeemResponse) {
    GoodsModule.api.buyInfo(() => {
      MsgMgr.emit(MsgEnum.ON_SHOP_ORDER_NOTIFY, rs);
    });
    MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: rs.rewardList });
  }

  public register() {
    //订阅服务器消息
    // 新增资源
    ApiHandler.instance.subscribe(GoodsMessage, GoodsSubCmd.getGoods, this.getGoodsCallback);
    // 资源更新
    ApiHandler.instance.subscribe(GoodsMessage, GoodsSubCmd.updateGoods, this.updateGoodsCallback);

    // 支付结果回调
    ApiHandler.instance.subscribe(GoodsRedeemResponse, GoodsSubCmd.orderSubscriber, this.orderSubscriberCallback);
  }

  public unRegister() {
    //取消订阅服务器消息
    ApiHandler.instance.unSubscribe(GoodsSubCmd.getGoods, this.getGoodsCallback);
    ApiHandler.instance.unSubscribe(GoodsSubCmd.updateGoods, this.updateGoodsCallback);
    ApiHandler.instance.unSubscribe(GoodsSubCmd.orderSubscriber, this.orderSubscriberCallback);
  }
}

// 路由: 10 - 1  --- 广播推送: com.feamon.proto.goods.GoodsMessage (新增资源)
// 路由: 10 - 2  --- 广播推送: com.feamon.proto.goods.GoodsMessage (资源更新)
