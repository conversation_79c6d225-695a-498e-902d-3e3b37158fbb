import { _decorator, CCInteger, Component, Graphics, Node, NodeEventType, UITransform, Widget } from "cc";
const { ccclass, property, executeInEditMode } = _decorator;

@ccclass("RoundGraphics")
@executeInEditMode
export class RoundGraphics extends Component {
  @property({ serializable: true })
  private _userRadius: number = 10;

  @property({ type: CCInteger })
  set radiu(value: number) {
    this._userRadius = value;
    this.draw();
  }
  get radiu(): number {
    return this._userRadius;
  }
  start() {
    this.draw();
    this.node.on(NodeEventType.TRANSFORM_CHANGED, this.draw, this);
  }
  private draw() {
    let graphics = this.getComponent(Graphics);
    if (graphics) {
      let width = graphics.getComponent(UITransform).width;
      let height = graphics.getComponent(UITransform).height;
      let anchorX = graphics.getComponent(UITransform).anchorX;
      let anchorY = graphics.getComponent(UITransform).anchorY;
      let x = -width * anchorX;
      let y = height * (anchorY - 1);
      graphics.roundRect(x, y, width, height, this._userRadius);
      graphics.fill();
      // log.log("graphics is finish ");
      // log.log(`${width}-${height}-${this._userRadius}`);
    }
  }
  onFocusInEditor(): void {
    this.draw();
  }

  update(deltaTime: number) {}
}
