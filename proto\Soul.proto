syntax = "proto3";
package sim;
import "Comm.proto";

// 
message SoulIntegrateRequest {
  // 被吞噬的兽魂ID
  int64 srcId = 1;
  // 吞噬的兽魂ID
  int64 dstId = 2;
}

// 
message SoulIntegrateResponse {
  // 吞噬的武魂已经吞噬的次数
  int32 integrateCount = 1;
  // 吞噬的武魂拥有的属性集合
  map<int64,double> attrMap = 2;
  // 奖励的道具
  sim.RewardMessage rewardMessage = 3;
}

// 
message SoulPictureActiveResponse {
  // 
  SoulPlanMessage soulPlan = 1;
  // 是否有激活新的方案
  bool unlockPlan = 2;
  // 如果激活不为空
  SoulPlanMessage newSoulPlan = 3;
}

// 
message SoulPictureMessage {
  // 当前生效的属性
  int64 attrId = 1;
  // 当前生效的属性值
  double attrAdd = 2;
  // 候补生效的属性 <= 0 时表示没有或者已经被用于替换为生效的属性
  int64 backUpAttrId = 3;
  // 候补生效的属性值
  double backUpAttrAdd = 4;
}

// 
message SoulPictureSelectRequest {
  // 第几个计划
  int32 planIndex = 1;
  // 第几个方案
  int32 pictureIndex = 2;
}

// 
message SoulPlanMessage {
  // 是否方案生效
  bool use = 1;
  // 生效的组合 key为组合在配置表的索引顺序，从0开始
  map<int32,SoulPictureMessage> pictureMap = 2;
}

// 
message WarriorBuySoulResponse {
  // 得到的新的武魂信息
  WarriorSoulMessage warriorSoulMessage = 1;
  // 本轮武魂的购买情况
  repeated bool chosenIndexList = 2;
  // 三个武魂的模板ID
  repeated int64 refreshTemplateIdList = 3;
  // 曾经获得的所有兽魂
  repeated int64 soulHoldSet = 4;
}

// 
message WarriorRefreshResponse {
  repeated int64 refreshTemplateIdList = 1;
  int32 freeCount = 2;
  int32 paidCount = 3;
  int32 exp = 4;
}

// 
message WarriorSoulManageMessage {
  int32 slot = 1;
  repeated bool chosenIndexList = 2;
  repeated int64 refreshTemplateIdList = 3;
  int32 freeCount = 4;
  int32 paidCount = 5;
  int32 exp = 6;
  // 曾经获得的所有兽魂集合数量
  repeated int64 soulHoldSet = 7;
  // 生效的图鉴方案
  repeated SoulPlanMessage planList = 8;
  map<int64,WarriorSoulMessage> soulMap = 9;
}

// 
message WarriorSoulMessage {
  int64 id = 1;
  // 武魂的模板ID
  int64 soulTemplateId = 2;
  // 阶
  int32 stage = 3;
  // 等级
  int32 grade = 4;
  // 是否上阵
  bool chosen = 5;
  // 属性集合
  map<int64,double> attrMap = 6;
  // 吞噬次数
  int32 integrateCount = 7;
  // 获取的时间
  int64 timeStamp = 8;
}

// 
message WarriorSoulWorkResponse {
  map<int64,WarriorSoulMessage> allSoulMap = 1;
}

