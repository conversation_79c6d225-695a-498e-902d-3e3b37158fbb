import { IConfigShop } from "../../../game/JsonDefine";
import { JsonMgr } from "../../../game/mgr/JsonMgr";

export enum ShopTypeEnum {
  古镜商店_3 = 3,
  古镜商店_4 = 4,
  福地商店 = 10,
  战盟商店 = 11,
  试练商店 = 13,
  每日挑战商店 = 14,
}
/**
 * 本地配置文件数据结构
 * 数据基础处理，过滤等，按id取
 */
export class CommonConfig {
  public getShopConfig(type: number) {
    let shopList = [];
    Object.values(JsonMgr.instance.jsonList.c_shop).forEach((val: IConfigShop) => {
      if (val.type == type) {
        shopList.push(val);
      }
    });
    return shopList;
  }
}
