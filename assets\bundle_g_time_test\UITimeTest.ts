import { _decorator, Component, instantiate, Node, ScrollView, EventTouch } from "cc";
import { routeConfig } from "../platform/src/core/managers/RouteTableManager";
import { BUILD } from "cc/env";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "../platform/src/core/BaseCtrl";
import { ActivityModule } from "../GameScrpit/module/activity/ActivityModule";
import { TimeTaskVO } from "../GameScrpit/module/time_task/TimeTaskConfig";
import { ActivityID } from "../GameScrpit/module/activity/ActivityConstant";
import { FmButton } from "../platform/src/core/ui/components/FmButton";
import { NodeTool } from "../GameScrpit/lib/utils/NodeTool";
import { JsonMgr } from "../GameScrpit/game/mgr/JsonMgr";
import { TimeTestAdapter } from "./TimeTastViewHolder";
import { AdapterView } from "../platform/src/core/ui/adapter_view/AdapterView";
import { TimeTaskModule } from "../GameScrpit/module/time_task/TimeTaskModule";
const { ccclass, property } = _decorator;

@ccclass("UITimeTest")
//路由配置
@routeConfig({
  //指定资源包路径
  bundle: BundleEnum.BUNDLE_G_TIME_TEST,
  //指定prefab路径
  url: "UITimeTest",
  nextHop: [], //路由跳转 如果没有需要跳转的设置为空
  exit: "dialog_close", //关闭页面的节点名称
})
/**
 * 继承BaseCtrl 类
 */
export class UITimeTest extends BaseCtrl {
  playShowAni: boolean = true; //是否播放显示动画
  private _adapter: TimeTestAdapter; //适配器
  private _curIndex = 0; //当前索引
  start() {
    //调用了BaseCtrl的start方法用于初始化界面
    super.start();

    // 检查TimeTaskModule初始化
    if (!TimeTaskModule.data || !TimeTaskModule.data.timeTaskMessage) {
      console.warn("TimeTaskModule未初始化，正在创建基本结构...");
      // 使用类型断言解决只读属性问题
      if (!TimeTaskModule.data) {
        (TimeTaskModule as any).data = {};
      }
      if (!TimeTaskModule.data.timeTaskMessage) {
        TimeTaskModule.data.timeTaskMessage = {} as any;
      }
      if (!TimeTaskModule.data.timeTaskMessage.completeMap) {
        TimeTaskModule.data.timeTaskMessage.completeMap = {};
      }
    }

    // 检查节点是否存在
    const viewholderNode = this.getNode("viewholder_tast");
    if (!viewholderNode) {
      console.error("找不到viewholder_tast节点!");
      return;
    }

    //初始化_adapter
    this._adapter = new TimeTestAdapter(this.getNode("viewholder_tast"));

    //初始化数据
    let time_TestDate = ActivityModule.data.allActivityConfig[ActivityID.TIME_TASK_1] as TimeTaskVO;
    //获取这个节点上的AdapterView 组件
    this.getNode("list_content").getComponent(AdapterView).setAdapter(this._adapter);

    console.log("time_TestDate:", time_TestDate.subList);
    console.log("time_TestDate:", time_TestDate.subList[0].completeList);
    //设置数据
    this._adapter.setData(
      time_TestDate.subList[this._curIndex].rewardList,
      time_TestDate.subList[this._curIndex].completeList,
      time_TestDate.subList[this._curIndex].id,
      time_TestDate.subList[this._curIndex].taskTypeId
    );

    //循环遍历数据数组，确保有足够的子节点
    for (let i = 0; i < time_TestDate.subList.length; i++) {
      //获取节点下的所有子节点
      let children = this.getNode("scroll_content").children[i];
      //判断子节点是否存在
      if (!children) {
        //如果子节点不存在 则创建一个子节点
        children = instantiate(this.getNode("scroll_content").children[0]);
        //添加一个子节点
        this.getNode("scroll_content").addChild(children);
        //显示子节点
        children.active = true;
      }
      //设置子节点的属性
      let task = JsonMgr.instance.jsonList.c_task[time_TestDate.subList[i].taskTypeId];
      // 设置子节点zz的文本
      children.getComponent(FmButton).string = task.title;
    }

    //遍历子节点，设置选中状态
    this.getNode("scroll_content").children.forEach((child) => {
      //获取节点上的组件 FmButton  .selected 选择状态 设置为 false
      child.getComponent(FmButton).selected = false;
    });
    //将当前索引的子节点设置为选中状态
    this.getNode("scroll_content").children[this._curIndex].getComponent(FmButton).selected = true;

    // 为节点添加滚动事件添加监听
    this.getNode("node_header").on(ScrollView.EventType.SCROLLING, this.onCloseBackScroll, this);
    //一开始将左移动图标设置为false
    this.getNode("node_left_indicator").active = false;
  }

  //滚动事件发生
  onCloseBackScroll() {
    //获取滚动组件
    let scroll = this.getNode("node_header").getComponent(ScrollView);
    //如果不是横向滚动就返回 不执行操作
    if (!scroll.horizontal) {
      return;
    }
    //展示可滚动内容节点
    let conten = scroll.content;
    //获取容器左边界
    let left = NodeTool.getBorderLeft(scroll.node);
    //获取容器右边界
    let right = NodeTool.getBorderRight(scroll.node);
    //获取节点左边界
    let nodeLeft = NodeTool.getNodeLeft(conten);
    //获取节点右边界
    let nodeRight = NodeTool.getNodeRight(conten);
    //判断节点左边界是否大于左边界
    let isContentLeft = nodeLeft >= left;
    //判断节点右边界是否小于右边界
    let isContentRight = nodeRight <= right;
    //设置右移动图标
    this.getNode("node_right_indicator").active = !isContentRight;
    //设置左移动图标
    this.getNode("node_left_indicator").active = !isContentLeft;
  }

  //点击标签页事件
  onClickTab(event: EventTouch) {
    //获取点击的索引
    let index = event.target.getSiblingIndex();

    //遍历子节点，设置选中状态
    this.getNode("scroll_content").children.forEach((child) => {
      //获取节点上的组件 FmButton  .selected 选择状态 设置为 false
      child.getComponent(FmButton).selected = false;
    });
    //将当前索引的子节点设置为选中状态
    this.getNode("scroll_content").children[index].getComponent(FmButton).selected = true;
    //获取任务数据
    let time_TestDate = ActivityModule.data.allActivityConfig[ActivityID.TIME_TASK_1] as TimeTaskVO;
    //设置数据
    this._adapter.setData(
      time_TestDate.subList[index].rewardList,
      time_TestDate.subList[index].completeList,
      time_TestDate.subList[index].id,
      time_TestDate.subList[index].taskTypeId
    );
  }

  update(deltaTime: number) {}
}
