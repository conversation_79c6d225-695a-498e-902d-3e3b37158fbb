import { _decorator, color, instantiate, isValid, Label, Node, Prefab, sp } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { IListData, ItemHaoZhao } from "./component/ItemHaoZhao";
import { RightConditionEnum, Sleep } from "../../GameDefine";
import { IBoxRewardData, ItemReward } from "./component/ItemReward";
import { CityModule } from "../../../module/city/CityModule";
import { LangMgr } from "../../mgr/LangMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { RewardMessage } from "../../net/protocol/Comm";
import { IConfigBuildBox } from "../../JsonDefine";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { ConfirmMsg } from "../UICostConfirm";
import FmUtils from "../../../lib/utils/FmUtils";
import { HdVipCardRouteItem } from "../../../module/hd_vipcard/HdVipCardRoute";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";

const { ccclass, property } = _decorator;

@ccclass("UIHaoZhaoBox")
export class UIHaoZhaoBox extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAJORCITY}?prefab/ui/UIHaoZhaoBox`;
  }

  // 列表节点
  private _nodeContentRate: Node;

  // 列表节点
  private _nodeContentGrid: Node;

  // 概率数据
  private _rateListData: IListData[] = [];

  // 奖品列表
  private _rewardListData: IBoxRewardData[] = [];

  // tab
  private _tabList: Node[];
  private _tabContentList: Node[];
  // 当前选中Tab
  private _selectTabIdx = 0;

  // 列表节点
  private _pbRate: Prefab;

  // 列表节点
  private _pbReward: Prefab;

  // 一键打开
  private _btnOneKeyOpenChecked: boolean = false;

  // 当前等级，当等级更新后，需要重新获取数据
  private _curLevel: number = 0;

  // 默认配置
  private _cfgDefault: IConfigBuildBox;

  // 成功颜色
  private _colorSuccess = color().fromHEX("#13A228");

  // 失败颜色
  private _colorFail = color().fromHEX("#d75427");

  private _spine_kai_bao_xiang: sp.Skeleton;

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_CITY_HAO_ZHAO_UPDATE, this.onCityHaoZhaoUpdate, this);
    MsgMgr.on(MsgEnum.ON_GUIDE_NEXT, this.onCityHaoZhaoUpdate, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_CITY_HAO_ZHAO_UPDATE, this.onCityHaoZhaoUpdate, this);
    MsgMgr.off(MsgEnum.ON_GUIDE_NEXT, this.onCityHaoZhaoUpdate, this);
  }

  async onEvtShow() {
    // 默认配置
    let cfgList = JsonMgr.instance.jsonList.c_buildBox;
    const keyList = Object.keys(cfgList);
    this._cfgDefault = cfgList[keyList[0]];

    // 当前宝箱等级
    this._curLevel = CityModule.data.cityAggregateMessage.boxLevel;

    // 绑定结点
    this._tabList = [this.getNode("btn_tab_box"), this.getNode("btn_tab_level_up")];
    this._tabContentList = [this.getNode("tab_content_1"), this.getNode("tab_content_2")];

    this._nodeContentRate = this.getNode("content_rate");
    this._nodeContentGrid = this.getNode("content_grid");

    // 设置标题
    FmUtils.setDialogTitleX(this.getNode("DialogPrimary"), 124, [], "号召宝箱");

    // 设置标签名称
    this.setTabLabel(this._tabList[0], LangMgr.txMsgCode(142, [], "宝 箱"));
    this.setTabLabel(this._tabList[1], LangMgr.txMsgCode(141, [], "升 级"));

    // 默认不选中
    this.getNode("btn_one_key_open").getChildByPath("bg_duigou/bg_choose_icon").active = this._btnOneKeyOpenChecked;

    this.switchTab(0);

    // 红点
    BadgeMgr.instance.setBadgeId(this._tabList[0], BadgeType.UIMajorCity.UICityDetail.btn_tab_box.id);
    BadgeMgr.instance.setBadgeId(this._tabList[1], BadgeType.UIMajorCity.UICityDetail.btn_tab_level_up.id);
    BadgeMgr.instance.setBadgeId(this.getNode("btn_open"), BadgeType.UIMajorCity.UICityDetail.btn_tab_box.btn_open.id);
    BadgeMgr.instance.setBadgeId(
      this.getNode("btn_level_up"),
      BadgeType.UIMajorCity.UICityDetail.btn_tab_level_up.btn_level_up.id
    );
  }

  onCityHaoZhaoUpdate() {
    let isLevelUp = false;
    if (this._curLevel !== CityModule.data.cityAggregateMessage.boxLevel) {
      // 第一次初始化不加特效
      if (this._curLevel > 0 && CityModule.data.cityAggregateMessage.boxLevel > this._curLevel) {
        isLevelUp = true;
      }

      this._curLevel = CityModule.data.cityAggregateMessage.boxLevel;
      this._rateListData = [];
      this._rewardListData = [];
    }

    if (this._selectTabIdx == 0) {
      this.initContent1();
    } else if (this._selectTabIdx == 1) {
      this.initContent2(isLevelUp);
    }
  }

  setTabLabel(nodeTab: Node, string: string) {
    const lbl = nodeTab.getChildByPath("node_check/Label").getComponent(Label);
    lbl.string = string;
    const lbl2 = nodeTab.getChildByPath("node_uncheck/Label").getComponent(Label);
    lbl2.string = string;
  }

  async switchTab(idx: number) {
    this._selectTabIdx = idx;
    for (let i = 0; i < this._tabList.length; i++) {
      this._tabList[i].getChildByName("node_check").active = idx == i;
      this._tabList[i].getChildByName("node_uncheck").active = idx != i;
      this._tabContentList[i].active = idx == i;
    }

    if (idx == 0) {
      this.initContent1();
    } else if (idx == 1) {
      this.initContent2(false);
    }
  }

  async initContent1() {
    // 宝箱状态
    this._spine_kai_bao_xiang = this.getNode("spine_kai_bao_xiang").getComponent(sp.Skeleton);
    SpineUtil.playSpine(this._spine_kai_bao_xiang, "baoxiang", false);

    // 设置宝箱数量
    const lbl_box_num = this.getNode("lbl_box_num").getComponent(Label);
    lbl_box_num.string = "X" + CityModule.data.cityAggregateMessage.boxTotalCount;

    // 设置等级
    // const lbl_level = this.getNode("lbl_level").getComponent(Label);
    // lbl_level.string = LangMgr.txMsgCode(133, [this._curLevel], "号召宝箱等级:%s");

    // 设置宝箱描述
    // const lbl_level_desc = this.getNode("lbl_level_desc").getComponent(Label);
    // lbl_level_desc.string = LangMgr.txMsgCode(132, [], "宝箱等级越高,越容易获得稀有道具");

    // 设置宝箱标题
    const lbl_reward_title = this.getNode("lbl_reward_title").getComponent(Label);
    lbl_reward_title.string = LangMgr.txMsgCode(99999999, [], "几率获得以下道具");

    // 按钮名称
    const lblBtnOpen = this.getNode("btn_open").getChildByName("lbl_open").getComponent(Label);
    lblBtnOpen.string = LangMgr.txMsgCode(99999999, [], "打 开");

    // 一键打开
    const lbl_one_key_open = this.getNode("lbl_one_key_open").getComponent(Label);
    lbl_one_key_open.string = LangMgr.txMsgCode(134, [], "一键打开");

    if (this._rewardListData.length > 0) {
      return;
    }

    // 初始化 _listData
    this._rewardListData = this.createRewardData(CityModule.data.cityAggregateMessage.boxLevel);

    // 加载预制体
    this._pbReward = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_MAJORCITY, "prefab/components/ItemReward");
    if (!isValid(this.node)) {
      return;
    }

    this._nodeContentGrid.destroyAllChildren();
    // 初始化列表
    for (let i = 0; i < this._rewardListData.length; i++) {
      await Sleep(0.02);
      if (!isValid(this.node)) {
        return;
      }
      const itemData = this._rewardListData[i];
      const node = instantiate(this._pbReward);
      node.setParent(this._nodeContentGrid);
      node.getComponent(ItemReward).initData(itemData);
    }
  }

  async initContent2(isLevelUp: boolean) {
    // 下一级需要几个建筑
    let buildNum = CityModule.service.getBuildNeed(this._curLevel + 1);

    // 解锁条件展示
    const lbl_unlock = this.getNode("lbl_unlock").getComponent(Label);
    lbl_unlock.string = LangMgr.txMsgCode(125, [CityModule.data.cityMessageList.length, buildNum], "解锁等级");
    lbl_unlock.color = CityModule.data.cityMessageList.length >= buildNum ? this._colorSuccess : this._colorFail;

    // 下一级号召人数
    let needPeople = 0;
    if (this._cfgDefault.callTime.length >= this._curLevel) {
      needPeople = this._cfgDefault.callTime[this._curLevel - 1];
    } else {
      needPeople = this._cfgDefault.callTime[this._cfgDefault.callTime.length - 1];
    }

    // 累计号召
    const lbl_unlocl_hao_zhao = this.getNode("lbl_unlocl_hao_zhao").getComponent(Label);
    lbl_unlocl_hao_zhao.string = LangMgr.txMsgCode(126, [CityModule.data.cityTotalNum, needPeople], "累计号召");
    lbl_unlocl_hao_zhao.color = CityModule.data.cityTotalNum >= needPeople ? this._colorSuccess : this._colorFail;

    let lbl_head_level = this.getNode("lbl_head_level").getComponent(Label);
    lbl_head_level.string = LangMgr.txMsgCode(129, [this._curLevel]);

    let lbl_head_level_next = this.getNode("lbl_head_level_next").getComponent(Label);
    lbl_head_level_next.string = LangMgr.txMsgCode(130, [this._curLevel + 1], "");

    this.getNode("lbl_hint").getComponent(Label).string = LangMgr.txMsgCode(131, []);

    if (this._rateListData.length > 0) {
      return;
    }

    // 初始化 _listData
    this._rateListData = this.createRateData(CityModule.data.cityAggregateMessage.boxLevel);

    this._pbRate = await this.assetMgr.loadPrefabSync(BundleEnum.BUNDLE_G_MAJORCITY, "prefab/components/ItemHaoZhao");
    if (!isValid(this.node)) {
      return;
    }

    let isMaxLevel = CityModule.data.cityAggregateMessage.boxLevel >= this._cfgDefault.lvMax;

    this._nodeContentRate.destroyAllChildren();
    // 初始化列表
    for (let i = 0; i < this._rateListData.length; i++) {
      await Sleep(0.02);
      if (!isValid(this.node)) {
        return;
      }

      const itemData = this._rateListData[i];
      const node = instantiate(this._pbRate);
      node.setParent(this._nodeContentRate);
      node.getComponent(ItemHaoZhao).initData(itemData, isMaxLevel, isLevelUp);
    }
  }

  createRewardData(level: number): IBoxRewardData[] {
    let cfgList = JsonMgr.instance.jsonList.c_buildBox;
    let listData = [];

    const keyList = Object.keys(cfgList);
    for (let i = 0; i < keyList.length; i++) {
      const cfg = cfgList[Number(keyList[i])];

      // 超过了，取最后一个
      let idxNow = level - 1;
      if (cfg.rateList.length <= idxNow) {
        idxNow = cfg.rateList.length - 1;
      }
      idxNow = Math.max(0, idxNow);

      let unLockLevel = 1;
      for (let j = 0; j < cfg.rateList.length; j++) {
        if (cfg.rateList[j] > 0) {
          unLockLevel = j + 1;
          break;
        }
      }

      if (level > unLockLevel) {
        unLockLevel = 0;
      }

      let item: IBoxRewardData = {
        id: cfg.id,
        itemId: cfg.rewardList[0],
        itemNum: cfg.rewardList[1],
        rate: (cfg.rateList[idxNow] || 0) / 100,
        unlockLevel: unLockLevel,
      };
      listData.push(item);
    }
    return listData;
  }

  /**
   * 根据配置与等级初始化列表数据
   * @param level 等级
   * @returns 列表数据
   */
  createRateData(level: number): IListData[] {
    let cfgList = JsonMgr.instance.jsonList.c_buildBox;
    let listData = [];

    // 准备数据
    const keyList = Object.keys(cfgList);
    for (let i = 0; i < keyList.length; i++) {
      const cfg = cfgList[Number(keyList[i])];

      // 超过了，取最后一个
      let idxNow = level - 1;
      if (cfg.rateList.length <= idxNow) {
        idxNow = cfg.rateList.length - 1;
      }
      idxNow = Math.max(0, idxNow);

      // 超过了，取最后一个
      let idxNext = level;
      if (cfg.rateList.length <= idxNext) {
        idxNext = cfg.rateList.length - 1;
      }
      idxNext = Math.max(0, idxNext);

      let item: IListData = {
        id: cfg.id,
        itemId: cfg.rewardList[0],
        itemNum: cfg.rewardList[1],
        rate: (cfg.rateList[idxNow] || 0) / 100,
        nextRate: (cfg.rateList[idxNext] || 0) / 100,
      };
      listData.push(item);
    }

    // 0的放到最后
    for (let i = 0; i < listData.length; i++) {
      if (listData[0].rate === 0) {
        const item = listData.shift();
        listData.push(item);
      } else {
        break;
      }
    }
    return listData;
  }

  on_click_btn_tab_box() {
    AudioMgr.instance.playEffect(1511);
    this.switchTab(0);
  }

  on_click_btn_tab_level_up() {
    AudioMgr.instance.playEffect(1511);
    this.switchTab(1);
  }

  on_click_btn_one_key_open() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._btnOneKeyOpenChecked = !this._btnOneKeyOpenChecked;
    if (this._btnOneKeyOpenChecked) {
      if (!PlayerModule.service.hasRight(111)) {
        this._btnOneKeyOpenChecked = false;

        // 提示不能开启
        const msg: ConfirmMsg = {
          msg: LangMgr.txMsgCode(116, [FmUtils.getRightCondition(110, RightConditionEnum.Level)], "提示"),
          okText: LangMgr.txMsgCode(140, [], "前往"),
        };

        UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
          if (resp?.ok) {
            UIMgr.instance.showDialog(HdVipCardRouteItem.UICardMain);
          }
        });
      }
    }

    this.getNode("btn_one_key_open").getChildByPath("bg_duigou/bg_choose_icon").active = this._btnOneKeyOpenChecked;
  }

  async on_click_btn_open() {
    if (CityModule.data.cityAggregateMessage.boxTotalCount <= 0) {
      TipsMgr.showTipX(143, [], "暂无宝箱");
      return;
    }

    TipsMgr.setEnableTouch(false, 3);
    // for test
    // TipsMgr.setEnableTouch(false, 1.5, false);
    // SpineUtil.playSpine(this._spine_kai_bao_xiang, "baoxiang_kai", false);
    // await Sleep(0.6);
    // MsgMgr.emit(MsgEnum.ON_GET_AWARD, {
    //   rewardList: [
    //     6, 1, 1036, 2, 1036, 2, 1036, 2, 1036, 2, 1036, 2, 1061, 1, 1002, 5, 1002, 5, 1002, 5, 1002, 5, 1002, 5, 1002,
    //     5, 1002, 5, 1002, 5, 1002, 5, 1001, 20,
    //   ],
    // });
    // return;

    if (this._btnOneKeyOpenChecked) {
      CityModule.api.getAllBoxReward(async (resp: RewardMessage) => {
        TipsMgr.setEnableTouch(false, 1.5, false);
        SpineUtil.playSpine(this._spine_kai_bao_xiang, "baoxiang_kai", false);
        await Sleep(0.6);
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, resp);
      });
    } else {
      CityModule.api.getOneBoxReward(async (resp: RewardMessage) => {
        TipsMgr.setEnableTouch(false, 1.5, false);
        SpineUtil.playSpine(this._spine_kai_bao_xiang, "baoxiang_kai", false);
        await Sleep(0.6);
        MsgMgr.emit(MsgEnum.ON_GET_AWARD, resp);
      });
    }
  }

  on_click_btn_wen_hao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { desId: 18 });
  }

  on_click_btn_level_up() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);

    if (!CityModule.service.canUpLevelBox()) {
      TipsMgr.showTipX(144, [], "未达升级要求");
      return;
    }

    CityModule.api.lvBoxLevel(() => {
      AudioMgr.instance.playEffect(1509);
    });
  }
}
