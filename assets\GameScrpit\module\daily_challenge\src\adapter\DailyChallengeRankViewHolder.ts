import { _decorator, find, instantiate, Label, Node, Sprite } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";

import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";

import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { AssetMgr } from "db://assets/platform/src/ResHelper";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import ToolExt from "db://assets/GameScrpit/game/common/ToolExt";
const { ccclass, property } = _decorator;

@ccclass("DailyChallengeRankViewHolder")
export class DailyChallengeRankViewHolder extends ViewHolder {
  // private _assetMgr: AssetMgr = null;

  bindData(data: any) {
    this.setRank(this.position + 1);
    this.setMessage(data);
  }

  private async setRank(rank: number) {
    if (rank <= 3) {
      this.getNode("spr_rank").active = true;
      this.getNode("spr_lab_rank_bg").active = false;
      let spf = await this.assetMgr.loadSpriteFrameSync(
        `${BundleEnum.BUNDLE_COMMON_UI}`,
        `atlas_imgs/YWC_icon_paiming${rank}`
      );
      if (spf) {
        this.getNode("spr_rank").getComponent(Sprite).getComponent(Sprite).spriteFrame = spf;
      }
    } else {
      this.getNode("spr_rank").active = false;
      this.getNode("spr_lab_rank_bg").active = true;
      this.getNode("lbl_rank").getComponent(Label).string = String(rank);
    }
  }

  private setMessage(data: any) {
    this.getNode("lbl_name").getComponent(Label).string = data.playerSimpleMessage.nickname;

    let db = PlayerModule.data.getConfigLeaderData(data.playerSimpleMessage.level);
    this.getNode("lbl_level").getComponent(Label).string = db.name;
    this.getNode("lbl_score").getComponent(Label).string = String(data.point);

    let info = ToolExt.newPlayerBaseMessage();
    info.avatarList = data.playerSimpleMessage.avatarList;
    info.userId = data.playerSimpleMessage.userId;
    info.nickname = data.playerSimpleMessage.nickname;
    info.sex = data.playerSimpleMessage.sex;
    FmUtils.setHeaderNode(this.getNode("btn_header"), info);
  }
}
export class DailyChallengeRankAdapter extends ListAdapter {
  private _item: Node;
  private _data: any[];
  constructor(item: Node) {
    super();
    this._item = item;
  }
  public setData(data: any[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    let viewHolder = node.getComponent(DailyChallengeRankViewHolder);
    viewHolder.bindData(this._data[position]);
  }
  getCount(): number {
    return this._data.length;
  }
}
