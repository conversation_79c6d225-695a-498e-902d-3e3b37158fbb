import { _decorator, EventTouch, Label, Layout, math, Node, sp, Sprite, UI, UITransform, ValueType, Widget } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { ItemCtrl } from "../../../common/ItemCtrl";
import { SoulModule } from "db://assets/GameScrpit/module/soul/SoulModule";
import { SoulPictureActiveResponse, SoulPictureMessage } from "../../../net/protocol/Soul";
import { ItemCost } from "../../../common/ItemCost";
import { SoulTujianAttr } from "../SoulTujianAttr";
import { UISoulDetailTujian } from "../UISoulDetailTujian";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { SoulRouteName } from "db://assets/GameScrpit/module/soul/SoulRoute";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
@ccclass("SoulTuJianViewholder")
export class SoulTuJianViewholder extends ViewHolder {
  @property(Label)
  private lblTitle: Label;
  @property(Node)
  private btnShouhunXilian: Node;
  @property(Node)
  private btnActivateDisable: Node;
  @property(Node)
  private nodeSouls: Node;
  @property(Node)
  private btnActivate: Node;
  @property(Node)
  private nodeXiLian: Node;
  @property(Node)
  private lblAttrTips: Node;
  @property(Node)
  private attrCur: Node;
  @property(Node)
  private attrXilian: Node;
  @property(Node)
  private itemCost: Node;
  @property(Node)
  private effctXilian: Node;

  private _data: any;
  private _planIndex;

  private _context: UISoulDetailTujian;

  private refreshViewholder() {
    let soulSet = SoulModule.data.warriorSoulManageMsg.soulHoldSet;

    let index = 0;
    let collectNum = 0;
    for (; index < this._data.soulId.length && index < this.nodeSouls.children.length; index++) {
      let node = this.nodeSouls.children[index];
      let owned = soulSet.find((soul) => {
        return soul == this._data.soulId[index];
      });
      if (owned) {
        node.getChildByName("lbl_unowned").active = false;
        node.getComponentsInChildren(Sprite).forEach((item) => {
          item.color = math.color("#FFFFFF");
        });
        collectNum++;
      } else {
        node.getChildByName("lbl_unowned").active = true;
        node.getComponentsInChildren(Sprite).forEach((item) => {
          item.color = math.color("#6E6E6E");
        });
      }
      node.getComponent(ItemCtrl).setItemId(this._data.soulId[index]);
    }
    for (; index < this.nodeSouls.children.length; index++) {
      this.nodeSouls.children[index].active = false;
    }
    this.attrCur.active = false;
    this.btnActivate.active = false;
    this.btnShouhunXilian.active = false;
    this.btnActivateDisable.active = false;
    this.lblAttrTips.active = false;

    if (collectNum < this._data.soulId.length) {
      this.lblTitle.color = math.color("#607e74");
      this.btnActivateDisable.active = true;
      this.lblAttrTips.active = true;
    } else {
      this.lblTitle.color = math.color("#148d64");
      let picture: SoulPictureMessage =
        SoulModule.data.warriorSoulManageMsg.planList[this._planIndex].pictureMap[this.position];
      if (picture) {
        this.attrCur.active = picture.attrId > 0;
        this.attrXilian.active = picture.backUpAttrId > 0;
        this.attrCur.getComponent(SoulTujianAttr).setAttrId(this._data.id, picture.attrId, picture.attrAdd);
        this.attrXilian
          .getComponent(SoulTujianAttr)
          .setAttrId(this._data.id, picture.backUpAttrId, picture.backUpAttrAdd);
        this.itemCost.getComponent(ItemCost).setItemId(this._data.cost[0], this._data.cost[1]);
        this.btnShouhunXilian.active = true;
      } else {
        this.btnActivate.active = true;
        this.lblAttrTips.active = true;
      }
    }
    this.lblTitle.string = `【${this._data.name}】`;

    this.node.getComponent(Layout).updateLayout();
    this.node.getComponentsInChildren(Widget).forEach((widget) => {
      widget.updateAlignment();
    });
  }
  public updateData(context: UISoulDetailTujian, data: any, position: number, planIndex: number) {
    this._data = data;
    this.position = position;
    this._context = context;
    this._planIndex = planIndex;
    this.nodeXiLian.active = false;
    this.refreshViewholder();
  }
  public init() {
    this.effctXilian.getComponent(sp.Skeleton).setCompleteListener(() => {
      this.effctXilian.active = false;
    });
    this.nodeXiLian.on(
      Node.EventType.ACTIVE_CHANGED,
      () => {
        log.log("nodeXiLian.active", this.nodeXiLian.active);
        if (!this.nodeXiLian.active) {
          this.nodeXiLian
            .getChildByPath("node_content/btn_xilian")
            .off(Node.EventType.TOUCH_END, this.onClickXiLian, this);
          this.nodeXiLian
            .getChildByPath("node_content/btn_replace")
            .off(Node.EventType.TOUCH_END, this.onClickReplace, this);
        }
      },
      this
    );
  }
  private onClickActivate() {
    SoulModule.api.activePicture(this._planIndex, this.position, (data: SoulPictureActiveResponse) => {
      log.log(data);
      this.refreshViewholder();
    });
  }
  private onClickXiLian() {
    log.log("onClickXiLian", this._planIndex, this.position);
    this.effctXilian.active = true;

    this.effctXilian.getComponent(sp.Skeleton).setAnimation(0, "animation", false);
    AudioMgr.instance.playEffect(AudioName.Effect.通用升级);
    if (this.attrXilian.getComponent(SoulTujianAttr).getColor() > 3) {
      UIMgr.instance.showDialog(SoulRouteName.UISoulTujianConfirm, {
        callback: (isOk) => {
          if (isOk) {
            SoulModule.api.refreshPictureSkill(this._planIndex, this.position, (data: SoulPictureMessage) => {
              log.log(data);
              this.refreshViewholder();
            });
          }
        },
      });
    } else {
      SoulModule.api.refreshPictureSkill(this._planIndex, this.position, (data: SoulPictureMessage) => {
        log.log(data);
        this.refreshViewholder();
      });
    }
  }
  private onClickReplace() {
    AudioMgr.instance.playEffect(1012);
    log.log("onClickReplace", this._planIndex, this.position);
    let picture: SoulPictureMessage =
      SoulModule.data.warriorSoulManageMsg.planList[this._planIndex].pictureMap[this.position];
    if (!picture) {
      log.log("当前没有可替换的技能");
      return;
    }
    if (picture.backUpAttrId <= 0) {
      log.log("当前没有可替换的技能");
      return;
    }

    SoulModule.api.useBackUpPictureSkill(this._planIndex, this.position, (data: SoulPictureMessage) => {
      log.log(data);
      this.refreshViewholder();
      this.nodeXiLian.active = false;
    });
  }
  private onClickShowXilian(e: EventTouch) {
    this.nodeXiLian.active = true;
    let node: Node = e.target;
    let position = node.getWorldPosition();
    let node_content = this.nodeXiLian.getChildByName("node_content");
    let contentSize = node_content.getComponent(UITransform).contentSize;
    let offsetY =
      contentSize.height * (1 - node_content.getComponent(UITransform).anchorY) +
      node.getComponent(UITransform).height * node.getComponent(UITransform).anchorY;
    let finalPosition = node_content.getWorldPosition();
    finalPosition.y = position.y - offsetY;
    node_content.setWorldPosition(finalPosition);

    let picture: SoulPictureMessage =
      SoulModule.data.warriorSoulManageMsg.planList[this._planIndex].pictureMap[this.position];
    this.attrXilian.getComponent(SoulTujianAttr).setAttrId(this._data.id, picture.backUpAttrId, picture.backUpAttrAdd);
    this.nodeXiLian.getChildByPath("node_content/btn_xilian").on(Node.EventType.TOUCH_END, this.onClickXiLian, this);
    this.nodeXiLian.getChildByPath("node_content/btn_replace").on(Node.EventType.TOUCH_END, this.onClickReplace, this);
  }
  private onClickShowDetail() {
    this._context.showTips(this._data);
  }
}
