import { _decorator, EventTouch, Input, instantiate, Label, Node, ProgressBar, sp } from "cc";
import { UINode } from "../../../lib/ui/UINode";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import { HorseUpResponse } from "../../net/protocol/Horse";
import { UIMgr } from "../../../lib/ui/UIMgr";
import Formate from "../../../lib/utils/Formate";
import ToolExt from "../../common/ToolExt";
import { greenColor, redColor } from "../../bundleDefine/bundle_common_define";
import { HorseModule } from "../../../module/horse/HorseModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import { HorseRouteName } from "../../../module/horse/HorseRoute";
import TipMgr from "../../../lib/tips/TipMgr";
import { PlayerRouteName, PublicRouteName } from "../../../module/player/PlayerConstant";
import { BadgeMgr, BadgeType } from "../../mgr/BadgeMgr";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { AttrEnum } from "../../GameDefine";
import { ConfirmMsg } from "../UICostConfirm";
import { v2 } from "cc";
import { tween } from "cc";
import { v3 } from "cc";
import { UIOpacity } from "cc";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { NumJump } from "./NumJump";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { HorseAudioName } from "../../../module/horse/HorseConfig";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";

const { ccclass, property } = _decorator;

const LvUpAttrPosList = [
  [v2(-90, 30), v2(120, 0), v2(-30, -60)],
  [v2(-60, -30), v2(90, 30), v2(0, -90)],
  [v2(-60, 30), v2(90, 30), v2(-90, -60)],
  [v2(-30, 30), v2(90, 0), v2(-30, -60)],
];

@ccclass("UIHorseList")
export class UIHorseList extends UINode {
  protected _openAct: boolean = true;
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_HORSE}?prefab/ui/UIHorseList`;
  }

  protected dependOn(): BundleEnum[] {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _selectId: number = -1;

  // 抗性加成
  private AntAttr = [
    AttrEnum.抗击晕_31,
    AttrEnum.抗闪避_32,
    AttrEnum.抗连击_33,
    AttrEnum.抗反击_34,
    AttrEnum.抗暴击_35,
  ];

  private baseAttr = [AttrEnum.生命_1, AttrEnum.攻击_2, AttrEnum.防御_3];

  // 选中的horseId
  private _currentHoseId: number = 0;
  private _currentHoseNode: Node = null;

  protected onEvtShow(): void {
    this._currentHoseId = HorseModule.data.horseMessage.horseId;
    this.refresh();

    BadgeMgr.instance.setBadgeId(this.getNode("btn_upgrade"), BadgeType.UITerritory.btn_horse.btn_upgrade.id);
    this.getNode("lbl_attr0").active = false;
  }

  private _role: Node;
  private async setZhujueSkin() {
    if (this._role) {
      this._role.removeFromParent();
      this._role.destroy();
    }
    this._role = await ToolExt.loadUIRole(
      this.getNode("targetNode"),
      PlayerModule.data.skin.skinId,
      this._currentHoseId,
      "renderScale15",
      this
    );
    this._role.getChildByName("mount_point").setScale(1.5, 1.5, 1.5);
  }

  private refresh() {
    /**初始化坐骑列表 */
    let nodeHorseLayout = this.getNode("horse_lay");
    nodeHorseLayout.children.forEach((child) => (child.active = false));

    let horseIdList = Object.keys(JsonMgr.instance.jsonList.c_horse).map(Number);
    for (let idx in horseIdList) {
      let key = horseIdList[idx];

      let nodeItem = nodeHorseLayout.children[idx];
      if (!nodeItem) {
        nodeItem = instantiate(nodeHorseLayout.children[0]);
        nodeItem.active = false;
        nodeHorseLayout.addChild(nodeItem);
      }

      nodeItem["horseId"] = key;
      ToolExt.setItemIcon(nodeItem.getChildByName("horseIcon"), key);
      let itemConfig = JsonMgr.instance.getConfigItem(key);
      ToolExt.setItemBg(nodeItem.getChildByName("bg"), itemConfig.color);
      this.setHorseIcon(nodeItem);

      nodeItem.off(Input.EventType.TOUCH_END, this.click_horse, this);
      nodeItem.on(Input.EventType.TOUCH_END, this.click_horse, this);
      nodeItem.active = true;
    }

    this.setHorseEffect();

    this.setProgressBar();
  }

  /**设置坐骑图标状态 */
  private setHorseIcon(node: Node) {
    let horseId = node["horseId"];
    // 未解锁
    node.getChildByName("lock").active = HorseModule.data.horseMessage.unLockHorseList.indexOf(horseId) == -1;
    // 使用中
    node.getChildByName("bg_shiyongzhong").active = horseId == HorseModule.data.horseMessage.horseId;
    // 选中
    node.getChildByName("bg_xuanzhong").active = horseId == this._currentHoseId;
  }

  /**设置坐骑选中效果 */
  private setHorseEffect() {
    let config = HorseModule.data.getConfigHorse(this._currentHoseId);

    this.getNode("horse_name_lab").getComponent(Label).string = `${config.name}`;

    if (this._selectId != this._currentHoseId) {
      this._selectId = this._currentHoseId;
      this.setZhujueSkin();
    }

    // // 坐骑形象
    // let aniHorse = this.getNode("zuoqi_ef").getComponent(sp.Skeleton);
    // if (aniHorse.animation != HorseIcon[config.id]) {
    //   aniHorse.setAnimation(0, HorseIcon[config.id], true);
    // }

    let resistAttrUse = HorseModule.data.getResistAttr(this._currentHoseId);
    if (config.mainId > 0) {
      let configAttribute = JsonMgr.instance.getConfigAttribute(config.mainId);
      this.getNode("effect_lab").getComponent(Label).string = `${configAttribute.name}+${Formate.format(
        resistAttrUse[config.mainId] * 100
      )}%`;
    } else {
      this.getNode("effect_lab").getComponent(Label).string = `无特殊属性`;
    }

    if (HorseModule.data.horseMessage.horseId == this._currentHoseId) {
      this.getNode("btn_usezuoqi").active = false;
      this.getNode("btn_jiesuozuoqi").active = false;
    } else if (HorseModule.data.horseMessage.horseId != this._currentHoseId) {
      this.getNode("btn_usezuoqi").active =
        HorseModule.data.horseMessage.unLockHorseList.indexOf(this._currentHoseId) != -1;
      this.getNode("btn_jiesuozuoqi").active =
        HorseModule.data.horseMessage.unLockHorseList.indexOf(this._currentHoseId) == -1;
      if (HorseModule.data.horseMessage.unLockHorseList.indexOf(this._currentHoseId) == -1) {
        this.getNode("jiesuo_item_cost_lab").getComponent(Label).string = `${Formate.format(
          PlayerModule.data.getItemNum(ItemEnum.仙玉_6)
        )}/${config.costList[1]}`;

        ToolExt.setItemIcon(this.getNode("jiesuo_item_icon"), config.costList[0], this);

        ToolExt.setLabColor(
          this.getNode("jiesuo_item_cost_lab").getComponent(Label),
          PlayerModule.data.getItemNum(ItemEnum.仙玉_6) < config.costList[1] ? "#e41e1e" : "#74FF77"
        );
      }
    }

    /** 设置基础属性 */
    let baseAttr = HorseModule.data.getBaseAttr();
    let nodeBaseAttr = this.getNode("base_attr_lay");
    nodeBaseAttr.children.forEach((child) => (child.active = false));
    for (let i = 0; i < this.baseAttr.length; i++) {
      let attrId = this.baseAttr[i];
      let config = JsonMgr.instance.getConfigAttribute(attrId);
      let node = nodeBaseAttr.children[i];
      node.getComponentInChildren(Label).string = `${config.name} +${Formate.format(baseAttr[attrId])}`;
      node.active = true;
      this.getNode("base_attr_lay").addChild(node);
    }

    /** 设置%属性 */
    let resistAttr = HorseModule.data.getResistAttr(this._currentHoseId);
    let nodeOtherAttrLay = this.getNode("other_attr_lay");
    nodeOtherAttrLay.children.forEach((child) => (child.active = false));
    for (let i = 0; i < this.AntAttr.length; i++) {
      let attrId = this.AntAttr[i];
      let attrName = JsonMgr.instance.getConfigAttribute(attrId).name;
      let node = nodeOtherAttrLay.children[i];
      node.getComponent(Label).string = `${attrName} +${Formate.format(resistAttr[attrId] * 100)}%`;
      node.active = true;
      this.getNode("other_attr_lay").addChild(node);
    }

    let configLv = HorseModule.data.getConfigHorseLv(HorseModule.data.horseMessage.stage);
    /** 设置在几级升阶 */
    this.getNode("lbl_level_up_remain").getComponent(Label).string = `(再升${
      configLv.lvNeed - HorseModule.data.horseMessage.grade
    }级可进阶)`;
  }

  /**设置升级情况 */
  private setProgressBar() {
    let configHorseLv = JsonMgr.instance.jsonList.c_horseLv[HorseModule.data.horseMessage.stage];
    this.getNode("horse_level_lab").getComponent(
      Label
    ).string = `${HorseModule.data.horseMessage.stage} 阶 ${HorseModule.data.horseMessage.grade} 级`;
    if (configHorseLv.lvNeed == HorseModule.data.horseMessage.grade) {
      // 进阶
      this.getNode("ProgressBar").getComponent(ProgressBar).progress = 1;
      this.getNode("ProgressBar").getChildByName("lab").getComponent(Label).string = ``;
      this.getNode("btn_upgrade").getChildByName("btn_lab").getComponent(Label).string = "进阶";

      let cost1 = configHorseLv.breakCostList.find((value) => {
        return value[0] == 1076;
      });
      this.getNode("item_cost_lab").getComponent(Label).string = `${Formate.format(
        PlayerModule.data.getItemNum(cost1[0])
      )}/${cost1[1]}`;

      ToolExt.setLabColor(
        this.getNode("item_cost_lab").getComponent(Label),
        PlayerModule.data.getItemNum(cost1[0]) >= cost1[1] ? greenColor : redColor
      );

      ToolExt.setItemIcon(this.getNode("item_icon"), 1076, this);
    } else {
      // 获取经验 or 升级
      this.getNode("ProgressBar").getComponent(ProgressBar).progress =
        HorseModule.data.horseMessage.expPoint > configHorseLv.expNeed
          ? 1
          : HorseModule.data.horseMessage.expPoint / configHorseLv.expNeed;
      this.getNode("ProgressBar")
        .getChildByName("lab")
        .getComponent(Label).string = `${HorseModule.data.horseMessage.expPoint} / ${configHorseLv.expNeed}`;
      this.getNode("btn_upgrade").getChildByName("btn_lab").getComponent(Label).string = "升级";

      let cost1 = configHorseLv.lvCostList.find((value) => {
        return value[0] == 1075;
      });
      this.getNode("item_cost_lab").getComponent(Label).string = `${Formate.format(
        PlayerModule.data.getItemNum(cost1[0])
      )}/${cost1[1]}`;
      ToolExt.setLabColor(
        this.getNode("item_cost_lab").getComponent(Label),
        PlayerModule.data.getItemNum(cost1[0]) >= cost1[1] ? greenColor : redColor
      );

      ToolExt.setItemIcon(this.getNode("item_icon"), 1075, this);
    }
  }

  /**点击查看详情 */
  private on_click_btn_xiangxixinxi() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(HorseRouteName.UIHorseAttrDetail, { horseId: this._currentHoseId });
  }

  /**切换坐骑 */
  private on_click_btn_usezuoqi() {
    AudioMgr.instance.playEffect(HorseAudioName.Effect.点击上阵按钮);
    HorseModule.api.toggleHorse(this._currentHoseId, () => {
      TipMgr.showTip("上阵成功");
      this.refresh();
    });
  }

  /**解锁坐骑 */
  private on_click_btn_jiesuozuoqi() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let configHorse = HorseModule.data.getConfigHorse(this._currentHoseId);

    let msg: ConfirmMsg = {
      msg: `确认解锁${configHorse.name}？`,
      itemList: configHorse.costList,
      stopHintOption: false,
    };

    UIMgr.instance.showDialog(PublicRouteName.UICostConfirm, msg, (resp) => {
      if (resp?.ok) {
        HorseModule.api.unLockHorse(this._currentHoseId, () => {
          this.refresh();
          if (this._currentHoseNode) {
            let skt_open = this._currentHoseNode.getChildByName("skt_open");
            skt_open.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
              if (trackEntry.animation.name == "zhi_bao_shua_xin") {
                skt_open.active = false;
                skt_open.getComponent(sp.Skeleton).setCompleteListener(null);
              }
            });
            skt_open.active = true;
            SpineUtil.playSpine(skt_open.getComponent(sp.Skeleton), "zhi_bao_shua_xin", false);
          }
          TipMgr.showTip("解锁成功");
        });
      }
    });
  }

  /**升级或进阶 */
  private on_click_btn_upgrade() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let configHorseLv = HorseModule.data.getConfigHorseLv(HorseModule.data.horseMessage.stage);
    let costList = [];
    if (HorseModule.data.horseMessage.grade < configHorseLv.lvNeed) {
      costList = configHorseLv.lvCostList;
    } else {
      costList = configHorseLv.breakCostList;
    }

    for (let i = 0; i < costList.length; i++) {
      let item = costList[i];
      if (PlayerModule.data.getItemNum(item[0]) < item[1]) {
        UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
          itemId: item[0],
          needNum: item[1],
        });
        //弹窗礼包事件
        MsgMgr.emit(MsgEnum.ON_ACTIVITY_TANCHUANG, item[0]);
        return;
      }
    }

    let expPointOld = HorseModule.data.horseMessage.expPoint;
    let max = configHorseLv.expNeed - expPointOld;
    let grade = HorseModule.data.horseMessage.grade;
    let stage = HorseModule.data.horseMessage.stage;
    HorseModule.api.upgradeOrStage((rs: HorseUpResponse) => {
      AudioMgr.instance.playEffect(HorseAudioName.Effect.升级成功);
      let attrList: number[][] = [];
      // 升阶
      if (stage != HorseModule.data.horseMessage.stage) {
        let breakAddList = HorseModule.data.getConfigHorseLv(stage).breakAddList;
        this.playUpAni(breakAddList);
      } else {
        // 加经验点数
        let addNum = rs.expPoint - expPointOld;
        // 升级
        if (grade != HorseModule.data.horseMessage.grade) {
          addNum = max;
        }

        let nodeNum = instantiate(this.getNode("NumJump"));
        nodeNum.parent = this.getNode("NumJump").parent;
        nodeNum.active = true;
        nodeNum.getComponent(NumJump).play(`+${addNum}`, Math.floor(addNum / 2));

        let expAddList = HorseModule.data.getConfigHorseLv(stage).expAddList;
        for (let i = 0; i < expAddList.length; i++) {
          let attrItem = expAddList[i];
          attrList[i] = [attrItem[0], attrItem[1] * addNum];
        }
        this.playUpAni(attrList);
      }

      this.refresh();
    });
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_back() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private click_horse(event: EventTouch) {
    AudioMgr.instance.playEffect(HorseAudioName.Effect.点击至宝图标);
    this._currentHoseId = event.target["horseId"];
    this._currentHoseNode = event.target;
    this.refresh();
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 3 });
  }

  private on_click_btn_title() {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 3 });
  }

  private getLblNode(): Node {
    let nodeLvUp = this.getNode("node_lv_up");
    for (let idx = 1; idx < nodeLvUp.children.length; idx++) {
      if (!nodeLvUp.children[idx].active) {
        return nodeLvUp.children[idx];
      }
    }

    let nodeAdd = instantiate(nodeLvUp.children[1]);
    nodeLvUp.addChild(nodeAdd);
    nodeAdd.active = false;
    return nodeAdd;
  }

  private playUpAni(attrList: number[][]) {
    let nodeLvUp = this.getNode("node_lv_up");

    for (let i = 0; i < 3; i++) {
      let nodeItem = this.getLblNode();

      nodeItem.setPosition(0, 0);
      nodeItem.getComponent(UIOpacity).opacity = 0;
      let attrInfo = JsonMgr.instance.getConfigAttribute(attrList[i][0]);
      nodeItem.getComponent(Label).string = `${attrInfo.name}：+${attrList[i][1]}`;

      nodeItem.active = true;
      // 向上飘
      tween(nodeItem)
        .delay(0.2 * i)
        .by(1, { position: v3(0, 150, 0) })
        .call(() => {
          nodeItem.active = false;
        })
        .start();
      // 渐显渐隐
      tween(nodeItem.getComponent(UIOpacity))
        .set({ opacity: 0 })
        .delay(0.2 * i)
        .to(0.4, { opacity: 255 })
        .delay(0.3)
        .to(0.3, { opacity: 0 })
        .start();
    }

    nodeLvUp.getChildByName("ani").active = true;
    nodeLvUp.getChildByName("ani").getComponent(sp.Skeleton).setAnimation(0, "action", false);
  }
}
