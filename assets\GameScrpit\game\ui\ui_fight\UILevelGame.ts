import {
  _decorator,
  Animation,
  instantiate,
  isValid,
  Label,
  Node,
  ProgressBar,
  sp,
  Sprite,
  tween,
  UIOpacity,
  UITransform,
  v3,
  Vec3,
} from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import Formate from "../../../lib/utils/Formate";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import { JsonMgr } from "../../mgr/JsonMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import ToolExt from "../../common/ToolExt";
import { RoleStatus, tweenTagEnum } from "../../GameDefine";
import { FightModule, FightRouteItem } from "../../../module/fight/src/FightModule";
import { PlayerModule } from "../../../module/player/PlayerModule";
import ResMgr from "../../../lib/common/ResMgr";
import { RegularChapterPassResponse } from "../../net/protocol/Chapter";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { RunRole } from "./role/RunRole";
import { runRoleSwk } from "./role/runRoleSwk";
import { runRoleTwo } from "./role/runRoleTwo";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import { randomRangeInt } from "cc";
import { FightMsgEnum } from "../../../module/fight/src/FightConfig";
import { AudioMgr, AudioName } from "../../../../platform/src/AudioHelper";
import { math } from "cc";
import { MonsterCtrl } from "./role/MonsterCtrl";
import { PoolMgr } from "../../../../platform/src/PoolHelper";
import { Prefab } from "cc";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import FmUtils from "../../../lib/utils/FmUtils";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

const runRoleMap: Map<string, any> = new Map([
  ["runRole", RunRole],
  ["runRoleSwk", runRoleSwk],
  ["runRoleTwo", runRoleTwo],
]);

export class RunRoleDetail {
  skinId: number;
  id: number;
  db1: any;
}

// 动画状态类型
const enum AniStatusEnum {
  Init,
  Idle,
  StartRun,
  CreateMonster,
  Running,
  StopRun,
  Waiting,
  SeeBoss,
}

@ccclass("UILevelGame")
export class UILevelGame extends UINode {
  // 闲置时间，超过3秒，给出弱引导提示
  idleTime = 0;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FIGHT}?prefab/ui/UILevelGame`;
  }

  private isUiReady = false;

  // 动画状态
  private _aniStatus: AniStatusEnum = AniStatusEnum.Init;

  private _spine_Sprint_animation: sp.Skeleton;

  private _spine_tubiaoin: sp.Skeleton;

  private set aniStatus(aniStatus: AniStatusEnum) {
    this._aniStatus = aniStatus;
    if (aniStatus == AniStatusEnum.Idle) {
    } else if (aniStatus == AniStatusEnum.StartRun) {
      SpineUtil.playOneByOne(this._spine_Sprint_animation, "animation1", "animation2");
    } else if (aniStatus == AniStatusEnum.StopRun) {
      SpineUtil.playSpine(this._spine_Sprint_animation, "animation3", false);
    }
  }

  private get aniStatus(): AniStatusEnum {
    return this._aniStatus;
  }

  // 主角慢速
  private roleSpeedIdle: number = 150;

  // 主角冲速
  private roleSpeedRun: number = 800;

  // 主角加速度
  private roleSpeedAcceleration: number = 900;

  // 主角减速度
  private roleSpeedDeceleration: number = 1200;

  // 主角速度当前值
  private roleSpeed: number = 200;

  // 视角要移动到的x坐标
  private viewRunX: number = -100;

  // 主角攻击线
  private hitPosX = 20;

  // 单张地图大小
  private bgFarWidth = 2048;
  private bgRoadWidth = 2048;
  private bgNearWidth = 2048;

  // 地图当前位置
  private bgFarPosX: number = 0;
  private bgRoadPosX: number = 0;
  private bgNearPosX: number = 0;

  // 抖动cd
  private shakeCd: number = 0;

  private _runRole: RunRole;

  // 怪物对象池
  private _monsterPool: PoolMgr<Node>;

  /**
   * 怪物列表
   */
  private _monsterList: Node[] = [];

  /**
   * 掉落道具池
   */
  private _dropItemPool: Node[] = [];

  /**当前回合返回的数据 */
  private _curBattleRes: RegularChapterPassResponse;
  protected _tickerIdList: number[] = [];

  public init(args) {
    super.init(args);

    let chapterId = FightModule.data.chapterId;
    let info = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let chapterChildId = FightModule.data.chapterChildId;
    if (chapterChildId > info.costTime) {
      UIMgr.instance.replaceDialog(FightRouteItem.UILevelBoss, {}, null, () => {
        //UIMgr.instance.closeByName(FightRouteItem.UILevelGame);
      });
    }
    // 初始化怪物对象池
    this._monsterPool = new PoolMgr<Node>(async () => {
      return new Promise((resolve) => {
        ResMgr.loadPrefab(
          `${BundleEnum.BUNDLE_G_FIGHT}?prefab/monster/Monster`,
          (pb: Prefab) => {
            if (isValid(this.node) == false) {
              return;
            }

            let nodeNew = instantiate(pb);
            nodeNew.getComponent(MonsterCtrl).playIdle();
            nodeNew.walk((child) => (child.layer = this.node.layer));
            resolve(nodeNew);
          },
          this
        );
      });
    }, 50);
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.upTopItem, this);
    MsgMgr.on(FightMsgEnum.ON_FIGHT_TREASURE_UPDATE, this.upDogBox, this);
    MsgMgr.on(MsgEnum.ON_USER_CLICK, this.onUserClick, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.upTopItem, this);
    MsgMgr.off(FightMsgEnum.ON_FIGHT_TREASURE_UPDATE, this.upDogBox, this);
  }

  protected onEvtShow(): void {
    this._spine_Sprint_animation = this.getNode("spine_Sprint_animation").getComponent(sp.Skeleton);
    this._spine_tubiaoin = this.getNode("spine_tubiaoin").getComponent(sp.Skeleton);

    let chapterId = FightModule.data.chapterId;
    let info = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let chapterChildId = FightModule.data.chapterChildId;
    if (chapterChildId > info.costTime) {
      return;
    }

    SpineUtil.playSpine(this._spine_tubiaoin, "action", false);

    this._spine_Sprint_animation.node.active = false;

    this.skt_speed_effect_ShowIs(false);
    this.loadRole();
    this.setLevelBg();
    this.setLevelName();
    this.upTopItem();
    this.upFateBattleNum();
    this.setAutoFight();
    this.initDotLay();
    this.setLevelProGress(FightModule.data.chapterChildId);
    this.upDogBox();
    this.tick(0);
    this.isUiReady = true;

    // 引导手指显示
    this.getNode("spine_finger").active = false;
    this.getNode("node_num_add").active = false;
    this.getNode("bg_hongkuang").active = false;
  }

  onUserClick() {
    this.getNode("spine_finger").active = false;
    this.idleTime = 0;
  }

  /**
   * 是否到boss关卡
   * @returns true 见到boss, false还不能见
   */
  private seeBoss(): boolean {
    let chapterId = FightModule.data.chapterId;
    let chapterChildId = FightModule.data.chapterChildId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    if (chapterChildId > levelCopy.costTime) {
      this.getNode("bg_hongkuang").active = true;
      this.getNode("bg_hongkuang").getComponent(Animation).play("bossRed");
      this.getNode("bosscoming").active = true;
      this.getNode("bosscoming").getComponent(sp.Skeleton).setAnimation(0, "action", false);
      this.getNode("bosscoming")
        .getComponent(sp.Skeleton)
        .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
          //清空监听
          if ("action" == trackEntry.animation.name) {
            log.log("准备进入boss关卡");
            MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "SEE_BOSS");
            TipsMgr.setEnableTouch(false, 3);
            UIMgr.instance.showDialog(FightRouteItem.UILevelBoss, {}, null, () => {
              UIMgr.instance.closeByName(FightRouteItem.UILevelGame);
            });
          }
        });

      this.aniStatus = AniStatusEnum.SeeBoss;
      AudioMgr.instance.playEffect(AudioName.Effect.关卡警报);
      return true;
    }
    return false;
  }

  /**
   * 向服务端进行通关校验
   * @returns true 成功，false 失败
   */
  private async onHit(autoBool: boolean): Promise<boolean> {
    let num = this.getBattleNum(
      PlayerModule.data.playerBattleAttrResponse.power,
      PlayerModule.data.playerBattleAttrResponse.speed
    );

    let fate = PlayerModule.data.getItemNum(ItemEnum.气运_1);
    if (fate < num) {
      UIMgr.instance.showDialog(PlayerRouteName.UIItemFetch, {
        itemId: ItemEnum.气运_1,
        needNum: num,
      });

      this.aniStatus = AniStatusEnum.StopRun;
      return false;
    }

    if (autoBool == false) {
      this.getNode("itemLayer_qiyun").active = false;
      this.getNode("qi_yun_xiaoshi").active = true;
      this.getNode("qi_yun_xiaoshi")
        .getComponent(sp.Skeleton)
        .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
          if ("qyxh_02" == trackEntry.animation.name) {
            this.getNode("qi_yun_xiaoshi").active = false;
            this.getNode("qi_yun_xiaoshi").getComponent(sp.Skeleton).setCompleteListener(null);
          }
        });
      this.getNode("qi_yun_xiaoshi").getComponent(sp.Skeleton).setAnimation(0, "qyxh_02", false);
    }

    return new Promise((resolve, reject) => {
      FightModule.api.passChildChapter(
        (res: RegularChapterPassResponse) => {
          this.getNode("red_skt").active = true;
          this.getNode("red_skt")
            .getComponent(sp.Skeleton)
            .setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
              if ("qyxh_01" == trackEntry.animation.name) {
                this.getNode("red_kou").active = true;
                this.getNode("red_kou").getComponent(Label).string = "-" + num;
                this.getNode("red_skt").active = false;
                this.getNode("red_skt").getComponent(sp.Skeleton).setCompleteListener(null);

                tween(this.getNode("red_kou"))
                  .to(0.3, { position: new Vec3(-45, -35, 0) }, { easing: "sineOut" })
                  .delay(0.2)
                  .call(() => {
                    this.getNode("red_kou").active = false;
                    this.getNode("red_kou").setPosition(v3(-45, 0, 0));
                  })
                  .start();
              }
            });
          this.getNode("red_skt").getComponent(sp.Skeleton).setAnimation(0, "qyxh_01", false);

          this._curBattleRes = res;
          this.upFateBattleNum();
          this.setLevelProGress(FightModule.data.chapterChildId, true);

          if (this.aniStatus == AniStatusEnum.Idle || this.aniStatus == AniStatusEnum.Waiting) {
            this._runRole.playRun();
            resolve(true);
          }
        },
        (error, msg, data): boolean => {
          resolve(false);
          return false;
        }
      );
    });
  }

  private async on_click_btn_fight() {
    TipsMgr.setEnableTouch(false, 3);
    if (this.aniStatus == AniStatusEnum.SeeBoss) {
      return;
    }

    if (this.aniStatus == AniStatusEnum.Idle) {
      if (this.seeBoss()) {
        return;
      }
      this.getNode("btn_fight").active = false;

      this.aniStatus = AniStatusEnum.Waiting;
      let successPass = await this.onHit(false);
      if (successPass) {
        this.aniStatus = AniStatusEnum.StartRun;
      } else {
        this.getNode("btn_fight").active = true;
        this.aniStatus = AniStatusEnum.Idle;
      }

      // this.aniStatus = AniStatusEnum.StartRun;
      // this._runRole.playRun();
    } else if (this.aniStatus == AniStatusEnum.Running) {
      TipsMgr.showTipX(2016, [], "正在冲锋中");
    }

    TipsMgr.setEnableTouch(true);
  }

  private on_click_btn_close() {
    // 关闭窗口 effect_guanbiyinxiao
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }

  private on_click_btn_autoFight() {
    let db = JsonMgr.instance.jsonList.c_copyMain;
    let info = db[FightModule.data.chapterId];
    let finishedLv = info.finishedLv;
    if (info.index < finishedLv) {
      TipsMgr.showTipX(231, [finishedLv], `累计通关${finishedLv}关后解锁`);
      return;
    }
    let bool = FightModule.data.autoFight;
    FightModule.data.autoFight = !bool;
    this.setAutoFight();
  }

  private setAutoFight() {
    this["gou"].active = FightModule.data.autoFight;
  }

  private on_click_btn_wenhao() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 14 });
  }

  private on_click_btn_dogNode() {
    UIMgr.instance.showDialog("UILevelBox");
  }

  /**设置背景 */
  private setLevelBg() {
    let chapterId = FightModule.data.chapterId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let background = JsonMgr.instance.jsonList.c_background[levelCopy.bgId];
    let runBg = background.runBg;

    // 远景图
    const urlFar = `${BundleEnum.BUNDLE_G_FIGHT}?images/bg/${runBg[2]}`;
    ResMgr.loadImage(urlFar, this.getNode("bg_far1").getComponent(Sprite), this);
    ResMgr.loadImage(urlFar, this.getNode("bg_far2").getComponent(Sprite), this);

    // 路面图
    const urlRoad = `${BundleEnum.BUNDLE_G_FIGHT}?images/bg/${runBg[1]}`;
    ResMgr.loadImage(urlRoad, this.getNode("bg_road1").getComponent(Sprite), this);
    ResMgr.loadImage(urlRoad, this.getNode("bg_road2").getComponent(Sprite), this);

    // 近景图
    const urlNear = `${BundleEnum.BUNDLE_G_FIGHT}?images/bg/${runBg[0]}`;
    ResMgr.loadImage(urlNear, this.getNode("bg_near1").getComponent(Sprite), this);
    ResMgr.loadImage(urlNear, this.getNode("bg_near2").getComponent(Sprite), this);
  }

  private loadRole() {
    let db1 = JsonMgr.instance.jsonList.c_leaderSkin[PlayerModule.data.skin.skinId];
    let db2 = JsonMgr.instance.jsonList.c_spineShow[db1.spineId];
    let scrpitName = db2.runScrpit;

    let scrpit = runRoleMap.get(scrpitName);
    let param: RunRoleDetail = { skinId: db1.id, id: db1.spineId, db1: db1 };

    this._runRole = new scrpit(param);
    this.getNode("levelFightLayer").addChild(this._runRole);
    this._runRole.layer = this.node.layer;

    this._runRole.loadRenderNode();
    this._runRole.insertChild(this.getNode("bg_shadow"), 0);
    this.getNode("bg_shadow").setPosition(0, 0);

    // 初始位置
    this._runRole.setPosition(-450, 20);
  }

  /**初始化点 */
  private initDotLay() {
    this.getNode("dotLay").children.forEach((val) => {
      val.getChildByName("ok").active = false;
    });
  }

  /**设置关卡进度条 */
  private setLevelProGress(chapterChildId: number, isAct: boolean = false) {
    let curIndex = chapterChildId - 1;
    let chapterId = FightModule.data.chapterId;
    let info = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let maxBar = info.costTime; //总刻度
    let bar = curIndex / maxBar;
    if (isAct == true) {
      tween(this["levelProgress"].getComponent(ProgressBar))
        .tag(tweenTagEnum.UILevelGame_Tag)
        .to(0.5, { progress: bar })
        .call(() => {
          this.getNode("dotLay").children[curIndex - 1].getChildByName("ok").active = true;
        })
        .start();
    } else {
      this["levelProgress"].getComponent(ProgressBar).progress = bar;
      for (let i = 0; i < this.getNode("dotLay").children.length && i < curIndex; i++) {
        this.getNode("dotLay").children[i].getChildByName("ok").active = true;
      }
    }
  }

  /**统一更新道具 */
  private upTopItem() {
    this["llb_item1"].getComponent(Label).string = Formate.format(PlayerModule.data.getItemNum(ItemEnum.气运_1));
    this["llb_item3"].getComponent(Label).string = Formate.format(PlayerModule.data.getItemNum(ItemEnum.阅历_3));
    this.upFateBattleNum();
  }

  /**加速效果的显示隐藏 */
  private skt_speed_effect_ShowIs(bool: boolean) {
    this.getNode("skt_speed_effect").active = bool;
  }

  /**设置关卡名字 */
  private setLevelName() {
    let chapterId = FightModule.data.chapterId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    this["levelName"].getComponent(Label).string = levelCopy.des;
  }
  /**设置狗状态 */
  private setDogState() {
    let num = Object.keys(FightModule.data.treasureMap).length;
    if (num <= 6) {
      this.getNode("dog").getComponent(sp.Skeleton).setAnimation(0, "dog1", true);
    } else if (num > 6 && num < 15) {
      this.getNode("dog").getComponent(sp.Skeleton).setAnimation(0, "dog2", true);
    } else {
      this.getNode("dog").getComponent(sp.Skeleton).setAnimation(0, "dog3", true);
    }
  }
  /**更新关卡上的宝箱显示 */
  private upDogBox() {
    let treasureMap = FightModule.data.treasureMap;
    let num = Object.keys(treasureMap).length;
    this["lbl_box_num"].getComponent(Label).string = num;
    if (num > 6) {
      this["lbl_box_num"].active = true;
    } else {
      this["lbl_box_num"].active = false;
    }
    let carKey = "car" + (num + 1);
    if (num + 1 > 7) {
      carKey = "car7";
    }
    this.getNode("car").getComponent(sp.Skeleton).setAnimation(0, carKey, true);
    this.setDogState();
  }
  /**更新闯关的气运消耗数值 */
  private upFateBattleNum() {
    let bloom = FightModule.data.chapterMsg.speedParam;
    let num = this.getBattleNum(PlayerModule.data.playerBattleAttrResponse.power, bloom);
    this["lbl_need_item1"].getComponent(Label).string = Formate.format(num);
    if (num > PlayerModule.data.getItemNum(ItemEnum.气运_1)) {
      ToolExt.setLabColor(this["lbl_need_item1"].getComponent(Label), "#E10000");
      this.getNode("btn_fight").getComponent(sp.Skeleton).setAnimation(0, "animation1", true);
    } else {
      ToolExt.setLabColor(this["lbl_need_item1"].getComponent(Label), "#FFFFFF");
      this.getNode("btn_fight").getComponent(sp.Skeleton).setAnimation(0, "animation2", true);
    }
  }

  /**计算消耗数值 */
  private getBattleNum(playerPower: number, bloom: number): number {
    let chapterId = FightModule.data.chapterId;
    let chapterChildId = FightModule.data.chapterChildId;
    let levelCopy = JsonMgr.instance.getConfigCopyMain(chapterId);

    if (levelCopy.costNum) {
      // 固定消耗
      let a = levelCopy.costNum;
      let b = levelCopy.costTimeList[chapterChildId - 1] / 10000; //配置是万分比，需要除以10000
      let c = ToolExt.levelBossPower(levelCopy.powerList);
      let d = playerPower;
      return FmUtils.passChapterCostFix(a, b, c, d);
    } else {
      // 系数消耗
      let a = levelCopy.cost;
      let b = levelCopy.costTimeList[chapterChildId - 1] / 10000; //配置是万分比，需要除以10000
      let c = ToolExt.levelBossPower(levelCopy.powerList);
      let d = playerPower;
      let e = bloom;
      return FmUtils.passChapterCost(a, b, c, d, e);
    }
  }

  // 是否还有怪物
  private hasMonster(): boolean {
    this._monsterList = this._monsterList.filter((val) => {
      return val.active == true;
    });

    return this._monsterList.length > 0;
  }

  public async tick(dt: number): Promise<void> {
    if (!this.isUiReady) return;

    // 视角移动，返回完成状态和移动距离
    const viewRs = this.updateView(dt);

    // 主角速度
    const roleRs = this.updateRole(dt);

    // 场景移动
    this.updateBg(dt);

    // 状态自动切换
    if (this.aniStatus == AniStatusEnum.Init) {
      if (viewRs.complete && roleRs.complete) {
        this.aniStatus = AniStatusEnum.Idle;
      }
    } else if (this.aniStatus == AniStatusEnum.StartRun) {
      if (viewRs.complete && roleRs.complete) {
        this.aniStatus = AniStatusEnum.Running;
        this.createMonsterTeam();
      }
    } else if (this.aniStatus == AniStatusEnum.StopRun) {
      if (viewRs.complete && roleRs.complete && !this.hasMonster()) {
        this.aniStatus = AniStatusEnum.Idle;
        if (this.seeBoss()) {
          return;
        }
      }
    } else if (this.aniStatus == AniStatusEnum.Running) {
      if (!this.hasMonster()) {
        if (this.seeBoss()) {
          return;
        }

        if (FightModule.data.autoFight) {
          this.aniStatus = AniStatusEnum.Waiting;
          let successPass = await this.onHit(true);
          if (successPass) {
            this.aniStatus = AniStatusEnum.StartRun;
          } else {
            this.aniStatus = AniStatusEnum.Idle;
          }

          // this.aniStatus = AniStatusEnum.StartRun;
          // this._runRole.playRun();
        } else {
          this.aniStatus = AniStatusEnum.StopRun;
        }
      }
    } else if (this.aniStatus == AniStatusEnum.Idle) {
      // 3秒未操作显示弱引导
      if (
        !TipsMgr.topRouteCtrl.routeHistory?.length &&
        UIMgr.instance.getLastPageInfo().name == FightRouteItem.UILevelGame
      ) {
        this.idleTime += dt;
        if (this.idleTime > 2) {
          this.idleTime = 0;
          this.getNode("spine_finger").active = true;
        }
      } else {
        this.idleTime = 0;
      }
    }

    // 主角攻击
    this.isPlayerCanAttack();
  }

  // 主角是否攻击
  private isPlayerCanAttack() {
    let needAttack = false;
    for (let i in this._monsterList) {
      let monster = this._monsterList[i];
      // 移动到此位置的怪物
      if (monster.position.x == this.viewRunX + 120) {
        let monsterCtrl = monster.getComponent(MonsterCtrl);
        if (monsterCtrl.status == RoleStatus.DIE) {
          continue;
        }

        needAttack = true;
        monsterCtrl.playDie(() => {
          // 释放标记
          monster.active = false;
          // monster.removeFromParent();
          // 死亡后返回池子
          this._monsterPool.recycle(monster);

          // 播放“+数字”
          this.playAddNum();
        });

        // 播放掉落声音
        AudioMgr.instance.playEffect(1643);

        // 创建阅历
        for (let i = 0; i < 4; i++) {
          this.createYueli(monster.worldPosition);
        }
      }
    }

    // 播放攻击
    if (needAttack) {
      AudioMgr.instance.playEffect(AudioName.Effect.普通关卡冲撞);
      this.shakeWorld();
      this._runRole.attack && this._runRole.attack();
    }
  }

  // 视角移动
  private updateView(dt: number): { complete: boolean; moveDt: number } {
    let rs = {
      complete: true,
      moveDt: 0,
    };

    if (this.aniStatus == AniStatusEnum.Init) {
      if (this._runRole.position.x < this.viewRunX) {
        let x = this._runRole.position.x + dt * this.roleSpeed;
        this._runRole.setPosition(x, this._runRole.position.y);
        rs.complete = false;
      }

      if (this._runRole.position.x > this.viewRunX) {
        this._runRole.setPosition(this.viewRunX, this._runRole.position.y);
        rs.complete = true;
        log.log("视角初始化完成");
      }
    }

    return rs;
  }

  // 场景移动
  private updateBg(dt: number) {
    if (this.aniStatus == AniStatusEnum.Init) {
      return;
    }

    // 地图远景移动
    let dtFar = (dt * this.roleSpeed) / 8;
    this.bgFarPosX -= dtFar;
    this.bgFarPosX = this.bgFarPosX % this.bgFarWidth;
    this.getNode("bg_far1").setPosition(this.bgFarPosX, this.getNode("bg_far1").getPosition().y);
    this.getNode("bg_far2").setPosition(this.bgFarPosX + this.bgFarWidth, this.getNode("bg_far2").getPosition().y);

    // 路面移动
    let dtRoad = dt * this.roleSpeed;
    this.bgRoadPosX -= dtRoad;
    this.bgRoadPosX = this.bgRoadPosX % this.bgRoadWidth;
    this.getNode("bg_road1").setPosition(this.bgRoadPosX, this.getNode("bg_road1").getPosition().y);
    this.getNode("bg_road2").setPosition(this.bgRoadPosX + this.bgRoadWidth, this.getNode("bg_road2").getPosition().y);

    // 近景移动
    let dtNear = dt * this.roleSpeed * 1.5;
    this.bgNearPosX -= dtNear;
    this.bgNearPosX = this.bgNearPosX % this.bgNearWidth;
    this.getNode("bg_near1").setPosition(this.bgNearPosX, this.getNode("bg_near1").getPosition().y);
    this.getNode("bg_near2").setPosition(this.bgNearPosX + this.bgNearWidth, this.getNode("bg_near2").getPosition().y);
  }

  // 主角速度
  private updateRole(dt: number): { complete: boolean } {
    let rs = { complete: true };
    if (this.aniStatus == AniStatusEnum.Running || this.aniStatus == AniStatusEnum.StartRun) {
      // 主角加速
      if (this.roleSpeed < this.roleSpeedRun) {
        this.roleSpeed += dt * this.roleSpeedAcceleration;
        rs.complete = false;
      }
      // 修正
      if (this.roleSpeed > this.roleSpeedRun) {
        this.roleSpeed = this.roleSpeedRun;
        rs.complete = true;
        this.skt_speed_effect_ShowIs(true);
        // log.log("主角加速完成");
      }
    } else if (this.aniStatus == AniStatusEnum.StopRun || this.aniStatus == AniStatusEnum.Idle) {
      if (!this.hasMonster()) {
        // 主角减速
        if (this.roleSpeed > this.roleSpeedIdle) {
          this.roleSpeed -= dt * this.roleSpeedDeceleration;
        }
        // 修正
        if (this.roleSpeed < this.roleSpeedIdle) {
          this.roleSpeed = this.roleSpeedIdle;
          this.aniStatus = AniStatusEnum.Idle;
          rs.complete = true;

          this._runRole.playWalk();
          this.skt_speed_effect_ShowIs(false);

          // 冲字显示时机
          if (this.getNode("btn_fight").active == false) {
            SpineUtil.playSpine(this._spine_tubiaoin, "action", false);
          }
          this.getNode("btn_fight").active = true;
          this.getNode("itemLayer_qiyun").active = true;
          // log.log("主角减速完成");
        }
      }
    }
    return rs;
  }

  // 创建怪物编队
  private async createMonsterTeam(monsterMatrixId: number = 0) {
    this.aniStatus = AniStatusEnum.CreateMonster;

    //读配置表阵型
    let configMonsterMatrix = FightModule.data.getConfigMonsterMatrix(monsterMatrixId);

    for (let idx in configMonsterMatrix.positionList) {
      await this.createMonster(
        v3(configMonsterMatrix.positionList[idx][0], configMonsterMatrix.positionList[idx][1]),
        configMonsterMatrix.skinId[idx],
        configMonsterMatrix.speed
      );
    }

    // 设置节点层级
    const listNode = [];
    this.getNode("levelFightLayer").children.forEach((node: Node) => {
      listNode.push({ node: node, y: node.position.y });
    });
    listNode.sort((a, b) => {
      return b.y - a.y;
    });
    for (let idx = 0; idx < listNode.length; idx++) {
      listNode[idx].node.setSiblingIndex(idx);
    }
    this.aniStatus = AniStatusEnum.Running;
  }

  // 创建一个怪物，并移动到指定位置
  private async createMonster(pos: Vec3, skinId: number, speed: number) {
    if (!isValid(this.getNode("levelFightLayer"))) {
      return;
    }

    pos.x = pos.x + 400;
    pos.y += 50;

    // 随机怪物形象
    if (!skinId || skinId < 1 || skinId > 7) {
      skinId = randomRangeInt(1, 7);
    }

    // 从对象池取出怪物
    let nodeMonster = await this._monsterPool.getOne();
    nodeMonster.active = true;

    // 初始化怪物
    let monsterCtrl = nodeMonster.getComponent(MonsterCtrl);
    monsterCtrl.init(skinId);
    monsterCtrl.moveSpeed = speed + this.roleSpeed;

    // 加入到父节点
    this.getNode("levelFightLayer").addChild(nodeMonster);

    // 统一列表管理
    this._monsterList.push(nodeMonster);

    //设置初始位置
    nodeMonster.setPosition(pos);

    // 移动到撞击位置
    nodeMonster.getComponent(MonsterCtrl).moveTo(v3(this.hitPosX, pos.y));
  }

  // 抖动
  private shakeWorld() {
    let ts = new Date().valueOf();
    if (this.shakeCd < ts) {
      // 0.2秒内不在抖动
      this.shakeCd = ts + 200;
      this.shake(this.getNode("bgMask_bottom").getChildByName("mapLayer"), v3(-375, 0));
      this.shake(this.getNode("levelFightLayer"), v3(0, -150));
      this.shake(this.getNode("mapLayer"), v3(-375, -50));
    }
  }

  /** 抖动具体方法 */
  private shake(node: Node, endPos: Vec3, amplitude: number = 20) {
    const tickTime = 0.05;

    // x轴震动
    let t1 = tween(node)
      .set({ position: endPos })
      .by(tickTime / 2, { position: v3(-amplitude / 2, 0) })
      .by(tickTime, { position: v3(+amplitude, 0) })
      .by(tickTime, { position: v3(-amplitude, 0) })
      .by(tickTime, { position: v3((amplitude * 3) / 4, 0) })
      .by(tickTime, { position: v3(-amplitude / 2, 0) })
      .by(tickTime, { position: v3(amplitude / 4, 0) });

    let t2 = tween(node)
      .delay(tickTime / 4)
      .by(tickTime / 2, { position: v3(0, amplitude / 2) })
      .by(tickTime, { position: v3(0, -amplitude) })
      .by(tickTime, { position: v3(0, +amplitude) })
      .by(tickTime, { position: v3(0, -amplitude) })
      .by(tickTime, { position: v3(0, (amplitude * 3) / 4) })
      .by(tickTime, { position: v3(0, -amplitude / 2) })
      .by(tickTime, { position: v3(0, amplitude / 4) })
      .set({ position: endPos });
    tween(node).parallel(t1, t2).start();
  }

  /**
   * 创建阅历掉落
   * @param startWorldPos 掉落起始位置,世界坐标
   */
  private createYueli(startWorldPos: Vec3) {
    let nodeDrop = this.getNode("node_drop");

    // 从池子获取
    let newDropItem: Node;
    for (let i in this._dropItemPool) {
      if (!this._dropItemPool[i].active) {
        newDropItem = this._dropItemPool[i];
      }
    }

    // 没取到就创建一个
    if (newDropItem == null) {
      newDropItem = instantiate(this.getNode("node_drop_item"));
      nodeDrop.addChild(newDropItem);
      this._dropItemPool.push(newDropItem);
    }
    newDropItem.active = true;

    // 设置初始位置
    newDropItem.setWorldPosition(startWorldPos);

    // 初始显示阴影
    newDropItem.getChildByName("bg_yinying").getComponent(Sprite).color = math.color("0000005E");

    // 初始设置图标大小
    newDropItem.getChildByName("bg_drop_item").setScale(1, 1, 1);

    // 随机结束位置
    let size = nodeDrop.getComponent(UITransform).contentSize;
    const pos = v3(randomRangeInt(0, size.x), randomRangeInt(0, size.y));

    // 内部弹弹动作
    tween(newDropItem.getChildByName("bg_drop_item"))
      .to(0.2, { position: v3(0, 100, 1) }, { easing: "cubicOut" })
      .to(0.8, { position: v3(0, 0, 1) }, { easing: "bounceOut" })
      .start();

    // 直线运动
    tween(newDropItem)
      .to(1, { position: pos }, { easing: "sineInOut" })
      .to(1, { worldPosition: this.getNode("bg_item3").getWorldPosition() }, { easing: "backIn" })
      .call(() => {
        // active 为false，表明使用完毕，可以重新从池子里获取
        newDropItem.active = false;
        this.getNode("spine_light").getComponent(sp.Skeleton).setAnimation(0, "animation", false);
      })
      .start();

    // 收集时候缩小动画
    tween(newDropItem.getChildByName("bg_drop_item"))
      .delay(1.7)
      .to(0.3, { scale: v3(0.7, 0.7, 1) })
      .start();

    // 阴影颜色渐变淡出
    tween(newDropItem.getChildByName("bg_yinying").getComponent(Sprite))
      .delay(1.5)
      .to(0.4, { color: math.color("00000000") })
      .start();
  }

  protected onEvtClose(): void {
    this._runRole && this._runRole.onRemove();
  }

  /**
   *
   * @param num 数字
   */
  private playAddNum() {
    let nodeNum = this.getNode("node_num_add");
    if (!this._curBattleRes) {
      return;
    }

    let num = ToolExt.listToMap(this._curBattleRes.resAddList)[ItemEnum.阅历_3];

    nodeNum.active = true;
    nodeNum.getComponentInChildren(Label).string = "+" + num;
    nodeNum.setPosition(0, -64);
    nodeNum.setScale(0.1, 0.1, 1);

    tween(nodeNum)
      .show()
      .to(0.5, { position: v3(0, -32), scale: v3(1, 1, 1) }, { easing: "elasticOut" })
      .delay(0.7)
      .hide()
      .start();

    // 清空返回数据
    this._curBattleRes = null;
  }
}
