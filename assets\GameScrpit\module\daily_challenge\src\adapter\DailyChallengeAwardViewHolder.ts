import { _decorator, Component, instantiate, Label, Layout, Node } from "cc";
import { ListAdapter, ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { RankReward } from "db://assets/GameScrpit/module/fracture/FractureConstant";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { IConfigDailyChallengeRank } from "db://assets/GameScrpit/game/JsonDefine";
const { ccclass, property } = _decorator;

@ccclass("DailyChallengeAwardViewHolder")
export class DailyChallengeAwardViewHolder extends ViewHolder {
  @property(Node)
  private lblTitle: Node;
  @property(Node)
  private itemLayout: Node;

  start() {}

  update(deltaTime: number) {}
  bindData(data: IConfigDailyChallengeRank, last: IConfigDailyChallengeRank) {
    //
    let rank = `第 ${data.taskList[0]} 名`;
    if (last && data.taskList[0] - last.taskList[0] > 1) {
      rank = `${last.taskList[0] + 1} - ${data.taskList[0]}`;
    }
    this.lblTitle.getComponent(Label).string = rank;
    let childIndex = 0;
    for (let i = 0; i < data.rewardList.length; i++) {
      //
      let item = this.itemLayout.children[childIndex];
      if (!item) {
        item = instantiate(this.itemLayout.children[0]);
        this.itemLayout.addChild(item);
      }
      item.active = true;
      FmUtils.setItemNode(item, data.rewardList[i][0], data.rewardList[i][1]);
      childIndex++;
    }
    for (let i = childIndex; i < this.itemLayout.children.length; i++) {
      this.itemLayout.children[i].active = false;
    }
    this.itemLayout.getComponent(Layout).updateLayout();
    this.node.getComponent(Layout).updateLayout();
  }
}

export class DailyChallengeAwardAdapter extends ListAdapter {
  private _item: Node;
  private _data: IConfigDailyChallengeRank[];
  constructor(item: Node) {
    super();
    this._item = item;
  }
  public setData(data: IConfigDailyChallengeRank[]) {
    this._data = data;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node.getComponent(DailyChallengeAwardViewHolder).bindData(this._data[position], this._data[position - 1]);
  }
  getCount(): number {
    return this._data.length;
  }
}
