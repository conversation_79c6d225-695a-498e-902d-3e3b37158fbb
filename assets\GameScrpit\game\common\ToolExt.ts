import {
  _decorator,
  Color,
  color,
  instantiate,
  isValid,
  Label,
  math,
  Node,
  sp,
  Sprite,
  tween,
  UITransform,
  v3,
  Vec3,
} from "cc";
import Tool from "../../lib/common/Tool";
import ResMgr from "../../lib/common/ResMgr";
import { BundleEnum } from "../bundleEnum/BundleEnum";
import { UIMgr } from "../../lib/ui/UIMgr";
import { UINode } from "../../lib/ui/UINode";
import { JsonMgr } from "../mgr/JsonMgr";
import { IAttr, SHOP_UNLOCK_TYPE, AttrEnum } from "../GameDefine";
import Formate from "../../lib/utils/Formate";
import { FriendModule } from "../../module/friend/FriendModule";
import { FriendStatisticsResponse } from "../net/protocol/Friend";
import { HeroModule } from "../../module/hero/HeroModule";
import { PlayerModule } from "../../module/player/PlayerModule";
import { HeroTypeIcon } from "../../module/hero/HeroConstant";
import { FightModule, FightRouteItem } from "../../module/fight/src/FightModule";
import { PlayerBaseMessage } from "../net/protocol/Player";
import { GORole } from "../fight/role/GORole";
import { StartUp } from "../../lib/StartUp";
import { PetModule } from "../../module/pet/PetModule";
import { IConfigAttribute, IConfigItem } from "db://assets/GameScrpit/game/JsonDefine";
import { HorseModule } from "../../module/horse/HorseModule";
import TipMgr from "../../lib/tips/TipMgr";
import { FarmModule } from "../../module/farm/FarmModule";
import { LangMgr } from "../mgr/LangMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

export enum Lab_Color_Enum {
  BRIGHT_CLOR,
}

@ccclass("ToolExt")
export default class ToolExt extends Tool {
  private static _qualityList = ["", "S0325", "S0328", "S0326", "S0324", "S0327"];
  private static _heroQualityList = ["", "S0305", "S0307", "S0313", "S0309", "S0311"];
  private static _heroQualityTopList = ["", "S0369", "S0367", "S0370", "S0366", "S0368"];

  private static labColorList1 = ["", "#92d564", "#70bff7", "#d0b0f7", "#f4b500", "#ffa9a9"];

  public static isRoleInvalid(role: GORole): boolean {
    if (role && isValid(role)) {
      return false;
    } else {
      return true;
    }
  }

  /**设置lab文本颜色
   * @param lab lab组件
   * @param colorStr 字符串颜色  类似这种 #1A7987
   */
  public static setLabColor(lab: Label, colorStr: string) {
    lab.color = color().fromHEX(colorStr);
  }

  public static setLabQualityColor(lab: Label, quality: number, type: Lab_Color_Enum) {
    if (type == Lab_Color_Enum.BRIGHT_CLOR) {
      ToolExt.setLabColor(lab, this.labColorList1[quality]);
    }
  }

  /**设置道具背景 */
  public static setItemBg(bgN: Node, quality: number, self?: UINode) {
    ResMgr.loadImage(
      `${BundleEnum.BUNDLE_COMMON_ITEM}?autoItem/${this._qualityList[quality]}`,
      bgN.getComponent(Sprite),
      self
    );
  }

  /**设置道具图标 */
  public static setItemIcon(iconN: Node, itemId: number, self?: UINode) {
    let cfg: IConfigItem = JsonMgr.instance.getConfigItem(itemId);
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_COMMON_ITEM, `autoItem/${cfg.iconId}`, iconN.getComponent(Sprite));
  }

  /**
   * 设置道具使用数量
   * @param label
   */
  public static setItemCost(label: Label, iconId: number, cost: number, style?: number): boolean {
    let itemNum = PlayerModule.data.getItemNum(iconId);
    label.string = `${Formate.format(itemNum)}/${Formate.format(cost)}`;
    let redColor: string = "#FF5244";
    let greencolor: string = "#00AF04";
    if (style == 1) {
      redColor = "ff0000";
      greencolor = "#74ff77";
    }
    if (itemNum < cost) {
      label.color = new Color(redColor);
      return false;
    } else {
      label.color = new Color(greencolor);
      return true;
    }
  }
  /**
   * 设置其他数据使用数量
   * @param label
   */
  public static setOtherCost(label: Label, residue: number, cost: number, style?: number): boolean {
    label.string = `${Formate.format(residue)}/${Formate.format(cost)}`;
    let redColor: string = "#FF5244";
    let greencolor: string = "#00AF04";
    if (style == 1) {
      //风格1
      redColor = "ff0000";
      greencolor = "#74ff77";
    }
    if (residue < cost) {
      label.color = new Color(redColor);
      return false;
    } else {
      label.color = new Color(greencolor);
      return true;
    }
  }

  /**设置英雄半身形象 */
  public static setHeroImageHalf(image: Node, id: number, self?: UINode) {
    ResMgr.loadImage(`${BundleEnum.BUNDLE_COMMON_HERO_HALF}?images/herohalf_${id}`, image.getComponent(Sprite), self);
  }

  // /**设置英雄半身形象 */
  // public static setHeroImageFull(image: Node, id: number, self?: UINode) {
  //   ResMgr.loadImage(`${BundleEnum.BUNDLE_COMMON_HERO_FULL}?images/hero_${id}`, image.getComponent(Sprite), self);
  // }

  /**设置英雄小图标 */
  public static setHeroImageIcon(image: Node, id: number, self?: UINode) {
    this.setItemIcon(image, id, self);
  }

  /**设置英雄背景 */
  public static setHeroBg(bg: Node, quality: number, self?: UINode) {
    ResMgr.loadSpriteFrame(
      `${BundleEnum.BUNDLE_COMMON_HERO_ICON}?patterns/heroIcon`,
      this._heroQualityList[quality],
      bg.getComponent(Sprite),
      self
    );
  }

  /**设置英雄背景2 */
  public static setHeroBgTop(bgTop: Node, quality: number, self?: UINode) {
    ResMgr.loadSpriteFrame(
      `${BundleEnum.BUNDLE_COMMON_HERO_ICON}?patterns/heroIcon`,
      this._heroQualityTopList[quality],
      bgTop.getComponent(Sprite),
      self
    );
  }

  /**设置英雄种族图标 */
  public static setRaceType(heroType: Node, type: number, self?: UINode) {
    ResMgr.setSpriteFrame(
      BundleEnum.BUNDLE_COMMON_UI,
      `atlas_imgs/${HeroTypeIcon[`type_${type}`]}`,
      heroType.getComponent(Sprite)
    );
  }

  /**设置仙友背景 */
  public static setFriendBg(bg: Node, quality, self?: UINode) {
    ResMgr.loadSpriteFrame(
      `${BundleEnum.BUNDLE_COMMON_FRIENDICON}?patterns/FriendQuality`,
      "XY_bg_pinzhi" + quality + "_1",
      bg.getComponent(Sprite),
      self
    );
  }

  /**设置仙友背景蒙版 */
  public static setFriendBgMask(bg: Node, quality, self?: UINode) {
    ResMgr.loadSpriteFrame(
      `${BundleEnum.BUNDLE_COMMON_FRIENDICON}?patterns/FriendQuality`,
      "XY_bg_pinzhi" + quality,
      bg.getComponent(Sprite),
      self
    );
  }

  /**设置仙友形象 */
  public static setFriendImages(image: Node, id: number, self?: UINode) {
    ResMgr.loadImage(`${BundleEnum.BUNDLE_COMMON_FRIEND}?half/friend_` + id, image.getComponent(Sprite), self);
  }

  /**关卡boss战力的计算 */
  public static levelBossPower(powerList: Array<Array<number>>) {
    // log.log("战力计算的属性===", powerList);
    let c_attribute = JsonMgr.instance.jsonList.c_attribute;

    let power: number = 0;
    for (let i = 0; i < powerList.length; i++) {
      let id = powerList[i][0];
      let num = powerList[i][1];
      let atrrInfo = c_attribute[id];
      let curPower = 0;
      if (atrrInfo.type1 == 1) {
        curPower = num * atrrInfo.powerRate2List[1];
      } else if (atrrInfo.type1 == 2) {
        curPower = num * atrrInfo.powerRate2List[1];
      }

      if (typeof curPower == "number") {
        power += curPower;
      } else {
        log.error("异常数值===id", id, "-----", num);
      }
    }
    return power;
  }

  /**获取一个区域内的随机坐标点 */
  public static getRandomPointInRect(rect) {
    // rect 对象应具有 x, y, width, height 属性，分别代表矩形左下角的坐标和宽高
    let x = rect.x + Math.random() * rect.width; // 在宽度范围内生成一个随机数
    let y = rect.y + Math.random() * rect.height; // 在高度范围内生成一个随机数
    return v3(x, y, 1); // 返回随机坐标点
  }

  /** 根据属性计算战力 */
  public static computePowerByAttr(attr: IAttr) {
    let power = 0;

    // 基础属性
    let baseAttrList = [AttrEnum.生命_1, AttrEnum.攻击_2, AttrEnum.防御_3, AttrEnum.敏捷_4];
    for (let idx in baseAttrList) {
      let key = baseAttrList[idx];
      let confAttribute: IConfigAttribute = JsonMgr.instance.jsonList.c_attribute[key];
      power += ((attr[key] || 0) * confAttribute.powerRate1List[1]) / confAttribute.powerRate1List[0];
    }

    // 特殊效果
    let advanceAttrList = [
      AttrEnum.击晕_21,
      AttrEnum.闪避_22,
      AttrEnum.连击_23,
      AttrEnum.反击_24,
      AttrEnum.暴击_25,
      AttrEnum.吸血_26,
      AttrEnum.伤害增加_27,

      AttrEnum.抗击晕_31,
      AttrEnum.抗闪避_32,
      AttrEnum.抗连击_33,
      AttrEnum.抗反击_34,
      AttrEnum.抗暴击_35,
      AttrEnum.抗吸血_36,
      AttrEnum.伤害减免_37,
    ];

    for (let idx in advanceAttrList) {
      let key = advanceAttrList[idx];
      let confAttribute: IConfigAttribute = JsonMgr.instance.jsonList.c_attribute[key];
      power += ((attr[key] || 0) * confAttribute.powerRate1List[1] * 10000) / confAttribute.powerRate1List[0];
    }
    return power;
  }

  /**服务端的奖励道具列表转换键值对
   * @param resAddList 服务端发送的奖励，[1,100,1010,100.......] 偶数位为id，奇数位为数量
   */
  public static traAwardItemMapList(resAddList: Array<number>): Array<{ id: number; num: number }> {
    if (!resAddList) {
      return [];
    }
    let itemListMap = [];
    let idIs = null;
    let index = 0;
    for (let i = 0; i < resAddList.length; i++) {
      idIs = i % 2;
      if (idIs == 0) {
        itemListMap[index] = { id: null, num: null };
        itemListMap[index].id = resAddList[i];
      } else {
        itemListMap[index].num = resAddList[i];
        index++;
      }
    }
    return itemListMap;
  }

  /**把奖励分层
   * @param itemListMap 键值对的奖励列表 [{id:1001,num:100},{id:1002,num:100}]
   * @param rowMax 每一个层的道具数量
   */
  public static minuteItemLayer(itemListMap: Array<{ id: number; num: number }>, rowMax: number = 4) {
    let layerList = [];
    let rowList = [];

    for (let i = 0; i < itemListMap.length; i++) {
      rowList.push(itemListMap[i]);
      if (rowList.length >= rowMax) {
        layerList.push(rowList);
        rowList = [];
      }
    }
    if (rowList.length > 0) {
      layerList.push(rowList);
      rowList = [];
    }
    return layerList;
  }

  /**Handler里使用，设置道具库存，并判断说，道具是否是大数资源，储存到玩家信息上 */
  public static setResStock(resStockMap: { [key: number]: number }) {
    let list = Object.keys(resStockMap);
    for (let i = 0; i < list.length; i++) {
      let id = Number(list[i]);
      let num = resStockMap[list[i]];
      let info = JsonMgr.instance.getConfigItem(id);
      switch (info.goodsType) {
        case 1:
        case 2:
          PlayerModule.data.setItemNum(id, num);
          break;
        case 3:
          HeroModule.api.getHeroById(id);
          break;
        case 5:
          /**灵兽 */
          PetModule.api.getOnePet(id);
          break;
        case 6:
          /**挚友 */
          FriendModule.api.getOneFriend(id);
          break;
        default:
          break;
      }
    }
  }

  /**在一定范围内，获取一个点
   * @param rect 矩形范围
   * @param size 一个大小，用来避免物品超出范围
   */
  public static getRandomPoint(rect: math.Rect, size: math.Size): Vec3 {
    rect.x = rect.x + size.width / 2;
    rect.width -= size.width;

    rect.y = rect.y + size.height / 2;
    rect.height -= size.height;
    let randomPoint = ToolExt.getRandomPointInRect(rect); // 获取随机点
    return randomPoint;
  }

  //计算贝塞尔曲线坐标函数
  public static twoBezier(t: number, p1: Vec3, cp: Vec3, p2: Vec3): Vec3 {
    let x = (1 - t) * (1 - t) * p1.x + 2 * t * (1 - t) * cp.x + t * t * p2.x;
    let y = (1 - t) * (1 - t) * p1.y + 2 * t * (1 - t) * cp.y + t * t * p2.y;
    return new Vec3(x, y, 0);
  }

  /**抛物线运动 */
  public static parabolaMove(node, endPos, tweenDuration = 0.65, tweenTag = 0, callBack?) {
    //node为做抛物线运动的节点
    let startPos = node.position; //起点，抛物线开始的坐标
    let middlePos = new Vec3(
      node.position.x - (node.position.x - endPos.x) / 2,
      node.position.y + (node.position.y - endPos.y),
      0
    ); //中间坐标，即抛物线最高点坐标
    //let destPos = new Vec3(node.position.x + 800, node.position.y, 0); //终点，抛物线落地点
    //middlePos = destPos;

    tween(node.position)
      .tag(tweenTag)
      .to(tweenDuration, endPos, {
        onUpdate: (target: Vec3, ratio: number) => {
          node.position = ToolExt.twoBezier(ratio, startPos, middlePos, endPos);
        },
      })
      .call(() => {
        callBack && callBack();
      })
      .start();
  }

  /**判断图片是否超出屏幕范围 */
  public static mapBeyondSize(pos: Vec3, node: Node) {
    let uiTransform = node.getComponent(UITransform);
    let screenWidth = StartUp.instance.getVisibleSize().width;
    let screenHeight = StartUp.instance.getVisibleSize().height;
    let contentX = uiTransform.contentSize.x;
    let contentY = uiTransform.contentSize.y;

    let x = pos.x;
    let y = pos.y;
    // 右
    if (x > contentX / 2 - screenWidth / 2) {
      x = contentX / 2 - screenWidth / 2;
    }
    // 左
    if (x < screenWidth / 2 - contentX / 2) {
      x = screenWidth / 2 - contentX / 2;
    }

    if (y > contentY / 2 - screenHeight / 2) {
      y = contentY / 2 - screenHeight / 2;
    }
    // 上
    if (y < screenHeight / 2 - contentY / 2) {
      y = screenHeight / 2 - contentY / 2;
    }

    return v3(x, y, 1);
  }

  /**返回仙友的解锁条件文本 */
  public static friendUnlockText(friendId: number) {
    let c_friend = JsonMgr.instance.jsonList.c_friend;
    let data = c_friend[friendId];
    let unlockType = data.unlock;
    let unlockNum = data.unlockNum;

    if (data["isTarget"] == 1) {
      return LangMgr.txMsgCode(161, []);
    }

    const msgMap = {
      1: 201,
      2: 202,
      3: 203,
      4: 204,
      5: 205,
      6: 206,
      7: 207,
      8: 208,
      9: 209,
      10: 210,
      11: 211,
      12: 213,
      13: 212,
      14: 203,
    };
    return LangMgr.txMsgCode(msgMap[unlockType], [unlockNum]);
  }

  /**返回仙友的解锁完成数值 */
  public static friendUnlockOkNum(res: FriendStatisticsResponse, friendId: number) {
    let c_friend = JsonMgr.instance.jsonList.c_friend;
    let data = c_friend[friendId];
    let unlock = data.unlock;

    if (data["isTarget"] == 1) {
      if (FriendModule.data.getFriendMessage(friendId)) {
        return 1;
      } else {
        return 0;
      }
    }

    switch (unlock) {
      case 1:
        if (res.encounterMap[friendId]) {
          return res.encounterMap[friendId];
        }
        return 0;
      case 2:
        return res.vipLevel;
      case 3:
        return res.level;
      case 4:
        return res.cityTotalLevel;
      case 5:
        return res.energySpeed;
      case 6:
        return res.marryPupilCnt;
      case 7:
        return res.petCnt;
      case 8:
        if (FriendModule.data.getFriendMessage(friendId)) {
          return 1;
        } else {
          return 0;
        }
      case 9:
        return res.adultPupilCnt;
      case 10:
        return res.competeWinCnt;
      case 11:
        if (FriendModule.data.getFriendMessage(friendId)) {
          return 1;
        } else {
          return 0;
        }
      case 12:
        if (FriendModule.data.getFriendMessage(friendId)) {
          return 1;
        } else {
          return 0;
        }
      case 13:
        return FarmModule.data.farmTrainMessage.totalColCnt;
    }
  }

  /**限购类型文字 */
  public static getMaxtypeLab(type: number) {
    switch (type) {
      case 0:
        return "不限购";
      case 1:
        return "每日限购:";
      case 2:
        return "每周限购:";
      case 3:
        return "每月限购:";
      case 4:
        return "永久限购:";
      case 5:
        return "活动限购:";
      default:
        return "";
    }
  }

  /**兑换商店的解锁限制文本 */
  public static getShopUnlockTypeLab(type, num) {
    switch (type) {
      case SHOP_UNLOCK_TYPE.仙盟等级:
        return "战盟等级达到" + num + "解锁";
      case SHOP_UNLOCK_TYPE.对应战将:
        let db = JsonMgr.instance.jsonList.c_hero[num];
        if (!db) {
          return "配置不对";
        }
        return "获得战将" + db.name + "解锁";
      case SHOP_UNLOCK_TYPE.拥有灵兽数量:
        return "累计获得" + num + "只灵兽解锁";
    }
  }

  /**二维数组属性，转换成对象 */
  public static getDetailAttr(arr: number[][]) {
    let obj = Object.create(null);
    for (let i = 0; i < arr.length; i++) {
      let id = arr[i][0];
      let num = arr[i][1];
      obj[id] = num;
    }

    return obj;
  }

  public static showLevelMain(call = () => {}) {
    let chapterId = FightModule.data.chapterId;
    if (!chapterId || chapterId == 0) {
      TipMgr.showTip("关卡功能还未开启");
      return;
    }

    let info = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let chapterChildId = FightModule.data.chapterChildId;
    if (chapterChildId > info.costTime) {
      UIMgr.instance.showDialog(FightRouteItem.UILevelBoss, {}, null, () => {
        UIMgr.instance.closeByName(FightRouteItem.UILevelGame);
      });
    } else {
      UIMgr.instance.showDialog(FightRouteItem.UILevelGame, {}, null, () => {
        UIMgr.instance.closeByName(FightRouteItem.UILevelBoss);
      });
    }
  }

  public static replaceLevelMain() {
    let chapterId = FightModule.data.chapterId;
    let info = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let chapterChildId = FightModule.data.chapterChildId;
    if (chapterChildId > info.costTime) {
      UIMgr.instance.showDialog(FightRouteItem.UILevelBoss, {}, null, () => {
        UIMgr.instance.closeByName(FightRouteItem.UIFightPage);
      });
    } else {
      UIMgr.instance.showDialog(FightRouteItem.UILevelGame, {}, null, () => {
        UIMgr.instance.closeByName(FightRouteItem.UIFightPage);
      });
    }
  }

  public static newPlayerBaseMessage(): PlayerBaseMessage {
    let rs: PlayerBaseMessage = {
      userId: null,
      /** 角色名称 */
      nickname: null,
      /** 头像 */
      avatarList: [-1, -1, -1, -1, -1],
      /** 性别 */
      sex: null,
      level: 0,
      vipLevel: 0,
      hiddenVip: false,
    };
    return rs;
  }

  public static tryFunc(func: Function) {
    return (event) => {
      try {
        func(event);
      } catch (error) {
        log.error(error);
      }
    };
  }

  /**同一个父节点不能调用多个不同的 */
  public static async loadUIRole(
    node: Node,
    roleSkin: number,
    horseId: number,
    renderScaleKey: string,
    self: UINode
  ): Promise<Node> {
    // 清空
    let spineId = 0;
    let scaleV3 = null;
    // 数据准备
    let path = "resources?prefab/role_point";
    let dbLeaderSkin = JsonMgr.instance.jsonList.c_leaderSkin[roleSkin];
    if (dbLeaderSkin) {
      spineId = dbLeaderSkin.spineId;
      scaleV3 = renderScaleKey != "" ? dbLeaderSkin[renderScaleKey] : [1, 1, 1];
    } else {
      let dbMonster = JsonMgr.instance.jsonList.c_monsterShow[roleSkin];
      spineId = Number(dbMonster.spineId);
      scaleV3 = renderScaleKey != "" ? dbMonster[renderScaleKey] : [1, 1, 1];
    }

    if (!spineId) {
      spineId = 10001;
    }

    let dbSpineShow = JsonMgr.instance.jsonList.c_spineShow[spineId];

    // 根节点
    let prefabRole = await ResMgr.loadPrefabSync(path, self);
    let nodeRole = instantiate(prefabRole);

    // 形象
    let prefab2 = await ResMgr.loadPrefabSync(dbSpineShow.prefabPath, self);
    if (isValid(nodeRole) == false) {
      return;
    }
    let node2 = instantiate(prefab2);
    node2.name = "renderPoint";

    // 坐骑
    if (HorseModule.data.getConfigHorse(horseId)) {
      let prefab3 = await ResMgr.loadPrefabSync(`${BundleEnum.RESOURCES}?prefab/mount/mount_${horseId}`, self);
      if (isValid(nodeRole) == false) {
        return;
      }
      let node3: Node = instantiate(prefab3);
      nodeRole.getChildByName("mount_point").destroyAllChildren();
      nodeRole.getChildByName("mount_point").addChild(node3);
      nodeRole.setPosition(0, 20, 0);
    }

    if (isValid(node) == false) {
      return;
    }
    node.destroyAllChildren();
    node.addChild(nodeRole);
    nodeRole.addChild(node2);
    node2.setScale(v3(scaleV3[0], scaleV3[1], scaleV3[2]));
    node2.getChildByName("render").getComponent(sp.Skeleton).setAnimation(0, "boss_idle", true);
    node.walk((child) => (child.layer = node.layer));
    return nodeRole;
  }

  public static listToMap(attrList: number[]) {
    let rs = Object.create(null);
    for (let i = 0; i < attrList.length; i += 2) {
      rs[attrList[i]] = attrList[i + 1];
    }
    return rs;
  }
}
