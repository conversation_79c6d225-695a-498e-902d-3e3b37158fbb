export class Rect {
  private _left: number;
  private _top: number;
  private _right: number;
  private _bottom: number;

  constructor(left: number, top: number, right: number, bottom: number) {
    this._left = left;
    this._top = top;
    this._right = right;
    this._bottom = bottom;
  }
  public set(left: number, top: number, right: number, bottom: number) {
    this._left = left;
    this._top = top;
    this._right = right;
    this._bottom = bottom;
  }
  get isZero() {
    return this._left == 0 && this._top == 0 && this._right == 0 && this._bottom == 0;
  }
  get left(): number {
    return this._left;
  }
  set left(value: number) {
    this._left = value;
  }
  get right(): number {
    return this._right;
  }
  set right(value: number) {
    this._right = value;
  }
  get top(): number {
    return this._top;
  }
  set top(value: number) {
    this._top = value;
  }
  get bottom(): number {
    return this._bottom;
  }
  set bottom(value: number) {
    this._bottom = value;
  }
}
