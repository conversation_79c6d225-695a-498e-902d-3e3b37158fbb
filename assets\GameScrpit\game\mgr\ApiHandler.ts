import { ByteValueList, ExternalMessage } from "../net/protocol/ExternalMessage";
import SendMgr from "./SendMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import { BinaryWriter } from "@bufbuild/protobuf/wire";
import { SyncQueue } from "../../../platform/src/lib/utils/Deque";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import SocketClient from "../../lib/socket/SocketClient";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
export type ApiHandlerSuccess = (data: any) => void;
export type ApiHandlerFail = (errorCode: Net_Code, msg: string[], data: any) => boolean;

interface IHandler {
  dataType: any;
  apiCmd: number;
  success?: ApiHandlerSuccess;
  fail?: ApiHandlerFail;
  isList: boolean;

  requestLock: SyncQueue<Number>;
}
export enum Net_Code {
  //频繁请求
  FREQUENT_REQUEST = 10001,

  //超时
  REQUEST_TIME_OUT = 10002,
}
export class ApiHandler {
  private static _instance: ApiHandler;
  private _handleMap: Map<number, IHandler>;
  private _subscribeMap: Map<number, IHandler[]>;
  private _syncHandleMap: Map<number, IHandler>;
  private constructor() {
    this._handleMap = new Map<number, IHandler>();
    this._subscribeMap = new Map<number, IHandler[]>();
    this._syncHandleMap = new Map<number, IHandler>();
  }
  public static get instance(): ApiHandler {
    if (!this._instance) {
      this._instance = new ApiHandler();
      SocketClient.ins.apiHandlerInstance = ApiHandler._instance;
    }
    return this._instance;
  }

  private realRequest(mergeCmd: number, data: BinaryWriter, hander: IHandler): number {
    let msgId = SendMgr.send(mergeCmd, data);
    this._handleMap.set(msgId, hander);
    // hander.requestLock.enqueue(msgId);//

    return msgId;
  }

  /**
   *
   * @param data 消息内容
   * @returns true则表示消息已经被处理，返回false则表示消息未处理
   */
  public handleMessage(data: ExternalMessage): boolean {
    let handleResult = false;
    if (this._handleMap.has(data.msgId)) {
      let handler = this._handleMap.get(data.msgId);
      // await hander.requestLock.dequeue();
      // log.info("同步请求响应", (data.cmdMerge >> 16) & 0xffff, data.cmdMerge & 0xffff, data.msgId, data);
      try {
        if (data.responseStatus === 0) {
          if (handler.isList) {
            let array = ByteValueList.decode(data.data);
            let res: any[] = [];
            array.values.forEach((element) => {
              let rs = handler.dataType.decode(element);
              res.push(rs);
            });
            handler.success?.call(this, res);
          } else {
            let responseData = handler.dataType.decode(data.data);
            // log.log(responseData);

            handler.success?.call(this, responseData);
            MsgMgr.emit(`${handler.apiCmd}`, responseData);
          }
          handleResult = true;
        } else {
          handleResult = handler.fail?.call(this, data.responseStatus, (data.validMsg || "").split["#"], data) ?? false;
        }
      } catch (error) {
        log.error("error", error);
        handleResult = handler.fail?.call(this, data.responseStatus, (data.validMsg || "").split["#"], data) ?? false;
      } finally {
        this._handleMap.delete(data.msgId);
        this._syncHandleMap.delete(data.cmdMerge);
      }
      return handleResult;
    } else if (this._subscribeMap.has(data.cmdMerge)) {
      let handlers = this._subscribeMap.get(data.cmdMerge);
      log.info("收到订阅", (data.cmdMerge >> 16) & 0xffff, data.cmdMerge & 0xffff);
      if (data.responseStatus === 0) {
        handlers.forEach((handler) => {
          if (handler.isList) {
            let array = ByteValueList.decode(data.data);
            let res: any[] = [];
            array.values.forEach((element) => {
              let rs = handler.dataType.decode(element);
              res.push(rs);
            });
            handler.success?.call(this, res);
          } else {
            try {
              let responseData = handler.dataType.decode(data.data);
              handler.success?.call(this, responseData);
            } catch (error) {
              log.log("route :", `${(data.cmdMerge >> 16) & 0xffff}-${data.cmdMerge & 0xffff}`);
              log.error("error", error);
            }

            // MsgMgr.emit(`${handler.apiCmd}`, responseData);
          }
        });
        return true;
      } else {
        // let handled = 0;
        // handlers.forEach((handler) => {
        //   let ret: boolean = handler.fail?.call(this, data.responseStatus, data.validMsg, data);
        //   if (ret) {
        //     handled++;
        //   }
        // });
        // if (handled == 0) {
        //   return false;
        // }
      }
      //TODO 测试完成后取消注释
      // return true;
    }
    return false;
  }

  /**
   * 根据mergeCmd订阅消息 对应取消订阅的接口为 unSubscribe()
   * @param type
   * @param mergeCmd
   * @param success
   * @param error
   */
  public subscribe(type: any, mergeCmd: number, success: ApiHandlerSuccess, error?: ApiHandlerFail) {
    let handler: IHandler = {
      dataType: type,
      apiCmd: mergeCmd,
      success: success,
      fail: error,
      isList: false,
      requestLock: new SyncQueue<Number>(1),
    };
    if (this._subscribeMap.has(mergeCmd)) {
      this._subscribeMap.get(mergeCmd).push(handler);
    } else {
      this._subscribeMap.set(mergeCmd, [handler]);
    }
  }
  /**
   *
   * 取消订阅
   * @param mergeCmd 订阅的mergeCmd
   */
  public unSubscribe(mergeCmd: number, success: ApiHandlerSuccess) {
    if (this._subscribeMap.has(mergeCmd)) {
      this._subscribeMap.get(mergeCmd).forEach((handler, index) => {
        if (handler.success === success) {
          this._subscribeMap.get(mergeCmd).splice(index, 1);
          log.log("取消订阅" + mergeCmd);
        }
      });
    }
  }

  /**
   * 移除所有订阅
   */
  public removeAllSubscribe() {
    this._subscribeMap.clear();
  }

  /**
   * 同步请求接口
   * @param type
   * @param mergeCmd
   * @param data
   * @param success
   * @param error
   * @returns
   */
  public requestSync(
    type: any,
    mergeCmd: number,
    data: BinaryWriter,
    success?: ApiHandlerSuccess,
    error?: ApiHandlerFail
  ) {
    if (this._syncHandleMap.get(mergeCmd)) {
      log.warn("频繁请求", (mergeCmd >> 16) & 0xffff, mergeCmd & 0xffff);
      error?.(Net_Code.FREQUENT_REQUEST, ["频繁请求"], null);
      return;
    }
    log.info("同步请求", (mergeCmd >> 16) & 0xffff, mergeCmd & 0xffff);
    let hander: IHandler = {
      dataType: type,
      apiCmd: mergeCmd,
      success: success,
      fail: error,
      isList: false,
      requestLock: new SyncQueue<Number>(1),
    };
    this._syncHandleMap.set(mergeCmd, hander);
    let msgId = this.realRequest(mergeCmd, data, hander);
    // 定时器超时清除_syncHandleMap中的数据
    setTimeout(() => {
      if (this._handleMap.has(msgId)) {
        let handler = this._handleMap.get(msgId);
        this._handleMap.delete(msgId);
        this._syncHandleMap.delete(mergeCmd);
        handler?.fail?.call(this, Net_Code.REQUEST_TIME_OUT, "请求超时", null);
        log.warn("请求超时", (mergeCmd >> 16) & 0xffff, mergeCmd & 0xffff);
      }
    }, 7000);
  }

  /**
   * 请求接口
   * @param type
   * @param mergeCmd
   * @param data
   * @param success
   * @param error
   */
  public request(type: any, mergeCmd: number, data: BinaryWriter, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    // // log.log(type.name);
    // let hander: IHandler = {
    //   dataType: type,
    //   apiCmd: mergeCmd,
    //   success: success,
    //   fail: error,
    //   isList: false,
    //   requestLock: new SyncQueue<Number>(1),
    // };
    // this.realRequest(mergeCmd, data, hander);

    this.requestSync(type, mergeCmd, data, success, error);
  }
  /**
   * 请求接口，返回结果为列表
   * @param type
   * @param mergeCmd
   * @param data
   * @param success
   * @param error
   */
  public list(type: any, mergeCmd: number, data: BinaryWriter, success?: ApiHandlerSuccess, error?: ApiHandlerFail) {
    // let dataType = new type();
    // log.log(type["create"]);
    let hander: IHandler = {
      dataType: type,
      apiCmd: mergeCmd,
      success: success,
      fail: error,
      isList: true,
      requestLock: new SyncQueue<Number>(1),
    };
    this.realRequest(mergeCmd, data, hander);
  }
}
