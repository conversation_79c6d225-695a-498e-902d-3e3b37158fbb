import { _decorator, Component, EventTouch, instantiate, Node, ScrollView, UITransform } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { ActivityModule } from "../../../activity/ActivityModule";
import { ActivityID } from "../../../activity/ActivityConstant";
import { TimeTaskVO } from "../../TimeTaskConfig";
import { TimeTaskAdapter } from "../../adapter/TimeTaskViewHolder";
import { FmButton } from "db://assets/platform/src/core/ui/components/FmButton";
import { JsonMgr } from "db://assets/GameScrpit/game/mgr/JsonMgr";
import { NodeTool } from "db://assets/GameScrpit/lib/utils/NodeTool";
import { TimeTaskModule } from "../../TimeTaskModule";

const { ccclass, property } = _decorator;

@ccclass("UITimeTask")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_TIME_TASK,
  url: "prefab/ui/UITimeTask",
  nextHop: [],
  exit: "dialog_close",
})
export class UITimeTask extends BaseCtrl {
  public playShowAni: boolean = true;
  private _adapter: TimeTaskAdapter;
  private _curIndex = 0;
  start() {
    super.start();
    this._adapter = new TimeTaskAdapter(this.getNode("viewholder_task"));
    let time_activity = ActivityModule.data.allActivityConfig[ActivityID.TIME_TASK_1] as TimeTaskVO;
    this.getNode("list_content").getComponent(AdapterView).setAdapter(this._adapter);
    this._adapter.setData(
      time_activity.subList[this._curIndex].rewardList,
      time_activity.subList[this._curIndex].completeList,
      time_activity.subList[this._curIndex].id,
      time_activity.subList[this._curIndex].taskTypeId
    );
    for (let i = 0; i < time_activity.subList.length; i++) {
      let child = this.getNode("scroll_content").children[i];
      if (!child) {
        child = instantiate(this.getNode("scroll_content").children[0]);
        this.getNode("scroll_content").addChild(child);
        child.active = true;
      }
      let task = JsonMgr.instance.jsonList.c_task[time_activity.subList[i].taskTypeId];
      child.getComponent(FmButton).string = task.title;
    }
    this.getNode("scroll_content").children.forEach((child) => {
      child.getComponent(FmButton).selected = false;
    });
    this.getNode("scroll_content").children[this._curIndex].getComponent(FmButton).selected = true;
    this.getNode("node_header").on(ScrollView.EventType.SCROLLING, this.onScroll, this);

    this.getNode("node_left_indicator").active = false;
  }

  onScroll() {
    let scrollview = this.getNode("node_header").getComponent(ScrollView);
    if (!scrollview.horizontal) return; // 如果不是横向滚动则返回

    const content = scrollview.content;
    let left = NodeTool.getBorderLeft(scrollview.node);
    let right = NodeTool.getBorderRight(scrollview.node);
    let contentLeft = NodeTool.getNodeLeft(content);
    let contentRight = NodeTool.getNodeRight(content);
    let isContentLeft = contentLeft >= left;
    let isContentRight = contentRight <= right;
    this.getNode("node_right_indicator").active = !isContentRight;
    this.getNode("node_left_indicator").active = !isContentLeft;
  }

  onClickTab(event: EventTouch) {
    //
    let index = event.target.getSiblingIndex();

    this.getNode("scroll_content").children.forEach((child) => {
      child.getComponent(FmButton).selected = false;
    });
    this.getNode("scroll_content").children[index].getComponent(FmButton).selected = true;
    let time_activity = ActivityModule.data.allActivityConfig[ActivityID.TIME_TASK_1] as TimeTaskVO;
    this._adapter.setData(
      time_activity.subList[index].rewardList,
      time_activity.subList[index].completeList,
      time_activity.subList[index].id,
      time_activity.subList[index].taskTypeId
    );
  }

  update(deltaTime: number) {}
}
