import { _decorator, instantiate, isValid, sp, Node, Prefab, UIOpacity, Animation, Sprite, Label } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import { BundleEnum } from "../game/bundleEnum/BundleEnum";
import { CityModule } from "../module/city/CityModule";
import { TipsMgr } from "../../platform/src/TipsHelper";
import { SpineUtil } from "../../platform/src/lib/utils/SpineUtil";
import { CityCtrl } from "../game/ui/ui_gameMap/CityCtrl";
import { NodeTool } from "../lib/utils/NodeTool";
import { Sleep } from "../game/GameDefine";
import { TaskSync } from "../lib/utils/TaskSync";
import { HeroTypeIcon } from "../module/hero/HeroConstant";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
import GuideMgr from "./GuideMgr";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

@ccclass("TopCityBuild")
export class TopCityBuild extends BaseCtrl {
  private _cityId: number;

  private _spineBg: sp.Skeleton;

  private _spineLight: sp.Skeleton;

  private _nodeBtnGo: Node;

  private _nodeBtnClose: Node;

  private _pbCity: Prefab = null;

  private _nodeCity: Node = null;

  private _startAni: Animation = null;

  init(args: any): void {
    this._cityId = args.cityId;
  }

  protected onLoad(): void {
    this._spineBg = this.getNode("spine_jiesuojianzhu").getComponent(sp.Skeleton);
    this._spineLight = this.getNode("spine_faguang").getComponent(sp.Skeleton);
    this._nodeBtnGo = this.getNode("btn_go");
    this._nodeBtnClose = this.getNode("btn_close");
    this._nodeCity = this.getNode("node_city");
    this._startAni = this.getNode("ani_root").getComponent(Animation);
  }

  protected async start() {
    super.start();

    // 初始化状态
    this._spineLight.node.active = false;
    this._nodeBtnGo.active = false;
    this._nodeBtnClose.active = false;

    this._nodeBtnGo.getComponent(UIOpacity).opacity = 0;
    this._nodeBtnClose.getComponent(UIOpacity).opacity = 0;

    // 展示动画播放完才可以点击
    const spineTime = SpineUtil.getSpineDuration(this._spineBg, "animation");
    TipsMgr.setEnableTouch(false, spineTime, false);

    SpineUtil.playOneByOne(this._spineBg, "animation", "animation2");

    const cfgBuild = CityModule.data.getConfigBuild(this._cityId);

    // 设置种族图标
    this.assetMgr.loadSpriteFrame(
      BundleEnum.BUNDLE_COMMON_UI,
      `atlas_imgs/${HeroTypeIcon[`type_${cfgBuild.type}`]}`,
      (spriteFrame) => {
        if (isValid(this.node) == false) {
          return;
        }
        this.getNode("bg_race").getComponent(Sprite).spriteFrame = spriteFrame;
      }
    );

    // 设置名字
    this.getNode("lbl_name").getComponent(Label).string = cfgBuild.name;

    // 多线任务准备
    const taskSync = new TaskSync(this.showCity, this);
    taskSync.addTask(Sleep, spineTime - 0.5, false);
    taskSync.addTask(this.loadPrefab);
    taskSync.start();
  }

  private async loadPrefab() {
    const pbCity = await this.assetMgr.loadPrefabSync(
      BundleEnum.BUNDLE_G_GAME_MAP,
      `prefab/building/city_${this._cityId}`
    );
    this._pbCity = pbCity;
    return pbCity;
  }

  private showCity() {
    if (!this._pbCity) {
      log.error("预制体不存在", `prefab/building/city_${this._cityId}`);
      this.closeBack();
      return;
    }

    this._spineLight.node.active = true;
    this._nodeBtnGo.active = true;
    this._nodeBtnClose.active = true;

    if (isValid(this.node) == false) {
      log.error("预制体已关闭", `prefab/building/city_${this._cityId}`);
      this.closeBack();
      return;
    }

    let nodeCity = instantiate(this._pbCity);

    nodeCity.walk((child) => (child.layer = this.node.layer));
    nodeCity.getComponent(CityCtrl).setOnlyShow({ cityLevel: 1, hideUI: true, showName: true });
    this._nodeCity.addChild(nodeCity);
    nodeCity.setPosition(0, 0);

    const nodeAni = NodeTool.findByName(nodeCity, "ani");
    CityModule.service.setBuildSize(nodeAni, this._cityId, 1);

    this._startAni.play("TopCityBuildShowAni");
  }

  private on_click_btn_close() {
    this.closeBack();
  }

  private on_click_btn_go() {
    this.closeBack();
    GuideMgr.startGuide({ stepId: 22, args: { buildId: this._cityId } });
  }
}
