import { _decorator, Component, Node } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { AdapterView } from "db://assets/platform/src/core/ui/adapter_view/AdapterView";
import { DailyChallengeRankAdapter } from "../../adapter/DailyChallengeRankViewHolder";
const { ccclass, property } = _decorator;

@ccclass("UIDailyChallengeRank")
@routeConfig({
  bundle: BundleEnum.BUNDLE_DAILY_CHALLENGE,
  url: "prefab/ui/UIDailyChallengeRank",
  nextHop: [],
  exit: "dialog_close",
})
export class UIDailyChallengeRank extends BaseCtrl {
  public playShowAni: boolean = true;
  start() {
    super.start();
    let adapter = new DailyChallengeRankAdapter(this.getNode("viewholder_rank"));
    this.getNode("node_list").getComponent(AdapterView).setAdapter(adapter);
    adapter.setData([{}, {}, {}, {}]);
  }

  update(deltaTime: number) {}
}
