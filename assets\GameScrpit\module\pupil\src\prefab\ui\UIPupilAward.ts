import { _decorator, Node, Vec3, tween, Animation, instantiate } from "cc";
import ToolExt from "db://assets/GameScrpit/game/common/ToolExt";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { routeConfig, RouteShowArgs } from "db://assets/platform/src/core/managers/RouteTableManager";
import { BundleEnum } from "db://assets/platform/src/ResHelper";

const { ccclass, property } = _decorator;
@ccclass("UIPupilAward")
@routeConfig({
  bundle: BundleEnum.BUNDLE_G_PUPIL,
  url: "prefab/ui/UIPupilAward",
  nextHop: [],
  exit: "",
  transparent: true,
})
export default class UIPupilAward extends BaseCtrl {
  @property(Node)
  nodeAward: Node = null;
  private _toWorldPosition: Vec3 = new Vec3();
  private _itemList: number[];
  init(args: RouteShowArgs): void {
    this._toWorldPosition = args.payload.toWorldPosition;
    this._itemList = args.payload.itemList;
  }
  protected start(): void {
    super.start();
    this.nodeAward.active = false;
    let items = ToolExt.traAwardItemMapList(this._itemList);
    for (let i = 0; i < items.length; i++) {
      let itemAward = instantiate(this.nodeAward);
      itemAward.parent = this.node;
      itemAward.active = true;
      ToolExt.setItemIcon(itemAward, items[i].id);
      this.playAwardAnimation(itemAward);
    }
  }

  /**
   * 播放奖励动画：先放大再飞到目标位置
   */
  private playAwardAnimation(itemAward: Node) {
    if (!itemAward) {
      console.error("nodeAward is null");
      return;
    }

    // 保存初始位置和缩放
    const initialScale = itemAward.scale.clone();
    // 生成随机位置: 半径100-150内且Y轴为负
    const radius = 100 + Math.random() * 50; // 100到150的随机半径
    const angle = Math.random() * Math.PI * 2; // 0到2π的随机角度
    let randomX = Math.cos(angle) * radius; // X坐标
    let randomY = Math.sin(angle) * radius; // Y坐标
    // 确保Y轴为负
    if (randomY > 0) {
      randomY = -randomY;
    }

    let randomPosition = new Vec3(randomX, randomY, 0);
    let randomTween = tween(itemAward).to(0.3, { position: randomPosition }).start();
    let randomScale = tween(itemAward)
      .set({ scale: new Vec3(0.5, 0.5, 0.5) })
      .to(0.3, { scale: new Vec3(1.5, 1.5, 1.5) }); // 0.3秒内放大到1.5倍

    // 动画序列：先放大，再飞到目标位置
    let moveTween = tween(itemAward).to(0.3, { worldPosition: this._toWorldPosition }); // 0.7秒内飞到目标位置
    let scaleTween = tween(itemAward).to(0.3, { scale: new Vec3(0.5, 0.5, 0.5) }); // 0.3秒内恢复原始缩放

    tween(itemAward)
      .parallel(randomTween, randomScale)
      .delay(0.5)
      .parallel(moveTween, scaleTween)
      .call(() => {
        // 动画结束后的回调
        this.closeBack();
      })
      .start();
  }
}
