import { is<PERSON><PERSON><PERSON>, Node } from "cc";
import { Deque } from "../../../lib/utils/Deque";
import { ListAdapter, ViewHolder } from "./ListAdapter";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";
const log = Logger.getLoger(LOG_LEVEL.ERROR);
export class ViewHolderManager {
  private cacheViews: Map<number, Deque<Node>> = new Map();
  private _adapter: ListAdapter;
  private _startIndex: number = -1;
  private _endIndex: number = 0;
  private _wrapAroundIndex: boolean = false; //是否支持循环索引

  private _visibleViews: Deque<Node> = new Deque<Node>();
  constructor(adapter: ListAdapter) {
    this._adapter = adapter;
  }
  get firstVisiblePosition() {
    let index = this._startIndex + 1;
    if (index >= this._adapter.getCount()) {
      if (this.wrapAroundIndex) {
        return 0;
      } else {
        return this._adapter.getCount() - 1;
      }
    }
    return index;
  }
  get lastVisiblePosition() {
    let index = this._endIndex - 1;
    if (index < 0) {
      if (this.wrapAroundIndex) {
        return this._adapter.getCount() - 1;
      } else {
        return 0;
      }
    }
    return index;
  }
  get dataCount() {
    return this._adapter.getCount();
  }

  /**
   * 是否支持循环
   */
  set wrapAroundIndex(bool: boolean) {
    this._wrapAroundIndex = bool;
  }
  get wrapAroundIndex() {
    return this._wrapAroundIndex;
  }

  get visibleViews() {
    return this._visibleViews;
  }

  private addCache(viewHolder: Node) {
    let key = viewHolder.getComponent(ViewHolder).viewType;
    let cache = this.cacheViews.get(key);
    if (!cache) {
      cache = new Deque<Node>();
      this.cacheViews.set(key, cache);
    }
    cache.addFront(viewHolder);
  }
  private getCache(viewType: number): Node | undefined {
    let cache = this.cacheViews.get(viewType);
    if (!cache || !cache.peekRear()) {
      let view = this._adapter.onCreateView(viewType);
      view.getComponent(ViewHolder).viewType = viewType;
      return view;
    }
    return cache.removeRear();
  }

  public resetPosition() {
    this._startIndex = -1;
    this._endIndex = 0;

    // this._visibleViews.clear();
    this.recycleAllView();
  }
  private recycleAllView() {
    let node = this._visibleViews.removeRear();
    while (node) {
      this.addCache(node);
      node = this._visibleViews.removeRear();
    }
  }

  /**
   * 重新计算位置
   */
  public calcPosition() {
    let foucsPosition = this.firstVisiblePosition;
    if (foucsPosition >= this._adapter.getCount()) {
      this._startIndex = -1;
      this._endIndex = 0;
    } else {
      this._endIndex = foucsPosition;
    }
    this.recycleAllView();
    // this._visibleViews.clear();
    // let count = this._adapter.getCount();
    // this._startIndex = -1;
    // this._endIndex = count;
  }

  /**
   * 回收队列头部的节点
   * @param viewHolder
   */
  public recyclerHeader(): Node | null {
    let node = this.visibleViews.removeFront();
    if (node) {
      this._startIndex++;
      if (this._startIndex >= this._adapter.getCount()) {
        if (this.wrapAroundIndex) {
          this._startIndex = 0;
        } else {
          this._startIndex = this._adapter.getCount() - 1;
        }
      }
      this.addCache(node);
    }

    return node;
  }

  /**
   * 回收队列尾部的节点
   * @param viewHolder
   */
  public recyclerTail(): Node | null {
    let node = this.visibleViews.removeRear();
    if (node) {
      this._endIndex--;
      if (this._endIndex < 0) {
        if (this.wrapAroundIndex) {
          this._endIndex = this._adapter.getCount() - 1;
        } else {
          this._endIndex = 0;
        }
      }
      this.addCache(node);
    }
    return node;
  }

  public addNode(position: number) {
    let pos = position;
    if (pos < 0) {
      pos = 0;
    }
    if (pos >= this._adapter.getCount()) {
      pos = this._adapter.getCount() - 1;
    }
    if (this._adapter.getCount() == 0) {
      return null;
    }
    let viewType = this._adapter.getViewType(pos);
    let newNode = this.getCache(viewType);
    if (!newNode) {
      return null;
    }
    this.visibleViews.clear();
    this.visibleViews.addFront(newNode);

    newNode.getComponent(ViewHolder).position = pos;
    this._startIndex = pos - 1;
    this._endIndex = pos + 1;
    return newNode;
  }
  public addToHeader(): Node | null {
    if (this._adapter.getCount() == 0) {
      return null;
    }
    if (this._startIndex < 0) {
      if (!this.wrapAroundIndex) {
        return null;
      }
      this._startIndex = this._adapter.getCount() - 1;
    }
    let viewType = this._adapter.getViewType(this._startIndex);
    let newNode = this.getCache(viewType);
    newNode.getComponent(ViewHolder).position = this._startIndex;
    this.visibleViews.addFront(newNode);
    this._startIndex--;
    return newNode;
  }
  public addToTail(): Node | null {
    if (this._adapter.getCount() == 0) {
      return null;
    }
    if (this._endIndex >= this._adapter.getCount()) {
      if (!this.wrapAroundIndex) {
        return null;
      }
      this._endIndex = 0;
    }
    let viewType = this._adapter.getViewType(this._endIndex);
    let newNode = this.getCache(viewType);
    newNode.getComponent(ViewHolder).position = this._endIndex;
    this.visibleViews.addRear(newNode);
    this._endIndex++;
    return newNode;
  }

  public bindData(viewHolder: Node) {
    try {
      this._adapter.onBindData(viewHolder, viewHolder.getComponent(ViewHolder).position);
    } catch (error) {
      log.error(error);
    }
  }
  public onDestroy() {
    let node = this.visibleViews.removeFront();
    while (node) {
      isValid(node) && node.destroy();
      node = this.visibleViews.removeFront();
    }
    // 遍历 this.cacheViews 并释放其中的节点
    for (const [_, deque] of this.cacheViews) {
      let node = deque.removeRear();
      while (node) {
        if (isValid(node)) {
          node.destroy();
        }
        node = deque.removeRear();
      }
    }
    // 清空 cacheViews
    this.cacheViews.clear();
  }
}
