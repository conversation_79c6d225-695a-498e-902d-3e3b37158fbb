import {
  _decorator,
  Node,
  sp,
  Sprite,
  Tween,
  tween,
  Vec3,
  UITransform,
  instantiate,
  Label,
  AudioSource,
  Color,
} from "cc";
import { Event_ctrl } from "./Event_ctrl";
import { EventActionModule } from "db://assets/GameScrpit/module/event_action/src/EventActionModule";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import TickerMgr from "db://assets/GameScrpit/lib/ticker/TickerMgr";
import { EventRepelResponse } from "../../../net/protocol/WorldEvent";
import { SpineUtil } from "db://assets/platform/src/lib/utils/SpineUtil";
import { LayerEnum, Sleep } from "../../../GameDefine";
import { dtTime } from "../../../BoutStartUp";
import MsgMgr from "db://assets/GameScrpit/lib/event/MsgMgr";
import MsgEnum from "../../../event/MsgEnum";
import { EventCtrlManager } from "./EventCtrlManager";
import { JsonMgr } from "../../../mgr/JsonMgr";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import { ItemEnum } from "db://assets/GameScrpit/lib/common/ItemEnum";
import Formate from "db://assets/GameScrpit/lib/utils/Formate";
import { LangMgr } from "../../../mgr/LangMgr";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { UIMgr } from "db://assets/GameScrpit/lib/ui/UIMgr";
import { PlayerRouteName } from "db://assets/GameScrpit/module/player/PlayerConstant";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";

const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const atkActList = ["atk1", "atk1_1", "atk_2", "atk2_1", "atk3", "atk3_1"];
const idleActList = ["idle_0", "idle_1", "idle_2", "idle_3"];
@ccclass("path_thief_ctrl")
export class path_thief_ctrl extends Event_ctrl {
  private groupedPoints: Node[][] = []; // 二维数组存储分组路径点
  private currentGroup: Node[] = []; // 当前可用路径组
  private currentIndex = 0; // 当前节点在数组中的索引
  private moveSpeed = 100; // 移动速度
  private hpLayer: Node = null; // 新增血量条节点引用

  private renderNode: Node = null; // 修改后的主节点引用
  private sktNode: Node = null; // 新增骨骼动画节点引用
  private _currentTween: Tween<Node> | null = null; // 当前正在执行的缓动
  private _tickerTimeoutId: number = null;

  private _deathPaths: Map<string, Node[]> = new Map(); // 存储死亡路线

  private _ticker_audioId: number = null;
  /**
   * 初始化事件控制器
   * @param params - 初始化所需的参数，可以根据实际需求定义类型
   */
  public async initialize(params: any) {
    super.initialize(params);

    MsgMgr.off(MsgEnum.ON_SANJIE_THIEF_HP_UPDATE, this.updateHpBar, this);
    MsgMgr.on(MsgEnum.ON_SANJIE_THIEF_HP_UPDATE, this.updateHpBar, this);

    MsgMgr.off(MsgEnum.ON_HEART_BEAT, this.onHartBeat, this);
    MsgMgr.on(MsgEnum.ON_HEART_BEAT, this.onHartBeat, this);

    // // 获取事件配置
    // const data = this.getEventConfig(params.eventId);
    // if (data) {
    //   this.node.setPosition(new Vec3(data.place[0], data.place[1]));
    // }

    // 初始化节点引用
    this.renderNode = this.node.getChildByName("render");
    this.sktNode = this.renderNode?.getChildByName("skt");
    this.hpLayer = this.renderNode?.getChildByName("hp_layer");

    // 节点存在性检查
    if (!this.checkNodes()) return;

    // 事件监听
    this.sktNode.off(Node.EventType.TOUCH_END, this.onThiefClicked, this);
    this.sktNode.on(Node.EventType.TOUCH_END, this.onThiefClicked, this);

    // 初始化动画状态
    this.setIdleAction();
    this.updateHpBar();

    // 路径分组处理
    const { pathGroups, deathGroups } = this.groupPathNodes();

    // 存储分组数据
    this.groupedPoints = Object.values(pathGroups).filter((g) => g.length > 0);
    this.storeDeathPaths(deathGroups);

    // 初始化移动
    this.initMovement();
    await Sleep(dtTime);
    this.randomMove();
  }

  // 新增辅助方法
  private checkNodes(): boolean {
    const nodes = [
      { node: this.renderNode, name: "render" },
      { node: this.sktNode, name: "skt" },
      { node: this.hpLayer, name: "hp_layer" },
    ];

    return nodes.every(({ node, name }) => {
      if (!node) log.error(`未找到名为${name}的子节点`);
      return !!node;
    });
  }

  private groupPathNodes() {
    const pathGroups: { [key: string]: Node[] } = {};
    const deathGroups: { [key: string]: Node[] } = {};

    // 先处理字母开头的死亡路径点
    this.node.children.forEach((child) => {
      const name = child.name;
      if (/^[A-Z]run\d+$/.test(name)) {
        const groupKey = name[0];
        (deathGroups[groupKey] ??= []).push(child);
      }
    });

    // 后处理公共路径点并追加到现有分组
    this.node.children.forEach((child) => {
      const name = child.name;
      if (/^run\d+$/.test(name)) {
        Object.keys(deathGroups).forEach((key) => {
          // 添加到对应字母组的末尾，保证自定义路线在前
          deathGroups[key].push(child);
        });
      }
    });

    // 原有移动路径点处理保持不变
    this.node.children.forEach((child) => {
      const name = child.name;
      if (/^[A-Z]\d+$/.test(name)) {
        const groupKey = name[0];
        (pathGroups[groupKey] ??= []).push(child);
      }
    });

    return { pathGroups, deathGroups };
  }

  private storeDeathPaths(deathGroups: { [key: string]: Node[] }) {
    Object.entries(deathGroups).forEach(([key, nodes]) => {
      this._deathPaths.set(
        key,
        nodes.sort((a, b) => {
          // 新增类型判断：大写字母开头的节点优先
          const aIsCustom = /^[A-Z]/.test(a.name);
          const bIsCustom = /^[A-Z]/.test(b.name);

          // 类型不同时，自定义节点（大写开头）排在前
          if (aIsCustom !== bIsCustom) {
            return bIsCustom ? 1 : -1;
          }

          // 类型相同时按数字排序
          const aNum = parseInt(a.name.match(/\d+/)[0]);
          const bNum = parseInt(b.name.match(/\d+/)[0]);
          return aNum - bNum;
        })
      );
    });
  }

  private initMovement() {
    this.currentGroup = this.groupedPoints[Math.floor(Math.random() * this.groupedPoints.length)];
    if (this.currentGroup?.length > 0) {
      this.renderNode.position = this.currentGroup[0].position.clone();
      this.renderNode.active = true;
    }
  }

  private randomMove() {
    if (this.currentGroup.length <= 1) {
      return; // 节点组中节点数量不足，无法移动
    }

    // 排除当前节点
    const availableIndices = Array.from({ length: this.currentGroup.length }, (_, i) => i).filter(
      (i) => i !== this.currentIndex
    );

    if (availableIndices.length === 0) {
      return; // 没有可用节点，无法移动
    }

    // 随机选择下一个目标节点的索引
    const randomIndex = availableIndices[Math.floor(Math.random() * availableIndices.length)];
    const targetNode = this.currentGroup[randomIndex];
    this.currentIndex = randomIndex;

    // 计算两点之间的距离
    const distance = Vec3.distance(this.renderNode.position, targetNode.position);
    // 根据速度计算移动时间
    const moveDuration = distance / this.moveSpeed;

    // 计算移动方向
    const currentPos = this.renderNode.position;
    const targetPos = targetNode.position;
    const direction = targetPos.x - currentPos.x;

    // 根据方向调整 scale
    if (direction < 0) {
      this.sktNode.scale = new Vec3(Math.abs(this.sktNode.scale.x), this.sktNode.scale.y, this.sktNode.scale.z);
    } else if (direction > 0) {
      this.sktNode.scale = new Vec3(-Math.abs(this.sktNode.scale.x), this.sktNode.scale.y, this.sktNode.scale.z);
    }

    // 使用 tween 缓动移动
    this._currentTween = tween(this.renderNode)
      .to(moveDuration, {
        position: targetNode.position.clone(),
      })
      .call(() => {
        this.randomMove();
      })
      .start();
  }

  private onThiefClicked(event: Event) {
    log.log("小偷被点击");
    AudioMgr.instance.playEffect(1726);

    if (this.renderNode == null) {
      return;
    }
    if (this.sktNode == null) {
      return;
    }

    let obj = EventActionModule.data.eventTrainMessage.eventMap[this._params.eventId];
    if (!obj) {
      log.error("错误的，这个事件已经结束了,拒绝发送消息");
      return;
    }

    let allCount = obj.totalProgress;
    let progress = obj.progress;

    if (progress >= allCount) {
      log.error("错误的，这个事件已经结束了,拒绝发送消息");
      return;
    }

    if (this.renderNode.layer !== LayerEnum.TOP) {
      UIMgr.instance.showDialog(PlayerRouteName.UIThief);
      return;
    }

    //TipsMgr.setEnableTouch(false, 5);
    if (this._currentTween) {
      this._currentTween.stop();
    }
    // 修改定时器调用部分
    this._tickerTimeoutId = TickerMgr.setTimeout(5, () => {
      if (this.renderNode && this.sktNode && this._currentTween) {
        // 添加安全判断
        this._currentTween.start();
      }
    });

    EventActionModule.api.postExpel(
      this._params.eventId,
      async (data: EventRepelResponse) => {
        log.log("驱赶小偷事件", data);
        this.node.name = "run";
        await this.setAtkAction();

        let obj = EventActionModule.data.eventTrainMessage.eventMap[this._params.eventId];

        // 新增30%概率触发特殊事件
        if (obj && obj.progress < obj.totalProgress && Math.random() < 0.3) {
          await this.handleSpecialEvent(); // 调用新增的触发方法
        }

        if (obj && obj.progress >= obj.totalProgress) {
          EventActionModule.api.getEventInfo();
          await this.thiefDie();
        }

        if (data.rewardMessage) {
          MsgMgr.emit(MsgEnum.ON_GET_AWARD, {
            itemList: data.rewardMessage.rewardList,
            transformList: data.rewardMessage.transformList,
          });
          if (obj && obj.progress >= obj.totalProgress) {
            EventCtrlManager.instance.removeEvent(this._params.eventId);
          }
        }
      },
      (errorCode: number, msg: string[], data: any): boolean => {
        TipsMgr.setEnableTouch(true);
        TipsMgr.showErrX(errorCode, msg);
        return true;
      }
    );
  }

  private setAtkAction() {
    this.sktNode.getComponent(sp.Skeleton).setCompleteListener(null);
    return new Promise(async (resolve) => {
      // 播放攻击音效
      const audioList = [1722, 1723];
      const audioId = audioList[Math.floor(Math.random() * audioList.length)];
      AudioMgr.instance.playEffect(audioId);

      // 获取当前动画名称
      const currentAnim = this.sktNode.getComponent(sp.Skeleton).getCurrent(0)?.animation?.name;

      // 根据当前动画名称选择攻击动作
      let availableActs = atkActList;
      if (currentAnim && currentAnim.includes("_1")) {
        availableActs = atkActList.filter((act) => !act.includes("_1")); // 排除带_1的动作
      } else {
        availableActs = atkActList.filter((act) => act.includes("_1")); // 只保留带_1的动作
      }

      let index = this.getActIndex();
      let key = availableActs[Math.min(availableActs.length - 1, index)]; // 从可用动作中选择

      this.sktNode.getComponent(sp.Skeleton).setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if (trackEntry.animation.name == key) {
          resolve(true);
          this.setIdleAction();
          TipsMgr.setEnableTouch(true);
          this.sktNode.getComponent(sp.Skeleton).setCompleteListener(null);
        }
      });

      this.sktNode.getComponent(sp.Skeleton).setAnimation(0, key, false);
    });
  }

  private setIdleAction() {
    let index = this.getActIndex();
    let key = idleActList[index];
    SpineUtil.playSpine(this.sktNode.getComponent(sp.Skeleton), key, true);
  }

  private getActIndex() {
    let obj = EventActionModule.data.eventTrainMessage.eventMap[this._params.eventId];

    if (!obj) {
      obj = {
        progress: 1,
        totalProgress: 1,
        rewardList: [],
      };
    }

    let allCount = obj.totalProgress;
    let progress = obj.progress;
    let list = this.divideNumberIntoThreePartsWithAccumulation(allCount);
    let index = this.findIndexGreaterOrEqual(progress, list);
    return index;
  }

  // 在类属性区域新增
  private _dieTween: Tween<Node> | null = null; // 死亡动画tween
  private _specialTween: Tween<Node> | null = null; // 特殊事件飘字tween
  private _hartbeatTween: Tween<Node> | null = null; // 心跳扣血飘字tween

  private async thiefDie() {
    this.sktNode.off(Node.EventType.TOUCH_END, this.onThiefClicked, this);
    this.onReturn();
    MsgMgr.off(MsgEnum.ON_HEART_BEAT, this.onHartBeat, this); //逃跑的时候就不飘字了

    const audioSource = this.node.addComponent(AudioSource);
    const clip = await AudioMgr.instance.getClipById(1721);
    if (!clip) {
      log.error("音频资源加载失败");
      return;
    }
    audioSource.clip = clip;
    audioSource.loop = true;
    audioSource.play();

    // let audio = AudioMgr.instance.playEffect(1721);
    // if (audio) {
    //   this._ticker_audioId = TickerMgr.setInterval(
    //     duration,
    //     () => {
    //       if (this.node.isValid == false) {
    //         return;
    //       }
    //       AudioMgr.instance.playEffect(1721);
    //     },
    //     false
    //   );
    // }

    return new Promise<void>(async (resolve) => {
      // 在类属性区域新增移动速度常量（或复用现有的moveSpeed）
      const ESCAPE_SPEED = 500; // 逃跑速度，单位：单位/秒

      // 停止当前所有动画
      if (this._currentTween) {
        this._currentTween.stop();
        this._currentTween = null;
      }

      // 获取当前路径组对应的字母(通过第一个路径点的名称)
      const groupKey = this.currentGroup[0]?.name.charAt(0) || "A";
      // 获取对应的死亡路径节点数组
      const deathPath = this._deathPaths.get(groupKey) || [];

      log.log("逃跑路径节点=====", deathPath);

      // 修改死亡路径处理逻辑
      const targetPositions = deathPath.length > 0 ? deathPath.map((node) => node.position.clone()) : []; // 删除默认die节点位置

      // 添加路径空校验
      if (targetPositions.length === 0) {
        log.error("未配置死亡路径，请检查场景节点命名");
        resolve();
        return;
      }
      // 隐藏血量条
      this.renderNode.getChildByName("hp_layer").active = false;
      this.sktNode.getComponent(sp.Skeleton).setAnimation(0, "run", true);

      // 修改后的移动动画序列
      this._dieTween = tween(this.renderNode)
        .parallel(
          tween().sequence(
            ...targetPositions.map((pos, index) => {
              // 计算移动距离（使用前一个位置作为起点）
              const prevPos = index === 0 ? this.renderNode.position : targetPositions[index - 1];
              const distance = Vec3.distance(prevPos, pos);
              const duration = distance / ESCAPE_SPEED; // 根据距离和速度计算时间

              const direction = pos.x - prevPos.x;

              return tween()
                .call(() => {
                  // 方向判断（保持现有逻辑不变）
                  if (direction < 0) {
                    this.sktNode.scale = new Vec3(
                      Math.abs(this.sktNode.scale.x),
                      this.sktNode.scale.y,
                      this.sktNode.scale.z
                    );
                  } else {
                    this.sktNode.scale = new Vec3(
                      -Math.abs(this.sktNode.scale.x),
                      this.sktNode.scale.y,
                      this.sktNode.scale.z
                    );
                  }
                })
                .to(
                  duration, // 使用计算后的时间
                  { position: pos },
                  {
                    onUpdate: (target: Node) => {
                      // 实时方向检测
                      const currentDirection = pos.x - target.position.x;
                      if (currentDirection * direction < 0) {
                        this.sktNode.scale = new Vec3(
                          -this.sktNode.scale.x,
                          this.sktNode.scale.y,
                          this.sktNode.scale.z
                        );
                      }
                    },
                  }
                );
            })
          )
        )
        .call(() => resolve())
        .start();
    });
  }

  // 新增血量条更新方法
  private updateHpBar() {
    if (!this.hpLayer) return;

    const eventId = this._params.eventId;
    let obj = EventActionModule.data.eventTrainMessage.eventMap[eventId];
    if (!obj) {
      obj = {
        progress: 1,
        totalProgress: 1,
        rewardList: [],
      };
    }
    let totalCount = obj.totalProgress;
    let currentCount = obj.progress;

    // 计算填充比例 (0~1)
    const fillAmount = Math.min(currentCount / totalCount);

    const bar = 1 - fillAmount;

    // 更新进度条
    const barNode = this.hpLayer.getChildByName("bar");

    if (barNode && barNode.getComponent(Sprite)) {
      barNode.getComponent(Sprite).fillRange = bar;
    }
  }

  // 新增特殊事件处理方法
  private async handleSpecialEvent() {
    let db = JsonMgr.instance.jsonList.c_event2[this._params.eventId];
    if (!db) {
      log.error("c_event2" + "没有" + this._params.eventId + "对应的配置");
      return;
    }

    log.log("触发30%概率特殊事件！");
    const wordList = db.word; // 获取文字数组
    const randomWord = wordList[Math.floor(Math.random() * wordList.length)]; // 随机选择一句

    // 克隆语言标签节点
    const originalNode = this.renderNode.getChildByName("lbl_language");
    if (!originalNode) {
      log.error("未找到lbl_language节点");
      return;
    }

    // 创建克隆节点
    const cloneNode = instantiate(originalNode);
    cloneNode.active = true;

    cloneNode.getComponent(Label).string = LangMgr.txMsgCode(randomWord, []);

    // 坐标转换（从hp_layer局部坐标转到世界坐标，再转为node的本地坐标）
    const worldPos = this.hpLayer.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);
    const localPos = this.node.getComponent(UITransform)!.convertToNodeSpaceAR(worldPos);
    cloneNode.setPosition(localPos);

    // 添加到场景
    this.node.addChild(cloneNode);

    // 执行缓动动画
    this._specialTween = tween(cloneNode)
      .parallel(
        tween().by(1.5, { position: new Vec3(0, 50, 0) }, { easing: "sineOut" }) // 上飘200单位
      )
      .call(() => {
        cloneNode.destroy(); // 动画完成后销毁节点
      })
      .start();
  }

  private onHartBeat() {
    let db = JsonMgr.instance.jsonList.c_event2[this._params.eventId];
    if (!db) {
      log.error("c_event2" + "没有" + this._params.eventId + "对应的配置");
      return;
    }
    let debuff = db.debuff;
    // debuff索引0是1代表气运，索引1代表的是万分比
    let fan_rong_du = PlayerModule.data.getItemNum(ItemEnum.繁荣度_2);

    // 计算扣除值（万分比转换）
    const reduceValue = Math.floor((fan_rong_du * debuff[1]) / 10000);

    // 克隆扣除数值节点
    const debuffNumNode = this.renderNode.getChildByName("debuff_num");
    if (debuffNumNode) {
      const cloneNode = instantiate(debuffNumNode);
      cloneNode.active = true;

      const Label_lbl_message = cloneNode.getChildByName("lbl_message").getComponent(Label);
      if (Label_lbl_message) Label_lbl_message.string = LangMgr.txMsgCode(246, []);

      // 设置数值显示
      const label = cloneNode.getChildByName("num").getComponent(Label);
      if (label) label.string = `-${Formate.format(reduceValue)}`;

      // 坐标转换
      const worldPos = this.hpLayer.getComponent(UITransform)!.convertToWorldSpaceAR(Vec3.ZERO);
      const localPos = this.node.getComponent(UITransform)!.convertToNodeSpaceAR(worldPos);
      cloneNode.setPosition(localPos);

      // 添加到场景
      this.node.addChild(cloneNode);

      // 执行缓动动画
      this._hartbeatTween = tween(cloneNode)
        .parallel(tween().by(1.5, { position: new Vec3(0, 100, 0) }, { easing: "sineOut" }))
        .call(() => cloneNode.destroy())
        .start();
    }
  }

  // 新增销毁时的事件移除
  onDestroy() {
    // 停止所有缓动动画
    [this._currentTween, this._dieTween, this._specialTween, this._hartbeatTween].forEach((t) => {
      if (t) {
        t.stop();
        t = null;
      }
    });
    if (this._currentTween) {
      this._currentTween.stop();
      this._currentTween = null;
    }

    if (this._tickerTimeoutId) {
      TickerMgr.clearTimeout(this._tickerTimeoutId);
      this._tickerTimeoutId = null;
    }

    if (this._ticker_audioId) {
      TickerMgr.clearInterval(this._ticker_audioId);
      this._ticker_audioId = null;
    }

    MsgMgr.off(MsgEnum.ON_SANJIE_THIEF_HP_UPDATE, this.updateHpBar, this);
    MsgMgr.off(MsgEnum.ON_HEART_BEAT, this.onHartBeat, this);
  }

  update(deltaTime: number): void {
    this.node.setSiblingIndex(this.node.parent.children.length - 1);
  }
  onReturn() {
    TipsMgr.setEnableTouch(false, 0.5);
    MsgMgr.emit(MsgEnum.ON_THIEF_UNFOCOUS, this.sktNode);
    MsgMgr.emit(MsgEnum.ON_UIMAIN_SHOW_ANI);

    this.renderNode.walk((child) => {
      child.layer = LayerEnum.UI;
    });
  }

  showUI() {
    MsgMgr.emit(MsgEnum.ON_THIEF_FOCOUS, this.sktNode);
    MsgMgr.emit(MsgEnum.ON_UIMAIN_HIDE_ANI);
    this.renderNode.walk((child) => {
      child.layer = LayerEnum.TOP;
    });
    TipsMgr.setEnableTouch(false, 1);

    RouteManager.uiRouteCtrl.showPrefab(BundleEnum.BUNDLE_G_GAME_MAP, "prefab/event_base/ui_thief", {
      ctrl: this,
      methodName: "onThiefClicked",
    });
  }
}
