import { _decorator, EventTouch, Layout, Node, NodeEventType, UITransform } from "cc";
import { ListAdapter, ViewHolder } from "../adapter_view/ListAdapter";
import { Deque } from "../../../lib/utils/Deque";
import { NodeTool } from "../../../../../GameScrpit/lib/utils/NodeTool";
import { ScrollableView } from "./ScrollableView";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

@ccclass("ExpandScrollView")
export class ExpandScrollView extends ScrollableView {
  private _adapter: ListAdapter;
  private _index: number = 0;
  private _cacheView: Map<number, Deque<Node>> = new Map();
  private _visibleViews: Deque<Node> = new Deque<Node>();

  private _expandNode: Node = null;
  private _currentSelectItem: Node = null;
  private _columnWidth: number;
  private _columnNum: number;

  private _expandIndex = -1;
  start() {}

  update(deltaTime: number) {
    super.update(deltaTime);
    if (this._adapter) {
      for (let i = 0; i < 6 && this._index < this._adapter.getCount(); i++) {
        // log.log(this._adapter.getViewType(this._index));
        let item = this.getCacheView(this._adapter.getViewType(this._index));
        // log.log(item, this._adapter.getCount());
        if (!item) {
          item = this._adapter.onCreateView(this._adapter.getViewType(this._index));
        }
        try {
          this._adapter.onBindData(item, this._index);
        } catch (error) {
          log.error(error);
        }
        if (this._expandNode) {
          item.on(Node.EventType.TOUCH_END, this.onClickExpand, this);
        }
        this._visibleViews.addRear(item);
        this.content.addChild(item);
        this._index++;
      }
      if (this._index == this._adapter.getCount() && this._expandIndex != -1) {
        this.expandIndex(this._expandIndex);
        this._expandIndex = -1;
      }
    }
  }
  private getViewIndex(target: Node): number {
    for (let i = 0; i < this._visibleViews.size(); i++) {
      if (target == this._visibleViews.get(i)) {
        return i;
      }
    }
  }
  public expandIndex(index: number) {
    if (this._adapter && this._index < this._adapter.getCount()) {
      this._expandIndex = index;
      return;
    }
    let target = this.content.children[index];
    if (target && target != this._currentSelectItem) {
      if (target.getComponent(ViewHolder).setSelect(true, this._expandNode)) {
        this.expand_item(index, target);
        this._currentSelectItem = target;
      }
      return;
    }
  }
  private onClickExpand(event: EventTouch) {
    let target: Node = event.target;
    let index = target.getSiblingIndex();
    if (this._currentSelectItem == target) {
      if (target.getComponent(ViewHolder).setSelect(false, this._expandNode)) {
        this.expand_item(index, target);
        this._currentSelectItem = null;
      }
    } else {
      if (target.getComponent(ViewHolder).setSelect(true, this._expandNode)) {
        this.expand_item(index, target);
        this._currentSelectItem = target;
      }
    }
  }
  private expand_item(index: number, target: Node) {
    // log.log(this._currentSelectItem, index);
    if (this._currentSelectItem != null) {
      let curExIndex = this._expandNode.getSiblingIndex();
      this._currentSelectItem.getComponent(ViewHolder).setSelect(false, this._expandNode);
      this.content.removeChild(this._expandNode);
      if (curExIndex < index) {
        index = index - 1;
      }
      if (target == this._currentSelectItem) {
        return;
      }
    }
    let insertPosition = Math.floor(index / this._columnNum) * this._columnNum + this._columnNum;

    // this._expandNode.once(
    //   Node.EventType.TRANSFORM_CHANGED,
    //   () => {
    //     let scrollWorldAR = this.node
    //       .getComponent(UITransform)
    //       .convertToNodeSpaceAR(this._expandNode.getWorldPosition());
    //     let offsetY = this._expandNode.getPosition().y - scrollWorldAR.y;
    //     let bottomOfScroll = NodeTool.getNodeBottom(this._expandNode) - offsetY;
    //     if (this._expandNode.parent && bottomOfScroll < NodeTool.getBorderBottom(this.node)) {
    //       let overOffset = NodeTool.getBorderBottom(this.node) - bottomOfScroll;
    //       log.log("overOffset", overOffset);
    //       this.node.getComponent(ScrollableView).scrollTo(0, overOffset, true);
    //     }
    //   },
    //   this
    // );
    this.content.once(
      NodeEventType.SIZE_CHANGED,
      () => {
        let scrollWorldAR = this.node
          .getComponent(UITransform)
          .convertToNodeSpaceAR(this._expandNode.getWorldPosition());
        let offsetY = this._expandNode.getPosition().y - scrollWorldAR.y;
        let bottomOfScroll = NodeTool.getNodeBottom(this._expandNode) - offsetY;
        if (this._expandNode.parent && bottomOfScroll < NodeTool.getBorderBottom(this.node)) {
          let overOffset = NodeTool.getBorderBottom(this.node) - bottomOfScroll;
          log.log("overOffset", overOffset);
          this.node.getComponent(ScrollableView).scrollTo(0, overOffset, true);
        }
      },
      this
    );
    this.content.insertChild(this._expandNode, insertPosition);

    return true;
  }
  private setCacheView(viewType: number, view: Node) {
    if (this._cacheView.has(viewType)) {
      this._cacheView.get(viewType).addFront(view);
    } else {
      let views = new Deque<Node>();
      views.addFront(view);
      this._cacheView.set(viewType, views);
    }
  }
  private getCacheView(viewType: number): Node {
    if (this._cacheView.has(viewType)) {
      return this._cacheView.get(viewType).removeRear();
    }
    return null;
  }
  private onDataSetChange(data: any) {
    if (data) {
      //只刷新数据
      for (let i = 0; i < this._visibleViews.size(); i++) {
        this._adapter.onBindData(this._visibleViews.get(i), i);
      }
    } else {
      // log.log(this._cacheView);
      this.node.getComponent(ScrollableView).scrollToStart(false);
      if (this._currentSelectItem != null) {
        //清空选中状态
        this._currentSelectItem.getComponent(ViewHolder).setSelect(false, this._expandNode);
        this._currentSelectItem = null;
      }
      this._cacheView.clear();
      for (let i = 0; i < this._visibleViews.size(); i++) {
        this.setCacheView(this._adapter.getViewType(i), this._visibleViews.get(i));
      }
      this._visibleViews.clear();
      this.content.removeAllChildren();
      this._index = 0;
      //创建可扩展node
      if (this._expandNode) {
        this._expandNode.destroy();
      }
      this._expandNode = this._adapter.onCreateView(-1);
    }
  }
  public setAdapter(adapter: ListAdapter) {
    this._adapter = adapter;
    this._adapter.setObserver(this.onDataSetChange.bind(this));
  }
  public setColumnWidth(columnWidth: number) {
    if (columnWidth <= 1) {
      return;
    }
    this._columnWidth = columnWidth;
    let width = this.content.getComponent(UITransform).width;
    let columnNum = Math.floor(width / this._columnWidth);
    let space = (width - columnNum * columnWidth) / (columnNum + 1);
    this.setLayoutParams(columnWidth, space, space, Math.floor(space), space, space, space);
  }
  public setLayoutParams(
    columnWidth: number,
    paddingLeft: number,
    paddingTop: number,
    paddingRight: number,
    paddingBottom: number,
    spaceX: number,
    spaceY: number
  ) {
    this._columnWidth = columnWidth;

    this.content.getComponent(Layout).paddingTop = paddingTop;
    this.content.getComponent(Layout).paddingLeft = paddingLeft;
    this.content.getComponent(Layout).paddingRight = paddingRight;
    this.content.getComponent(Layout).paddingBottom = paddingBottom;
    this.content.getComponent(Layout).spacingX = spaceX;
    this.content.getComponent(Layout).spacingY = spaceY;
    let contentWidth = this.content.getComponent(UITransform).width - paddingLeft - paddingRight + spaceX;
    this._columnNum = Math.floor(contentWidth / (columnWidth + spaceX));
  }
}
