import { _decorator, Label, Node, sp, tween, UITransform, v2, v3, Vec3 } from "cc";
import { UIMgr } from "../../../lib/ui/UIMgr";
import { UINode } from "../../../lib/ui/UINode";
import { FightModule, FightRouteItem } from "../../../module/fight/src/FightModule";
import { BundleEnum } from "../../bundleEnum/BundleEnum";
import FightManager from "../../fight/manager/FightManager";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import ToolExt from "../../common/ToolExt";
import { BossChapterRewardResponse } from "../../net/protocol/Chapter";
import { PlayerModule } from "../../../module/player/PlayerModule";
import TipMgr from "../../../lib/tips/TipMgr";
import { GameDirector } from "../../GameDirector";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { SpGuide103002 } from "../../../module/fight/src/FightConstant";
import { JsonMgr } from "../../mgr/JsonMgr";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import { Sleep } from "../../GameDefine";
import { actionDB } from "../../fight/section/AnimationSection";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;
const skipLabNum = 5;

@ccclass("UIFightPage")
export class UIFightPage extends UINode {
  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FIGHT}?prefab/ui/UIFightPage`;
  }

  protected dependOn(): Array<BundleEnum> {
    return [BundleEnum.BUNDLE_COMMON_UI, BundleEnum.BUNDLE_COMMON_ITEM];
  }

  private _args = null;

  private _round: number = 0;

  // 抖动cd
  private shakeCd: number = 0;

  private _maxRound = null;
  public init(args: any): void {
    super.init(args);
    this._args = args;
    this._maxRound = args.data.fightData.f;
  }

  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
    MsgMgr.on(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
    MsgMgr.on(MsgEnum.ON_SHAKE_WORLD, this.shakeWorld, this);
  }

  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
    MsgMgr.off(MsgEnum.ON_FIGHT_ROUND_UPDATE, this.setRoundShow, this);
    MsgMgr.off(MsgEnum.ON_SHAKE_WORLD, this.shakeWorld, this);
  }

  private shakeWorld() {
    let ts = new Date().valueOf();
    if (this.shakeCd < ts) {
      // 0.2秒内不在抖动
      this.shakeCd = ts + 200;
      this.shake(this.getNode("skt_world_bg"), v3(0, 0));
    }
  }

  /** 抖动具体方法 */
  private shake(node: Node, endPos: Vec3, amplitude: number = 20) {
    const tickTime = 0.05;

    // x轴震动
    let t1 = tween(node)
      .set({ position: endPos })
      .by(tickTime / 2, { position: v3(-amplitude / 2, 0) })
      .by(tickTime, { position: v3(+amplitude, 0) })
      .by(tickTime, { position: v3(-amplitude / 2, 0) });

    let t2 = tween(node)
      .delay(tickTime / 4)
      .by(tickTime / 2, { position: v3(0, amplitude / 2) })
      .by(tickTime, { position: v3(0, -amplitude) })
      .by(tickTime, { position: v3(0, +amplitude / 2) })
      .set({ position: endPos });
    tween(node).parallel(t1, t2).start();
  }

  protected async onEvtShow() {
    // tween(this.getNode("main"))
    //   .to(0.5, { scale: v3(0.8, 0.8, 1) })
    //   .to(0.5, { scale: v3(1.2, 1.2, 1) })
    //   .to(0.5, { scale: v3(1, 1, 1) })
    //   .start();
    this.getNode("main").active = false;
    await this.loadBg();

    this.getNode("btn_fightSkip").active = false;
    this.setTimeScaleLab();

    let posMap = new Map([
      [1, v2(-200, -200)],
      [2, v2(200, -200)],
    ]);
    let contentSize = this.getNode("main").getComponent(UITransform);
    this.getNode("main").setScale(0, 0, 0);
    FightManager.instance.start({
      main: this.node,
      parent: this.getNode("fightPoint"),
      fight: this._args.data.fightData,
      posMap: posMap,
      playId: this._args.data.fightData.a,
      contentSize: contentSize,
    });
  }
  private async loadBg() {
    let background = this.getMapInfo();
    let roundBg = background.roundBg;
    let pathList = roundBg.split("?");

    let bg_spine_asset = await this.assetMgr.loadSpineSync(BundleEnum.BUNDLE_COMMON_UI, pathList[1]);
    this.getNode("skt_world_bg").getComponent(sp.Skeleton).skeletonData = bg_spine_asset;
    this.getNode("skt_world_bg").active = true;
    this.getNode("main").active = true;
    SpineUtil.playSpine(this.getNode("skt_world_bg").getComponent(sp.Skeleton), background.waiting, true);
  }

  private getMapInfo() {
    let chapterId = FightModule.data.chapterId;
    let levelCopy = JsonMgr.instance.jsonList.c_copyMain[chapterId];
    let background = JsonMgr.instance.jsonList.c_background[levelCopy.bgId];

    return background;
  }

  /**更新回合显示 */
  private async setRoundShow(roundNumber: number) {
    this._round = roundNumber;

    this["roundLab"].getComponent(Label).string = `第${this._round}/${this._maxRound}回合`;
    if (this._round >= skipLabNum) {
      this.getNode("btn_fightSkip").active = true;
      this.getNode("skipLab").active = false;
    } else {
      this.getNode("btn_fightSkip").active = false;
      this.getNode("skipLab").active = true;
      this.getNode("skipLab").getComponent(Label).string = skipLabNum - this._round + "回合可后跳过战斗";
    }
  }

  /**时间倍数按钮的显示 */
  private setTimeScaleLab() {
    this["scaleTimeLab"].getComponent(Label).string = "x" + (PlayerModule.data.fightSpeed + 1);
  }

  private on_click_btn_timeScale() {
    PlayerModule.service.changeFightSpeed();
    this.setTimeScaleLab();
  }

  private async fightEnd() {
    if (this._args.data.win == false) {
      SpineUtil.playSpine(this.getNode("skt_world_bg").getComponent(sp.Skeleton), this.getMapInfo().lose, true);
      await Sleep(1.5);
      UIMgr.instance.showDialog(
        FightRouteItem.UIFightLose,
        null,
        () => {},
        () => {
          MsgMgr.off(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
        }
      );
    } else {
      SpineUtil.playSpine(this.getNode("skt_world_bg").getComponent(sp.Skeleton), this.getMapInfo().win, true);
      await Sleep(1.5);
      this.getChapterAward();
    }
  }

  private getChapterAward() {
    const chapterId = FightModule.data.chapterId;
    let callback = async (res: BossChapterRewardResponse) => {
      UIMgr.instance.showDialog(
        FightRouteItem.UIFightWin,
        { resAddList: res.resAddList, chapterId: chapterId },
        () => {
          ToolExt.replaceLevelMain();
        },
        () => {
          MsgMgr.off(MsgEnum.ON_FIGHT_END, this.fightEnd, this);
        }
      );

      log.warn("检查首充");
      // 显示首充
      if (FightModule.data.stepPass1030002 == SpGuide103002.SECOND_FIGHT) {
        log.warn("显示首充");
        FightModule.data.stepPass1030002 = SpGuide103002.SECOND_FIGHT_FINISH;
      }
      // 检查模块开启
      await GameDirector.instance.checkAndLoadModule(true);
    };
    FightModule.api.takePassBossChapterReward(callback);
  }

  private on_click_btn_fightSkip() {
    if (this._round < skipLabNum) {
      TipMgr.showTip("第五回合后跳过战斗");
      return;
    }
    TipsMgr.setEnableTouch(false, 3);
    // if (FightManager.instance.fightOver == true) {
    //   log.error("结算中");
    //   return;
    // }
    FightManager.instance.fightOver = true;
    MsgMgr.emit(MsgEnum.ON_FIGHT_SKIP);
  }

  public tick(dt: any): void {
    FightManager.instance.tick(dt);
  }

  protected onEvtClose(): void {
    FightManager.instance.exit();
  }
}
