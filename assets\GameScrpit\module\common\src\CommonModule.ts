import { GameData } from "../../../game/GameData";
import data from "../../../lib/data/data";
import { CommonApi } from "./CommonApi";
import { CommonConfig } from "./CommonConfig";
import { CommonData } from "./CommonData";
import { CommonRoute } from "./CommonRoute";
import { CommonService } from "./CommonService";
import { CommonSubscriber } from "./CommonSubscriber";
import { CommonViewModel } from "./CommonViewModel";

export class CommonModule extends data {
  private constructor() {
    super();
  }
  public static get instance(): CommonModule {
    if (!GameData.instance.CommonModule) {
      GameData.instance.CommonModule = new CommonModule();
    }
    return GameData.instance.CommonModule;
  }
  private _data = new CommonData();
  private _api = new CommonApi();
  private _service = new CommonService();
  private _subscriber = new CommonSubscriber();
  private _route = new CommonRoute();
  private _viewModel = new CommonViewModel();
  private _config = new CommonConfig();

  public static get data() {
    return this.instance._data;
  }

  public static get api() {
    return this.instance._api;
  }

  public static get config() {
    return this.instance._config;
  }

  public static get service() {
    return this.instance._service;
  }

  public static get viewModel() {
    return this.instance._viewModel;
  }

  public init(data?: any) {
    if (this._subscriber) {
      this._subscriber.unRegister();
    }
    this._data = new CommonData();
    this._api = new CommonApi();
    this._service = new CommonService();
    this._subscriber = new CommonSubscriber();
    this._route = new CommonRoute();
    this._viewModel = new CommonViewModel();
    this._config = new CommonConfig();

    // 模块初始化
    this._subscriber.register();
    this._route.init();
  }

  protected saveKey(): string {
    return this.constructor.name;
  }
}
