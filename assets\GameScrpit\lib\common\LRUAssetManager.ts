import {
  AssetManager,
  NodeEventType,
  Sprite,
  SpriteFrame,
  assetManager,
  isValid,
  Texture2D,
  Color,
  tween,
  Node,
} from "cc"; // 新增导入Texture2D

interface CacheItem {
  spriteFrame: SpriteFrame;
  lastUsed: number; // 最近使用时间戳
  // refCount: number; // 引用计数（用于多节点共享）
  memorySize: number; // 资源内存大小（字节）
  sprites: Set<Sprite>;
}

export class LRUAssetManager {
  private cache: Map<string, CacheItem> = new Map();
  private spriteCache: Map<string, string> = new Map();
  private memoryThreshold: number = 20 * 1024 * 1024; // 内存阈值（200MB）
  private totalMemory: number = 0; // 当前总内存占用（字节）
  // private usageQueue: string[] = [];

  private constructor() {}
  public static instance: LRUAssetManager = new LRUAssetManager();

  /**
   * 获取资源（优先从缓存读取）
   * @param bundleName 资源包名
   * @param path 资源路径
   * @param sprite 目标Sprite组件
   * @param callback 加载完成回调
   */
  async getSpriteFrame(bundleName: string, path: string, sprite: Sprite, callback?: Function, isAni: boolean = false) {
    const cacheKey = `${bundleName}/${path}`;
    const now = Date.now();

    // 1. 检查缓存是否存在
    const cachedItem = this.cache.get(cacheKey);
    if (cachedItem) {
      if (!cachedItem.sprites.has(sprite)) {
        cachedItem.sprites.add(sprite);
      }
      if (!isValid(cachedItem.spriteFrame)) {
        // 资源未加载完成
        return;
      }
      callback?.(cachedItem.spriteFrame);
      this.setSpriteFrame(cacheKey, sprite, cachedItem.spriteFrame, isAni, 0);

      return;
    }
    // 添加新资源并更新总内存（关键新增）
    let itemCache = {
      spriteFrame: null,
      lastUsed: now,
      memorySize: 0,
      sprites: new Set<Sprite>(),
    };
    itemCache.sprites.add(sprite);
    this.cache.set(cacheKey, itemCache);

    // 2. 直接调用AssetManager进行资源加载
    try {
      // 获取或加载bundle
      let bundle = assetManager.getBundle(bundleName);
      if (!bundle) {
        bundle = await new Promise<AssetManager.Bundle>((resolve, reject) => {
          assetManager.loadBundle(bundleName, (err, bundle) => {
            err ? reject(err) : resolve(bundle);
          });
        });
      }

      // 加载spriteFrame资源
      // 加载资源时计算内存大小
      // 加载spriteFrame资源时的内存计算优化
      bundle.load(`${path}/spriteFrame`, SpriteFrame, (err, asset) => {
        if (err) {
          console.warn("Sprite asset is invalid");
          this.cache.delete(cacheKey);
          return;
        }
        

        asset.addRef();
        // 动态计算纹理内存大小（关键优化）
        const texture = asset.texture;
        let bytesPerPixel = 4; // 默认RGBA8888格式
        if (texture) {
          // 根据实际像素格式调整字节数（示例常见格式）
          let pixelFormat = texture.getPixelFormat();
          switch (pixelFormat) {
            case Texture2D.PixelFormat.RGB565:
              bytesPerPixel = 2;
              break;
            case Texture2D.PixelFormat.RGBA4444:
              bytesPerPixel = 2;
              break;
            case Texture2D.PixelFormat.RGB_ETC1:
              bytesPerPixel = 0.5;
              break; // ETC1压缩格式每像素0.5字节
            // 可根据项目实际使用的格式扩展更多case
          }
        }

        const memorySize = asset.originalSize.width * asset.originalSize.height * bytesPerPixel || 0;
        //
        let cachekey = cacheKey;
        callback?.(asset);
        const validSprites = new Set<Sprite>();
        itemCache.sprites.forEach((sprite) => {
          if (isValid(sprite)) {
            validSprites.add(sprite);
            this.setSpriteFrame(cachekey, sprite, asset, isAni, memorySize);
            // 添加到缓存时更新总内存（关键新增）
          }
        });
        itemCache.spriteFrame = asset;
        itemCache.sprites = validSprites;
        if (itemCache.memorySize == 0) {
          itemCache.memorySize = memorySize;
          this.totalMemory += memorySize;
        }
      });
    } catch (error) {
      this.cache.delete(cacheKey);
      console.error("资源加载失败:", error);
      callback?.(null, error);
    }
  }
  private setSpriteFrame(cacheKey: string, sprite: Sprite, data: SpriteFrame, isAni: boolean, memorySizeAdd: number) {
    let cachedItem = this.cache.get(cacheKey);
    if (this.spriteCache.get(sprite.uuid) === cacheKey) {
      // 已经设置过
      return;
    } else if (this.spriteCache.get(sprite.uuid)) {
      let lastCacheItem = this.cache.get(this.spriteCache.get(sprite.uuid));
      lastCacheItem.sprites.delete(sprite);
    } else {
      sprite.node.once(NodeEventType.NODE_DESTROYED, (target) => {
        this.release(target, cacheKey, cachedItem.memorySize);
      });
    }

    this.spriteCache.set(sprite.uuid, cacheKey);

    // 内存超限时循环回收（关键修改）
    this.evictLeastUsed();

    // 过渡动画
    if (isAni) {
      let oc = new Color(sprite.color);
      let ocColor = new Color(oc.r, oc.g, oc.b, oc.a);
      let color = new Color(128, 128, 128, 128);
      tween(ocColor)
        .to(
          0.1,
          { a: color.a, r: color.r, g: color.g, b: color.b },
          {
            onUpdate: (target) => {
              isValid(sprite) && (sprite.color = target);
            },
          }
        )
        .call(() => {
          isValid(sprite) && (sprite.spriteFrame = data);
        })
        .to(
          0.1,
          { a: oc.a, r: oc.r, g: oc.g, b: oc.b },
          {
            onUpdate: (target) => {
              isValid(sprite) && (sprite.color = target);
            },
          }
        )
        .start();
    } else {
      sprite.spriteFrame = data;
    }
  }

  /**
   * 释放资源（同步更新内存统计）
   */
  private release(spriteNode: Node, cacheKey: string, memorySize: number) {
    const item = this.cache.get(cacheKey);
    if (!item) return;
    let sprite = spriteNode.getComponent(Sprite);
    item.lastUsed = Date.now();
    item.sprites.delete(sprite);
    this.spriteCache.delete(sprite.uuid);
  }

  /**
   * 淘汰最久未使用的资源（同步更新内存统计）
   */
  private evictLeastUsed() {
    // 循环释放直到内存达标或无可用资源
    // console.log("evictLeastUsed", this.totalMemory, this.memoryThreshold);
    let usageQueue = this.getSortedCacheKeys();
    while (this.totalMemory > this.memoryThreshold && usageQueue.length > 0) {
      const lruKey = usageQueue[0]; // 队首为最久未使用
      const item = this.cache.get(lruKey);

      if (!item) {
        continue;
      }

      // 若当前最久未使用资源仍有引用（refCount > 0），无法释放，终止循环
      if (item.sprites.size > 0) {
        console.log("释放的引用数不为0", this.totalMemory, this.memoryThreshold, this.cache);
        break;
      }

      // 执行资源释放
      this.cache.delete(lruKey);
      usageQueue.shift();
      this.totalMemory -= item.memorySize;
      item.spriteFrame.decRef();
      // console.log("资源释放", lruKey);
    }
  }

  private getSortedCacheKeys(): string[] {
    return Array.from(this.cache.keys()).sort((a, b) => {
      const itemA = this.cache.get(a);
      const itemB = this.cache.get(b);

      if (!itemA || !itemB) return 0;

      // 优先排列无精灵资源的
      const emptyA = itemA.sprites.size === 0 ? 1 : 0;
      const emptyB = itemB.sprites.size === 0 ? 1 : 0;

      // 主要排序条件：精灵数量为空优先
      if (emptyA !== emptyB) return emptyB - emptyA;

      // 次要排序条件：最后使用时间升序
      return itemA.lastUsed - itemB.lastUsed;
    });
  }
}
