import { _decorator, ccenum, EventTouch, isValid, Label, Node, NodeEventType, Sprite, v3, Vec3, Widget } from "cc";
import Formate from "../../lib/utils/Formate";
import { PlayerModule } from "../../module/player/PlayerModule";
import { FontColor } from "./FmConstant";
import ToolExt from "./ToolExt";
import MsgMgr from "../../lib/event/MsgMgr";
import MsgEnum from "../event/MsgEnum";
import { NodeTool } from "../../lib/utils/NodeTool";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import Logger, { LOG_LEVEL } from "../../lib/utils/Logger";
const log = Logger.getLoger(LOG_LEVEL.DEBUG);
const { ccclass, property } = _decorator;

/**
 * @zh
 * 数据类型。
 */
enum ItemCostType {
  /**
   * @zh
   * 显示 剩余/需要
   */
  RESIDUE_COST = 0,
  /**
   * @zh
   * 仅显示 需要
   */
  COST = 1,
  /**
   * @zh
   * 仅显示 剩余
   */
  RESIDUE = 2,

  /**
   * @zh
   * 自定义显示
   */
  CUSTOM = 3,
}
ccenum(ItemCostType);
@ccclass("ItemCost")
export class ItemCost extends BaseCtrl {
  @property(Sprite)
  bgItemIcon: Sprite;
  @property(Label)
  lblCost: Label;
  @property(Node)
  private checkBox: Node;
  @property({ type: ItemCostType })
  private type: ItemCostType = ItemCostType.RESIDUE_COST;

  private _isEnough: boolean = false;
  private _onChangeListener: Function;
  private _itemId: number;
  private _costNum: number;
  private _format: string = "";
  private _args: any[] = [];

  private originalPosition: Vec3 = v3(0, 0, 0);
  start() {
    MsgMgr.on(MsgEnum.ON_EXIST_ITEM_CHANGE, this.updateItem, this);
    MsgMgr.on(MsgEnum.ON_PLAYER_NUM_UPDATE, this.updateItem, this);
    this.originalPosition = this.node.position.clone();
    this.node.on(
      NodeEventType.SIZE_CHANGED,
      () => {
        if (isValid(this.node)) {
          let borderz_right = NodeTool.getBorderRight(this.node.parent);
          let node_right = NodeTool.getNodeRight(this.node);
          if (node_right > borderz_right) {
            let pos = this.node.position.clone();
            pos.x -= node_right - borderz_right;
            this.node.setPosition(pos);
          } else if (this.originalPosition.x != this.node.position.x) {
            let DeltaX = this.originalPosition.x - this.node.position.x;
            if (node_right + DeltaX > borderz_right) {
              let pos = this.node.position.clone();
              pos.x += borderz_right - node_right;
              this.node.setPosition(pos);
            } else {
              this.node.setPosition(this.originalPosition);
            }
          }
          this.node.getComponentsInChildren(Widget).forEach((widget) => {
            widget.updateAlignment();
          });
        }
      },
      this
    );
  }
  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_EXIST_ITEM_CHANGE, this.updateItem, this);
    MsgMgr.off(MsgEnum.ON_PLAYER_NUM_UPDATE, this.updateItem, this);
  }

  updateItem(itemId: number) {
    log.log("updateItem============", itemId, this.itemId);
    if (itemId == this._itemId) {
      this.setItemId(this._itemId, this._costNum);
    }
  }
  private onCheckBox(e: EventTouch) {
    this.checkBox.getChildByName("check").active = !this.checkBox.getChildByName("check").active;
    this._onChangeListener(this.checkBox.getChildByName("check").active);
  }
  /**
   * @zh
   * 自定义显示 %r为剩余 %c为自定义数字
   * @param id
   * @param format
   * @param args
   */
  public setCustom(id: number, format: string = "", ...args) {
    //
    this._format = format;
    this._args = args;
    this._itemId = id;

    let rs = format;
    for (let i = 0; i < args.length; i++) {
      rs = rs.replace("%c", args[i] || "");
    }
    let itemNum = PlayerModule.data.getItemNum(id);
    rs = rs.replace("%r", Formate.format(itemNum));
    this.lblCost.string = rs;
    ToolExt.setItemIcon(this.bgItemIcon.node, id);
  }

  public setItemId(id: number, costNum: number = 0) {
    this._itemId = id;
    let itemNum = 0;

    itemNum = PlayerModule.data.getItemNum(id);
    this._costNum = costNum;

    if (this.type == ItemCostType.RESIDUE_COST) {
      this.lblCost.string = `${Formate.format(itemNum)}/${Formate.format(costNum)}`;
      if (itemNum >= costNum) {
        this.lblCost.color = FontColor.GREEN_2;
        this._isEnough = true;
      } else {
        this.lblCost.color = FontColor.RED;
        this._isEnough = false;
      }
    } else if (this.type == ItemCostType.COST) {
      this.lblCost.string = `${Formate.format(costNum)}`;
    } else if (this.type == ItemCostType.RESIDUE) {
      this.lblCost.string = `${Formate.format(itemNum)}`;
    } else {
      this.setCustom(id, this._format, this._args);
    }

    ToolExt.setItemIcon(this.bgItemIcon.node, id);
  }
  public setOnCheckChange(callback: Function) {
    this._onChangeListener = callback;
  }

  public isEnough() {
    return isValid(this.node) && this.node.active && this._isEnough;
  }
  public get itemId() {
    return this._itemId;
  }
  public isCheck() {
    return this.checkBox.getChildByName("check").active;
  }
  public setCheck(check: boolean) {
    this.checkBox.getChildByName("check").active = check;
  }
}
