import { Asset, AssetManager, sp } from "cc";
import { Node } from "cc";
import { Sprite<PERSON>tlas } from "cc";
import { isValid } from "cc";
import { AudioClip } from "cc";
import { Prefab } from "cc";
import { Sprite<PERSON>rame } from "cc";
import { assetManager } from "cc";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { LRUAssetManager } from "../../GameScrpit/lib/common/LRUAssetManager";
const log = Logger.getLoger(LOG_LEVEL.WARN);
export enum BundleEnum {
  BUNDLE_INIT_LOGIN = "bundle_init_login",
  BUNDLE_PUB = "bundle_pub",
  BUNDLE_PUB_SOUND = "bundle_pub_sound",
  SCENE_LOADING = "scene_loading",
  SCENE_CREATE_PLAYER = "scene_create_player",
  BUNDLE_EXT_GUIDE = "bundle_ext_guide",

  /**内置分包优先级 8  */
  RESOURCES = "resources",

  /**优先级7=============公用资源 */

  BUNDLE_CITY_100 = "bundle_city_100",
  BUNDLE_CITY_101 = "bundle_city_101",
  BUNDLE_CITY_102 = "bundle_city_102",
  BUNDLE_CITY_103 = "bundle_city_103",
  BUNDLE_CITY_104 = "bundle_city_104",
  BUNDLE_CITY_105 = "bundle_city_105",
  BUNDLE_CITY_106 = "bundle_city_106",
  BUNDLE_CITY_107 = "bundle_city_107",
  BUNDLE_CITY_108 = "bundle_city_108",
  BUNDLE_CITY_109 = "bundle_city_109",
  BUNDLE_CITY_110 = "bundle_city_110",
  BUNDLE_CITY_111 = "bundle_city_111",
  BUNDLE_CITY_112 = "bundle_city_112",
  BUNDLE_CITY_113 = "bundle_city_113",
  BUNDLE_CITY_114 = "bundle_city_114",
  BUNDLE_CITY_115 = "bundle_city_115",

  /**文字资源 */
  BUNDLE_COMMON_FONT = "bundle_common_font",

  /**角色spine */
  BUNDLE_COMMON_ROLE = "bundle_common_role",

  /**仙友的公共资源 */
  BUNDLE_COMMON_FRIEND = "bundle_common_friend",

  /**英雄的公共资源 ---- 全身像*/
  BUNDLE_COMMON_HERO_FULL = "bundle_common_hero_full",

  /**英雄的公共资源 ---- 半身像*/
  BUNDLE_COMMON_HERO_HALF = "bundle_common_hero_half",

  /**英雄的公共资源 ---- 图标*/
  BUNDLE_COMMON_HERO_ICON = "bundle_common_hero_icon",

  /**道具图标，道具框 */
  BUNDLE_COMMON_ITEM = "bundle_common_item",

  /**灵宠公共资源 */
  BUNDLE_COMMON_PET = "bundle_common_pet",

  /**用户头像 ---- 公共资源 */
  BUNDLE_COMMON_PLAYERHEAD = "bundle_common_playerHead",

  /**公共的UI按钮图标资源 */
  BUNDLE_COMMON_UI = "bundle_common_ui",

  /**json配置文件 */
  BUNDLE_COMMON_JSON = "bundle_common_json",

  //=====优先级6 --- 初始化加载 --- 公共资源======

  /**通用窗口界面分包 */
  BUNDLE_G_COMMON_MAIN = "bundle_g_common_main",

  //======优先级5======
  /**主界面分包 */
  BUNDLE_G_MAINPAGE = "bundle_g_mainPage",

  /**主界面地图分包 */
  BUNDLE_G_GAME_MAP = "bundle_g_gameMap",

  /**主城据点分包 */
  BUNDLE_G_MAJORCITY = "bundle_g_majorCity",

  /**战斗分包 */
  BUNDLE_G_FIGHT = "bundle_g_fight",

  /**英雄分包 */
  BUNDLE_G_HERO = "bundle_g_hero",

  /**灵宠分包 */
  BUNDLE_G_PET = "bundle_g_pet",

  /**背包分包 */
  BUNDLE_G_KNAPSACK = "bundle_g_knapsack",

  /**主线任务分包 */
  BUNDLE_G_MAINTASK = "bundle_g_mainTask",

  /**玩家信息分包 */
  BUNDLE_G_PLAYER = "bundle_g_player",

  /**坐骑分包 */
  BUNDLE_G_HORSE = "bundle_g_horse",

  /**幸运夺宝分包 */
  BUNDLE_G_LOTTERY = "bundle_g_lottery",

  /**武魂分包 */
  BUNDLE_G_SOUL = "bundle_g_soul",

  /**仙友 */
  BUNDLE_G_FRIEND = "bundle_g_friend",

  /**弟子分包 */
  BUNDLE_G_PUPIL = "bundle_g_pupil",

  /**演武场 */
  BUNDLE_G_AZST = "bundle_g_azst",

  /**领地 */
  BUNDLE_G_TERRITORY = "bundle_g_territory",

  /**驿站 */
  BUNDLE_G_POST = "bundle_g_post",

  /**商城商店 */
  BUNDLE_G_SHOP = "bundle_g_shop",

  /**游历 */
  BUNDLE_G_TRAVEL = "bundle_g_travel",

  /**狩猎 */
  BUNDLE_G_HUNT = "bundle_g_hunt",

  /** 福地 */
  BUNDLE_G_FARM = "bundle_g_farm",

  /**邮件 */
  BUNDLE_G_MAIL = "bundle_g_mail",

  //仙友品质背景
  BUNDLE_COMMON_FRIENDICON = "bundle_common_friendIcon",

  //仙盟
  BUNDLE_G_CLUB = "bundle_g_club",

  /**图鉴 */
  BUNDLE_G_COLLECT = "bundle_g_collect",
}

const BundleDeps: Map<String, string[]> = new Map();

export class ResHelper {
  public static loadBundleDeps(nameList: string[], callback: Function) {
    let allCount = nameList.length;
    let completeCount = 0;
    for (let idx in nameList) {
      ResHelper.loadBundle(nameList[idx], (bundle) => {
        completeCount++;
        if (completeCount == allCount) {
          callback && callback();
        }
      });
    }
  }

  // 回调加载bundle
  public static loadBundle(name: string, callback: Function = null) {
    const bundleExist = assetManager.getBundle(name);
    if (!bundleExist) {
      assetManager.loadBundle(name, (err, bundle) => {
        if (err) {
          log.error(err);
          return;
        }

        // 加载依赖
        if (BundleDeps.get(name)) {
          ResHelper.loadBundleDeps(BundleDeps.get(name), () => {
            callback && callback(bundle);
          });
        } else {
          callback && callback(bundle);
        }
      });
    } else {
      if (BundleDeps.get(name)) {
        ResHelper.loadBundleDeps(BundleDeps.get(name), () => {
          callback && callback(bundleExist);
        });
      } else {
        callback && callback(bundleExist);
      }
    }
  }

  // 回调加载bundle
  public static preLoadBundle(name: string) {
    assetManager.loadBundle(name);
  }

  // 同步加载bundle
  public static async loadBundleSync(name: string): Promise<AssetManager.Bundle> {
    const bundleExist = assetManager.getBundle(name);
    if (!bundleExist) {
      return new Promise((resolve) => {
        assetManager.loadBundle(name, (err, bundle) => {
          if (err) {
            log.error(err);
            return;
          }
          resolve(bundle);
        });
      });
    } else {
      return bundleExist;
    }
  }

  public static async preLoadResSync(bundleName: string, assetUrl: string): Promise<any> {
    let bundle = await ResHelper.loadBundleSync(bundleName);
    return new Promise((resolve) => {
      bundle.preload(assetUrl, (err, asset) => {
        if (err) {
          log.error(err);
          return;
        }

        bundle.load(assetUrl, (err, asset) => {
          if (err) {
            log.error(err);
            return;
          }

          resolve(asset);
        });
      });
    });
  }
}

export class AssetMgr {
  // 动态加载的资源map
  private _resMap: Map<string, any> = new Map();

  public static create(): AssetMgr {
    return new AssetMgr();
  }

  // 加载图片
  public loadSpriteFrame(bundleName: string, assetUrl: string, callback: Function) {
    const key = bundleName + "?" + assetUrl;
    const asset: SpriteFrame = this._resMap.get(key);
    if (asset) {
      callback && callback(asset);
      return;
    }

    ResHelper.loadBundle(bundleName, (bundle: AssetManager.Bundle) => {
      bundle.load(assetUrl + "/spriteFrame", SpriteFrame, (err, asset) => {
        if (err) {
          log.error(err);
          callback && callback(null, err);
          return;
        }

        asset.addRef();
        this._resMap.set(key, asset);
        callback && callback(asset);
      });
    });
  }

  public async loadSpriteFrameSync(bundleName: string, assetUrl: string): Promise<SpriteFrame> {
    return new Promise((resolve) => {
      this.loadSpriteFrame(bundleName, assetUrl, (asset: SpriteFrame) => {
        resolve(asset);
      });
    });
  }

  // 加载图片集的子图片
  public loadSpriteFrameSub(bundleName: string, assetUrl: string, sub: string, callback: Function) {
    const key = bundleName + "?" + assetUrl;
    const asset: SpriteAtlas = this._resMap.get(key);
    if (asset) {
      var sf = asset.getSpriteFrame(sub);
      callback && callback(sf);
      return;
    }

    ResHelper.loadBundle(bundleName, (bundle: AssetManager.Bundle) => {
      bundle.load(assetUrl, SpriteAtlas, (err, spriteAtlas: SpriteAtlas) => {
        if (err) {
          log.error(err);
          return;
        }

        spriteAtlas.addRef();
        this._resMap.set(key, spriteAtlas);

        var sf = spriteAtlas.getSpriteFrame(sub);
        callback && callback(sf);
      });
    });
  }

  public async loadSpriteFrameSubSync(bundleName: string, assetUrl: string, sub: string): Promise<SpriteFrame> {
    return new Promise((resolve) => {
      this.loadSpriteFrameSub(bundleName, assetUrl, sub, (asset: SpriteFrame) => {
        resolve(asset);
      });
    });
  }

  public loadPrefab(bundleName: string, assetUrl: string, callback: Function) {
    const key = bundleName + "?" + assetUrl;
    const asset = this._resMap.get(key);
    if (asset) {
      callback && callback(asset);
      return;
    }

    ResHelper.loadBundle(bundleName, (bundle: AssetManager.Bundle) => {
      bundle.load(assetUrl, Prefab, (err, asset) => {
        if (err) {
          log.error(err);
          callback && callback(null, err);
          return;
        }

        asset.addRef();
        this._resMap.set(key, asset);
        callback && callback(asset);
      });
    });
  }

  public loadAny(bundleName: string, assetUrl: string, callback: Function) {
    const key = bundleName + "?" + assetUrl;
    const asset = this._resMap.get(key);
    if (asset) {
      callback && callback(asset);
      return;
    }

    ResHelper.loadBundle(bundleName, (bundle: AssetManager.Bundle) => {
      bundle.load(assetUrl, (err, asset) => {
        if (err) {
          log.error(err);
          callback && callback(null, err);
          return;
        }

        asset.addRef();
        this._resMap.set(key, asset);
        callback && callback(asset);
      });
    });
  }

  public loadMainAnySync(assetUrl: string) {
    const key = "main?" + assetUrl;
    const asset = this._resMap.get(key);
    if (asset) {
      return asset;
    }

    return new Promise((resolve, reject) => {
      assetManager.loadAny(assetUrl, (err, asset) => {
        if (err) {
          reject(err);
          return;
        }

        asset.addRef();
        this._resMap.set(assetUrl, asset);
        resolve(asset);
      });
    });
  }

  /**
   * 同步加载spine
   * @param bundleName bundle名称
   * @param assetUrl 资源路径
   * @returns
   */
  public async loadSpineSync(bundleName: string, assetUrl: string): Promise<sp.SkeletonData> {
    const key = bundleName + "?" + assetUrl;
    const asset = this._resMap.get(key);
    if (asset) {
      return asset;
    }

    return new Promise((resolve, reject) => {
      ResHelper.loadBundle(bundleName, (bundle: AssetManager.Bundle) => {
        bundle.load(assetUrl, sp.SkeletonData, (err, asset) => {
          if (err) {
            log.error(err);
            return;
          }

          asset.addRef();
          this._resMap.set(key, asset);
          resolve(asset);
        });
      });
    });
  }

  // 预加载
  public preloadAny(bundleName: string, assetUrl: string, callback?: Function) {
    const key = bundleName + "?" + assetUrl;
    const asset = this._resMap.get(key);
    if (asset) {
      callback && callback(asset);
      return;
    }

    ResHelper.loadBundle(bundleName, (bundle: AssetManager.Bundle) => {
      bundle.preload(assetUrl);
    });
  }

  /**
   * 同步加载的方法
   * @param bundleName bundle名
   * @param assetUrl 路径名
   * @returns
   */
  public loadPrefabSync(bundleName: string, assetUrl: string, dependence: string[] = []): Promise<Prefab> {
    return new Promise(async (resolve, reject) => {
      if (dependence?.length) {
        for (let i = 0; i < dependence.length; i++) {
          await ResHelper.loadBundleSync(dependence[i]);
        }
      }

      this.loadPrefab(bundleName, assetUrl, (asset: Prefab, err) => {
        if (err) {
          reject(err);
        }
        resolve(asset);
      });
    });
  }

  /**
   *
   * 同步加载音频
   *
   * @param bundleName bundle名
   * @param assetUrl 路径名
   * @returns
   */
  public loadAudioSync(bundleName: string, assetUrl: string) {
    return new Promise((resolve, reject) => {
      this.loadAudio(bundleName, assetUrl, (asset: AudioClip, err) => {
        if (err) {
          reject(err);
          return;
        }
        resolve(asset);
      });
    });
  }

  public loadAudio(bundleName: string, assetUrl: string, callback: Function) {
    const key = bundleName + "?" + assetUrl;
    const asset = this._resMap.get(key);
    if (asset) {
      callback && callback(asset);
      return;
    }

    ResHelper.loadBundle(bundleName, (bundle: AssetManager.Bundle) => {
      bundle.load(assetUrl, AudioClip, (err, asset) => {
        if (err) {
          log.error(err);
          callback && callback(null, err);
          return;
        }

        asset.addRef();
        this._resMap.set(key, asset);
        callback && callback(asset);
      });
    });
  }

  // 释放加载的资源
  public release() {
    this._resMap.forEach((value) => {
      value.decRef();
    });
    this._resMap.clear();
  }

  // 释放指定资源
  public releaseOne(bundleName: string, assetUrl: string) {
    const key = bundleName + "?" + assetUrl;
    const asset = this._resMap.get(key);
    if (asset) {
      this._resMap.delete(key);
      asset.decRef();
    }
  }
}
