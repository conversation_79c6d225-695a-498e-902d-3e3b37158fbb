import { Node } from "cc";
import { UINode } from "../../lib/ui/UINode";
import ToolExt from "../../game/common/ToolExt";
import { SoulModule } from "./SoulModule";
import { BadgeMgr, BadgeType } from "../../game/mgr/BadgeMgr";
import MsgMgr from "../../lib/event/MsgMgr";
import MsgEnum from "../../game/event/MsgEnum";
import { SoulPictureMessage } from "../../game/net/protocol/Soul";
import { JsonMgr } from "../../game/mgr/JsonMgr";
import { IConfigSoulPicture } from "../../game/JsonDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const PLANTNUM = 5;
export class SoulService {
  public init() {
    MsgMgr.off(MsgEnum.ON_SOUL_UPDATE, this.updatePopover, this);
    MsgMgr.off(MsgEnum.ON_SOUL_TUJIAN_UPDATE, this.updatePopover, this);

    MsgMgr.on(MsgEnum.ON_SOUL_UPDATE, this.updatePopover, this);
    MsgMgr.on(MsgEnum.ON_SOUL_TUJIAN_UPDATE, this.updatePopover, this);

    this.updatePopover();
  }

  public attrMap(attrMap: { [key: number]: number }) {
    let baseMap: { [key: number]: number } = {};
  }

  /**
   * 设置武魂静态图标
   * @param val node
   * @param soulId 模板id
   * @param self
   */
  public setSoulIconNode(val: Node, soulId: number, self?: UINode) {
    ToolExt.setItemIcon(val, soulId, self);
  }

  public updatePopover() {
    if (!SoulModule.data.warriorSoulManageMsg) {
      return;
    }
    // 基础配置
    let cfgBaseSoul = SoulModule.data.getConfigSoul();
    let show = SoulModule.data.warriorSoulManageMsg.freeCount < cfgBaseSoul.freeRefreshMax;
    BadgeMgr.instance.setShowById(BadgeType.UITerritory.btn_soul.btn_tab_soul.btn_get_soul.btn_free_refresh.id, show);

    // 图鉴红点配置pictureMap[方案数量][图鉴数量]
    let pictureDatas: IConfigSoulPicture[][] = [];
    Object.values(JsonMgr.instance.jsonList.c_soulPicture).forEach((item: any) => {
      // this._adapter.addItem(item);
      let i1 = Math.floor((item.id - 2001) / 1000);
      if (!pictureDatas[i1]) {
        pictureDatas[i1] = [];
      }
      pictureDatas[i1].push(item);
    });
    let planList = SoulModule.data.warriorSoulManageMsg.planList; //[this._planIndex].pictureMap[this.position];
    // log.log("planList", planList);

    for (let plant_index = 0; plant_index < PLANTNUM && plant_index < planList?.length; plant_index++) {
      // 所有拥有的武魂
      let soulSet = SoulModule.data.warriorSoulManageMsg.soulHoldSet;
      // 遍历方案
      for (let tujian_index = 0; tujian_index < pictureDatas[plant_index].length; tujian_index++) {
        let collectNum = 0;
        // 遍历图鉴
        for (let soul_index = 0; soul_index < pictureDatas[plant_index][tujian_index].soulId.length; soul_index++) {
          // 查找图鉴中的武魂是否包含在拥有的武魂列表中
          let owned = soulSet.find((soul) => {
            return soul == pictureDatas[plant_index][tujian_index].soulId[soul_index];
          });
          // let owned = (soulSet[pictureDatas[plant_index][tujian_index].soulId[soul_index]] ?? 0) > plant_index;
          if (owned) {
            collectNum++;
          }
        }
        let isActive = collectNum == pictureDatas[plant_index][tujian_index].soulId.length;
        // 图鉴已收集完成但未激活，激活的图鉴对应的index会存储在pictureMap中
        if (!planList[plant_index].pictureMap[tujian_index] && isActive) {
          // log.log("未激活的图鉴", plant_index, tujian_index);
          BadgeMgr.instance.setShowById(
            BadgeType.UITerritory.btn_soul.btn_tab_tujian[`tab${plant_index + 1}`].id,
            true
          );
          return;
        } else {
          // 已激活的图鉴不需要红点
          BadgeMgr.instance.setShowById(
            BadgeType.UITerritory.btn_soul.btn_tab_tujian[`tab${plant_index + 1}`].id,
            false
          );
        }
      }
    }
  }
}
