import {
  _decorator,
  CCInteger,
  Component,
  EventTouch,
  Input,
  Layout,
  Mask,
  Node,
  tween,
  UITransform,
  Vec3,
  Widget,
} from "cc";
import { ListAdapter } from "../../../platform/src/core/ui/adapter_view/ListAdapter";
import { Deque } from "../../../platform/src/lib/utils/Deque";
import { RecyclableAdapterView } from "./RecyclableAdapterView";
const { ccclass, property } = _decorator;

const rebound_time = 150;

@ccclass("ListView")
export class ListView extends RecyclableAdapterView {
  layout(node: Node, left: number, top: number) {
    let width = node.getComponent(UITransform).width;
    let height = node.getComponent(UITransform).height;
    let anchorX = node.getComponent(UITransform).anchorX;
    let anchorY = node.getComponent(UITransform).anchorY;
    // node.getComponent(Layout)?.destroy();
    let x: number = left + anchorX * width;
    let y = top - height * (1 - anchorY);
    node.setPosition(new Vec3(x, y));
  }
}
