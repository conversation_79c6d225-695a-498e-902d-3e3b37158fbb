import { _decorator, find, Label, Node, Sprite } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { HuntPlayerMessage } from "../../../net/protocol/Hunt";
import { BundleEnum } from "../../../bundleEnum/BundleEnum";
import { PlayerModule } from "db://assets/GameScrpit/module/player/PlayerModule";
import ToolExt from "../../../common/ToolExt";
import FmUtils from "db://assets/GameScrpit/lib/utils/FmUtils";
import { AssetMgr } from "db://assets/platform/src/ResHelper";
const { ccclass, property } = _decorator;

@ccclass("HuntRankViewHolder")
export class HuntRankViewHolder extends ViewHolder {
  // private _assetMgr: AssetMgr = null;

  private _spr_rank: Node;
  private _spr_lab_rank_bg: Node;

  private _lab_rank: Label;

  private _lab_Name: Label;
  private _lab_level: Label;
  private _lab_score: Label;
  private _btn_header: Node;

  public init() {
    this._spr_rank = find("spr_rank", this.node);
    this._spr_lab_rank_bg = find("spr_lab_rank_bg", this.node);
    this._lab_rank = find("spr_lab_rank_bg/lab_rank", this.node).getComponent(Label);

    this._lab_Name = find("lab_Name", this.node).getComponent(Label);
    this._lab_level = find("lab_level", this.node).getComponent(Label);
    this._lab_score = find("lab_score", this.node).getComponent(Label);
    this._btn_header = find("btn_header", this.node);
  }
  updateData(data: HuntPlayerMessage, position: number) {
    this.setRank(position + 1);
    this.setMessage(data);
  }

  private async setRank(rank: number) {
    if (rank <= 3) {
      this._spr_rank.active = true;
      this._spr_lab_rank_bg.active = false;
      let spf = await this.assetMgr.loadSpriteFrameSync(
        `${BundleEnum.BUNDLE_COMMON_UI}`,
        `atlas_imgs/YWC_icon_paiming${rank}`
      );
      if (spf) {
        this._spr_rank.getComponent(Sprite).getComponent(Sprite).spriteFrame = spf;
      }
    } else {
      this._spr_rank.active = false;
      this._spr_lab_rank_bg.active = true;
      this._lab_rank.string = String(rank);
    }
  }

  private setMessage(data: HuntPlayerMessage) {
    this._lab_Name.string = data.playerSimpleMessage.nickname;

    let db = PlayerModule.data.getConfigLeaderData(data.playerSimpleMessage.level);
    this._lab_level.getComponent(Label).string = db.name;
    this._lab_score.getComponent(Label).string = String(data.point);

    let info = ToolExt.newPlayerBaseMessage();
    info.avatarList = data.playerSimpleMessage.avatarList;
    info.userId = data.playerSimpleMessage.userId;
    info.nickname = data.playerSimpleMessage.nickname;
    info.sex = data.playerSimpleMessage.sex;
    FmUtils.setHeaderNode(this._btn_header, info);
  }
}
