import { _decorator, Component, Node } from "cc";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { BaseCtrl } from "db://assets/platform/src/core/BaseCtrl";
import { RouteManager } from "db://assets/platform/src/core/managers/RouteManager";
import { routeConfig } from "db://assets/platform/src/core/managers/RouteTableManager";
import { UIExchangeShop } from "../../../../common/src/prefab/ui/UIExchangeShop";
import { UIDailyChallengeAward } from "./UIDailyChallengeAward";
import { UIDailyChallengeRank } from "./UIDailyChallengeRank";
import { ShopTypeEnum } from "../../../../common/src/CommonConfig";
const { ccclass, property } = _decorator;

@ccclass("UIDailyChallenge")
@routeConfig({
  bundle: BundleEnum.BUNDLE_DAILY_CHALLENGE,
  url: "prefab/ui/UIDailyChallenge",
  nextHop: [],
  exit: "dialog_close",
})
export class UIDailyChallenge extends BaseCtrl {
  public playShowAni: boolean = true;
  start() {
    super.start();
  }

  update(deltaTime: number) {}

  private on_click_btn_paihang() {
    // RouteTableManager.instance.push(BundleEnum.BUNDLE_G_MAIN_RANK, "prefab/ui/UILeaderboard");
    RouteManager.uiRouteCtrl.showRoute(UIDailyChallengeRank);
  }
  private on_click_btn_jiangli() {
    RouteManager.uiRouteCtrl.showRoute(UIDailyChallengeAward);
  }
  private on_click_btn_shangdian() {
    RouteManager.uiRouteCtrl.showRoute(UIExchangeShop, { payload: { type: ShopTypeEnum.每日挑战商店 } });
  }
}
