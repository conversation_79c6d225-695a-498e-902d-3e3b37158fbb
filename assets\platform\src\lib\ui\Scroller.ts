import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils/Logger";

const log = Logger.getLoger(LOG_LEVEL.STOP);
const max_speed = 4;
const rebound_time = 300;

export class Scroller {
  private a_x = 0.001;
  private a_y = 0.001;
  private rebound_a_y = 0;
  private rebound_a_x = 0;
  private yv0 = 0;
  private xv0 = 0;
  private t0 = 0;
  private _quickStop = false;

  private _onFlingComplete: () => void;
  private _onFling: (xoffset: number, yoffset: number) => void;
  private _onScrollComplete: () => void;
  // private _onReb
  private _scrollIntervalId: number;
  public init() {
    this._scrollIntervalId = setInterval(() => {
      this.update();
    }, 1000 / 60);
  }
  public deInit() {
    clearInterval(this._scrollIntervalId);
  }
  public update() {
    if (this.yv0 == 0 && this.xv0 == 0) {
      return;
    }
    // log.log(`update ${this.yv0} ${this.xv0}`);
    if (this.rebound_a_x == 0 && this.rebound_a_y == 0) {
      // let startTime = Date.now();
      this.fling();
      // log.log(`duration: ${Date.now() - startTime}`);
    } else {
      this.rebound();
    }
  }
  private rebound() {
    let deltaX = 0;
    let deltaY = 0;
    let t = Date.now() - this.t0;
    if (this.yv0 != 0) {
      deltaY = this.yv0 * t - 0.5 * this.rebound_a_y * t * t;
      this.yv0 = this.yv0 - this.rebound_a_y * t;
      if ((this.yv0 <= 0 && this.rebound_a_y) > 0 || (this.yv0 >= 0 && this.rebound_a_y < 0)) {
        this.yv0 = 0;
        this.rebound_a_y = 0;
      }
    } else {
      this.rebound_a_y = 0;
    }
    if (this.xv0 != 0) {
      deltaX = this.xv0 * t - 0.5 * this.rebound_a_x * t * t;
      this.xv0 = this.xv0 - this.rebound_a_x * t;
      if ((this.xv0 <= 0 && this.rebound_a_x) > 0 || (this.xv0 >= 0 && this.rebound_a_x < 0)) {
        this.xv0 = 0;
        this.rebound_a_x = 0;
      }
    }
    this.t0 = Date.now();
    this._onFling?.(deltaX, deltaY);
    if (this.yv0 == 0 && this.xv0 == 0) {
      this._onScrollComplete?.();
      this._quickStop = false;
    }
  }
  private fling() {
    let deltaX = 0;
    let deltaY = 0;
    let a_y = this.a_y;
    let a_x = this.a_x;
    if (this._quickStop) {
      a_x = 0.05;
      a_y = 0.05;
    }
    let t = Date.now() - this.t0;
    if (this.yv0 > 0) {
      let s = this.yv0 * t - 0.5 * a_y * t * t;
      this.yv0 = this.yv0 - a_y * t;
      if (this.yv0 <= 0) {
        this.yv0 = 0;
      }
      deltaY = s;
    } else if (this.yv0 < 0) {
      let s = this.yv0 * t + 0.5 * a_y * t * t;
      this.yv0 = this.yv0 + a_y * t;
      if (this.yv0 >= 0) {
        this.yv0 = 0;
      }
      deltaY = s;
    }
    if (this.xv0 > 0) {
      let s = this.xv0 * t - 0.5 * a_x * t * t;
      this.xv0 = this.xv0 - a_x * t;
      if (this.xv0 <= 0) {
        this.xv0 = 0;
      }
      deltaX = s;
    } else if (this.xv0 < 0) {
      let s = this.xv0 * t + 0.5 * a_x * t * t;
      this.xv0 = this.xv0 + a_x * t;
      if (this.xv0 >= 0) {
        this.xv0 = 0;
      }
      deltaX = s;
    }
    this.t0 = Date.now();

    this._onFling?.(deltaX, deltaY);
    log.log(`deltaY ${deltaY} deltaX ${deltaX} - ${t}`);
    if (this.yv0 == 0 && this.xv0 == 0) {
      this._onFlingComplete?.();
      if (this._quickStop) {
        this._quickStop = false;
      }
    }
  }
  public setFlings(xSpeed: number, ySpeed: number) {
    log.log(`setFling [${xSpeed} , ${ySpeed}] [${this.rebound_a_x} , ${this.rebound_a_y}]`);
    //fling的时候需要重置（中断）回弹状态
    this.rebound_a_y = 0;
    this.rebound_a_x = 0;
    this.yv0 = Math.max(-max_speed, Math.min(ySpeed, max_speed));
    this.xv0 = Math.max(-max_speed, Math.min(xSpeed, max_speed));
    this.t0 = Date.now();
    if (this.yv0 == 0 && this.xv0 == 0) {
      this._onFlingComplete?.();
    }
    // this.xv0 = xSpeed;
    // this.yv0 = ySpeed;
  }
  public setQuickStop() {
    this._quickStop = true;
  }
  public reset() {
    this.yv0 = 0;
    this.xv0 = 0;
  }
  public scrollTo(xDistance: number, yDistance: number, force: boolean = false, reboundTime: number = rebound_time) {
    if (!force && (this.yv0 != 0 || this.xv0 != 0)) {
      //必须执行完上一次滚动
      log.log("必须执行完上一次滚动", this.yv0, this.xv0);
      return;
    }
    //先置0再重新算
    this.yv0 = 0;
    this.xv0 = 0;
    this._quickStop = false;
    this.yv0 = (yDistance * 2) / reboundTime;
    this.rebound_a_y = (yDistance * 2) / (reboundTime * reboundTime);
    this.xv0 = (xDistance * 2) / reboundTime;
    this.rebound_a_x = (xDistance * 2) / (reboundTime * reboundTime);
    this.t0 = Date.now();
  }
  public set onFlingComplete(onFlingComplete: () => void) {
    this._onFlingComplete = onFlingComplete;
  }
  public set onFling(onFling: (xoffset: number, yoffset: number) => void) {
    this._onFling = onFling;
  }
  public set onScrollComplete(onScrollComplete: () => void) {
    this._onScrollComplete = onScrollComplete;
  }
  /**
   * 阻尼设置默认0.001
   */
  public set damping(damping: number) {
    this.a_y = damping;
    this.a_x = damping;
  }
}
