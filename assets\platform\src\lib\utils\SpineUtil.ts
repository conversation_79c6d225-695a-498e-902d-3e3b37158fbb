import { isValid, sp } from "cc";
import { Sleep } from "../../../../GameScrpit/game/GameDefine";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
const log = Logger.getLoger(LOG_LEVEL.WARN);
/**
 * spine工具类
 */
export class SpineUtil {
  /**
   * 获取动画时长
   *
   * @param spine spine组件
   * @param aniName 动画名
   * @returns
   */
  public static getSpineDuration(spine: sp.Skeleton, aniName: string) {
    let animation = spine.findAnimation(aniName);
    if (!animation) {
      log.error("找不到动画", `spine动画名:${spine.skeletonData.name} 动画名:${aniName}`);
      return 0;
    }

    return animation.duration;
  }

  /**
   * 按顺序播放动画
   *
   * @param spine 动画组件
   * @param nameList 动画列表
   */
  public static async playOneByOne(spine: sp.Skeleton, ...nameList: string[]) {
    spine.node.active = true;
    for (let i = 0; i < nameList.length; i++) {
      let name = nameList[i];
      if (!isValid(spine.node)) {
        log.error("spine组件无效");
        return;
      }

      if (!spine.findAnimation(name)) {
        log.error("找不到动画", `spine动画名:${spine.skeletonData.name} 动画名:${name}`);
        continue;
      }

      if (i == nameList.length - 1) {
        spine.setAnimation(0, name, true);
      } else {
        spine.setAnimation(0, name, false);
      }

      let durning = this.getSpineDuration(spine, name);
      await Sleep(durning);
    }
  }

  /**
   * 播放动画
   *
   * @param spine 动画组件
   * @param name 动画名
   * @param loop 是否循环
   */
  public static async playSpine(spine: sp.Skeleton, name: string, loop: boolean = true): Promise<sp.spine.TrackEntry> {
    if (!isValid(spine.node)) {
      log.error("spine组件无效");
      return;
    }

    if (!spine.node.active) {
      // 如果未激活，下一帧在查询动画是否存在
      await Sleep(0.01);
      spine.node.active = true;
    }

    const rs = spine.setAnimation(0, name, loop);
    if (rs && rs.timeScale == 0) {
      rs.timeScale = 1;
    }

    if (!spine.findAnimation(name)) {
      log.warn("找不到动画", `spine动画名:${spine.skeletonData.name} 动画名:${name}`);
      return;
    }

    return rs;
  }

  /**
   * 让动画停在指定时间
   * @param spine 动画组件
   * @param name 动画名称
   * @param time 停在指定时间
   */
  public static async stopAtFrame(spine: sp.Skeleton, name: string, time: number) {
    const te = await SpineUtil.playSpine(spine, name, false);
    if (!te) {
      return;
    }

    // 对time作限制
    time = time % te.animation.duration;

    te.timeScale = 0; // 让动画停止
    te.trackTime = time;
  }

  /**
   * 上一个动画播放完成后播放下一个动画
   * @param spine 动画组件
   * @param name 动画名称
   * @param loop 是否循环
   * @returns
   */
  public static completePlay(spine: sp.Skeleton, name: string, loop: boolean = true) {
    if (!isValid(spine.node)) {
      log.error("spine组件无效");
      return;
    }

    if (!spine.node.active) {
      spine.node.active = true;
      spine.setAnimation(0, name, loop);
      spine.setCompleteListener(null);
      return;
    }

    if (!spine.animation) {
      spine.setAnimation(0, name, loop);
      spine.setCompleteListener(null);
      return;
    }

    if (!spine.getCurrent(0).isComplete) {
      spine.setAnimation(0, name, loop);
      spine.setCompleteListener(null);
      return;
    }

    spine.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
      spine.setCompleteListener(null);
      spine.setAnimation(0, name, loop);
    });
  }

  public static playToLastFrame(spine: sp.Skeleton, aniName: string) {
    // 查找动画
    let animation = spine.findAnimation(aniName);
    if (!animation) {
      log.error("找不到动画", `spine动画名:${spine.skeletonData.name} 动画名:${aniName}`);
      return;
    }

    // 获取动画总时长
    const duration = animation.duration;
    // 设置动画到指定轨道并播放到最后一帧
    const trackEntry = spine.setAnimation(0, aniName, false);
    if (trackEntry) {
      trackEntry.trackTime = duration;
    }
  }
}
