import { instantiate, Node, _decorator, Label } from "cc";

import { JsonMgr } from "../../../game/mgr/JsonMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import { TimeTaskModule } from "../TimeTaskModule";
import GuideMgr from "../../../ext_guide/GuideMgr";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../../game/event/MsgEnum";
import { ActivityTakeResponse } from "../../../game/net/protocol/Activity";
import { ListAdapter, ViewHolder } from "db://assets/platform/src/core/ui/adapter_view/ListAdapter";
const { ccclass, property } = _decorator;

@ccclass("TimeTaskViewHolder")
export class TimeTaskViewHolder extends ViewHolder {
  private _taskId: number;
  private _index: number;
  private _taskTypeId: number;
  private _data: number[];
  private _data2: number;
  public updateData(data: number[], data2: number, taskId: number, taskTypeId: number) {
    //
    this._taskId = taskId;
    this._data = data;
    this._data2 = data2;
    this._taskTypeId = taskTypeId;
    this._index = this.position;
    let task = JsonMgr.instance.jsonList.c_task[taskTypeId];
    this.getNode("lbl_title").getComponent(Label).string = task.des.replace("s%", data2 + "");
    for (let i = 0; i < data.length && data.length > 1; i += 2) {
      let item = this.getNode("node_items").children[i / 2];
      if (!item) {
        item = instantiate(this.getNode("node_items").children[0]);
        this.getNode("node_items").addChild(item);
        item.active = true;
      }
      FmUtils.setItemNode(item, data[i], data[i + 1]);
    }
    let isComplete = TimeTaskModule.data.timeTaskMessage.completeMap[taskId].targetVal > data2;
    let isGet = TimeTaskModule.data.timeTaskMessage.completeMap[taskId].takeList.includes(this.position);
    this.getNode("btn_get").active = isComplete && !isGet;
    this.getNode("btn_go").active = !isComplete;
    this.getNode("btn_yilingqu").active = isGet && isComplete;
  }
  onClickGo() {
    //
    let task = JsonMgr.instance.jsonList.c_task[this._taskTypeId];
    GuideMgr.startGuide({
      stepId: task.guideType,
    });
  }
  onClickGet() {
    //
    TimeTaskModule.api.takeTimeTask(this._taskId, this._index, (data: ActivityTakeResponse) => {
      this.updateData(this._data, this._data2, this._taskId, this._taskTypeId);
      MsgMgr.emit(MsgEnum.ON_GET_AWARD, { itemList: data.rewardList });
    });
  }
}

export class TimeTaskAdapter extends ListAdapter {
  private _item: Node;
  private _data: number[][] = [];
  private _data2: number[] = [];
  private _taskId: number;
  private _taskTypeId: number;
  constructor(item: Node) {
    super();
    this._item = item;
  }
  setData(data: number[][], data2: number[], taskId: number, taskTypeId: number) {
    this._taskTypeId = taskTypeId;
    this._taskId = taskId;
    this._data = data;
    this._data2 = data2;
    this.notifyDataSetChanged();
  }
  onCreateView(viewType: number): Node {
    let item = instantiate(this._item);
    item.active = true;
    return item;
  }
  onBindData(node: Node, position: number): void {
    node
      .getComponent(TimeTaskViewHolder)
      .updateData(this._data[position], this._data2[position], this._taskId, this._taskTypeId);
  }
  getCount(): number {
    return this._data.length;
  }
}
