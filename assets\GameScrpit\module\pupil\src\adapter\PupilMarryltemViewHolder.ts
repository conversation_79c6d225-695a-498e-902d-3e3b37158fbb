import { _decorator, Node, Label } from "cc";
import { ViewHolder } from "../../../../../platform/src/core/ui/adapter_view/ListAdapter";
import { PupilMarryReqMessage, PupilMarryRequest, PupilMessage } from "../../../../game/net/protocol/Pupil";
import { Sprite } from "cc";
import { PupilModule } from "../PupilModule";
import { Layout } from "cc";

import FmUtils from "../../../../lib/utils/FmUtils";
import { UIMgr } from "../../../../lib/ui/UIMgr";
import { PupilRouteName } from "../PupilRoute";
import Formate from "../../../../lib/utils/Formate";
import { PupilAni } from "../prefab/ui/PupilAni";
import { AssetMgr } from "../../../../../platform/src/ResHelper";
import { BundleEnum } from "../../../../game/bundleEnum/BundleEnum";
import { SpriteFrame } from "cc";
import { PupilAudioName } from "db://assets/GameScrpit/module/pupil/src/PupilConstant";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import ResMgr from "db://assets/GameScrpit/lib/common/ResMgr";

const { ccclass, property } = _decorator;
@ccclass("PupilMarryltemViewHolder")
export class PupilMarryltemViewHolder extends ViewHolder {
  @property(Sprite)
  private spZz: Sprite;

  @property(Label)
  private lblName: Label;

  @property(Sprite)
  private bgAttrNew: Sprite;

  @property(Label)
  private lblAttrNew: Label;

  @property(Label)
  private lblFrom: Label;

  @property(Sprite)
  private bgRewardItem: Sprite;

  @property(Label)
  private lblRewardNum: Label;

  @property(Layout)
  private layoutAttr: Layout;

  private _pupilMarryReqMessage: PupilMarryReqMessage;

  private _selfPupilId: number;

  private _channelId: number;

  public init() {}

  public updateData(position: number, args: any) {
    this._pupilMarryReqMessage = args.marryReq;
    let pupilMsg = this._pupilMarryReqMessage.pupil;

    this._selfPupilId = args.pupilId;

    this._channelId = args.channelId;

    let config = PupilModule.data.getConfigPupil(pupilMsg.ownInfo.talentId);

    // 设置形象
    this.node
      .getChildByPath("dz_bg_touxiangkuang/PupilAni")
      .getComponent(PupilAni)
      .setAniByNameId(pupilMsg.ownInfo.nameId);

    // 设置天资属性
    this.assetMgr.loadSpriteFrame(BundleEnum.BUNDLE_G_PUPIL, `images/${config.talentBg}`, (spf: SpriteFrame) => {
      this.spZz.spriteFrame = spf;
    });
    ResMgr.setSpriteFrame(BundleEnum.BUNDLE_G_PUPIL, `images/${config.talentBg}`, this.spZz);

    // 弟子姓名
    this.lblName.string = PupilModule.service.getPupilName(pupilMsg.ownInfo.nameId);

    // 归属
    this.lblFrom.string = `归属：${pupilMsg.ownInfo.userName}`;

    // 天资属性背景
    PupilModule.service.setPupilAdultAttrBg(this.bgAttrNew, pupilMsg.ownInfo.talentId);

    // 天资属性值
    this.lblAttrNew.string = Formate.formatAttribute(
      pupilMsg.ownInfo.adultAttrList[0],
      pupilMsg.ownInfo.adultAttrList[1]
    );

    // 普通属性节点设置
    PupilModule.service.setLayoutAttr(this.layoutAttr, pupilMsg.ownInfo.initAttrList);

    // 设置道具ICON
    FmUtils.setItemIcon(this.bgRewardItem.node, config.rewardList[0]);

    // 设置奖励道具数量
    this.lblRewardNum.string = `${config.rewardList[1]}`;
  }

  public onBtnMarry() {
    AudioMgr.instance.playEffect(PupilAudioName.Effect.点击结伴按钮);
    let data: PupilMarryRequest = {
      pupilId: this._selfPupilId,
      marryId: this._pupilMarryReqMessage.id,
      channelId: this._channelId,
    };

    PupilModule.api.marry(data, (resp) => {
      UIMgr.instance.replaceDialog(PupilRouteName.UIPupilMarryResPop, { pupilMsg: resp });
      PupilModule.api.marryShow(this._selfPupilId);
    });
  }
}
