import { _decorator, Color, EventTouch, isValid, Label, math, Node, Sprite, UITransform } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { UIMgr } from "../../../lib/ui/UIMgr";
import ResMgr from "../../../lib/common/ResMgr";
import { FriendCitySkillMessage, FriendHeroSkillResponse, FriendMessage } from "../../net/protocol/Friend";
import Formate from "../../../lib/utils/Formate";
import { divide } from "../../../lib/utils/NumbersUtils";
import { FriendModule } from "../../../module/friend/FriendModule";
import { HeroModule } from "../../../module/hero/HeroModule";
import TipMgr from "../../../lib/tips/TipMgr";
import { ScrollableView } from "../../../../platform/src/core/ui/components/ScrollableView";
import { ItemCost } from "../../common/ItemCost";
import { ItemEnum } from "../../../lib/common/ItemEnum";
import { FriendCityAdapter } from "./adapter/FriendCityAdapter";
import { ExpandScrollView } from "../../../../platform/src/core/ui/components/ExpandScrollView";
import MsgMgr from "../../../lib/event/MsgMgr";
import MsgEnum from "../../event/MsgEnum";
import { AudioMgr, AudioName } from "db://assets/platform/src/AudioHelper";
import { FriendAudioName } from "../../../module/friend/FriendConfig";
import { PlayerRouteName } from "../../../module/player/PlayerConstant";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { FmButton } from "../../../../platform/src/core/ui/components/FmButton";
const log = Logger.getLoger(LOG_LEVEL.WARN);
const { ccclass, property } = _decorator;

/**
 *
 * ivan_huang
 * Mon Jun 24 2024 11:15:26 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/friend/UIFriendSkillUpgrade.ts
 *
 */
const redColor: string = "#FF5244";
const greencolor: string = "#6CCA87";
@ccclass("UIFriendSkillUpgrade")
export class UIFriendSkillUpgrade extends UINode {
  protected _openAct: boolean = true;
  private _cityImprove: Node;
  private _cityType1Add: Label;
  private _cityType2Add: Label;
  private _cityType3Add: Label;
  private _cityType4Add: Label;
  private _cityType5Add: Label;
  private _cityContent: Node;
  private _skillContent: Node;

  private _heroImprove: Node;
  private _heroContent: Node;
  private _skill1Cost: ItemCost;
  private _skill2Cost: ItemCost;

  private _subTitle: Label;
  private _tabHero: Node;
  private _tabCity: Node;

  private _lastItem: Node;
  private _currentExpand: Node;
  private _curPos: number = -1;

  private _friendIds: number[];
  private _index: number;
  private _indexSkillItem: number = 0;

  private _cityAdapter: FriendCityAdapter;

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_FRIEND}?prefab/ui/UIFriendSkillUpgrade`;
  }
  //=================================================
  public init(args: any): void {
    super.init(args);
    this._friendIds = args[0];
    this._index = args[1];
  }

  private refreshCityData() {
    let attrAdds = FriendModule.data.getFriendCityAttrAdds(this._friendIds[this._index]);
    this._cityType1Add.string = `+${attrAdds[1]}%`;
    this._cityType2Add.string = `+${attrAdds[2]}%`;
    this._cityType3Add.string = `+${attrAdds[3]}%`;
    this._cityType4Add.string = `+${attrAdds[4]}%`;
    this._cityType5Add.string = `+${attrAdds[5]}%`;
  }
  protected onRegEvent(): void {
    MsgMgr.on(MsgEnum.ON_FRIEND_CITY_SKILL_UPDATE, this.refreshCityData, this);
  }
  protected onDelEvent(): void {
    MsgMgr.off(MsgEnum.ON_FRIEND_CITY_SKILL_UPDATE, this.refreshCityData, this);
  }
  protected onEvtShow(): void {
    super.onEvtShow();
    this._cityImprove = this.getNode("city");
    this._heroImprove = this.getNode("hero");
    this._cityContent = this.getNode("city_content");
    this._heroContent = this.getNode("hero_content");
    this._tabHero = this.getNode("tab_hero");
    this._tabCity = this.getNode("tab_city");
    this._subTitle = this.getNode("subtitle").getComponent(Label);
    this._skillContent = this.getNode("content");
    this._cityType1Add = this.getNode("type_1_add").getComponent(Label);
    this._cityType2Add = this.getNode("type_2_add").getComponent(Label);
    this._cityType3Add = this.getNode("type_3_add").getComponent(Label);
    this._cityType4Add = this.getNode("type_4_add").getComponent(Label);
    this._cityType5Add = this.getNode("type_5_add").getComponent(Label);
    this._skill1Cost = this.getNode("skill1_cost").getComponent(ItemCost);
    this._skill2Cost = this.getNode("skill2_cost").getComponent(ItemCost);

    this._cityAdapter = new FriendCityAdapter(
      this.getNode("UIFriendSkillUpgradeItem"),
      this.getNode("UIFriendSkillUpgradeExpand"),
      this.getNode("node_friend_nextskill")
    );
    let width = this.getNode("UIFriendSkillUpgradeItem").getComponent(UITransform).width;
    this.getNode("skill_list").getComponent(ExpandScrollView).setAdapter(this._cityAdapter);
    this.getNode("skill_list").getComponent(ExpandScrollView).setColumnWidth(width);

    this.refreshCity();

    this.registerBadgeNode();
  }
  private registerBadgeNode() {
    FriendModule.service.registerFriendSkillCityBadge(this.getNode("tab_city"), this._friendIds[this._index]);
    FriendModule.service.registerFriendSkillHeroBadge(this.getNode("tab_hero"), this._friendIds[this._index]);
    FriendModule.service.registerFriendSkillHeroFixBadge(this.getNode("skill1_btn_up"), this._friendIds[this._index]);
    FriendModule.service.registerFriendSkillHeroPercentBadge(
      this.getNode("skill2_btn_up"),
      this._friendIds[this._index]
    );
  }
  private on_click_close() {
    AudioMgr.instance.playEffect(AudioName.Effect.关闭窗口);
    UIMgr.instance.back();
  }
  private refreshCity() {
    this._tabCity.getComponent(FmButton).selected = true;
    this._tabHero.getComponent(FmButton).selected = false;
    this._cityContent.active = true;
    this._cityImprove.active = true;
    this._heroContent.active = false;
    this._heroImprove.active = false;
    this._subTitle.string = `建筑技能`;
    let friendInfo = FriendModule.config.getFriendById(this._friendIds[this._index]);
    this.getNode("lbl_dialog_subtitme").getComponent(Label).string = `${friendInfo.name}`;
    this.refreshCityData();
    let friend: FriendMessage = FriendModule.data.getFriendMessage(this._friendIds[this._index]);
    // let lock: FriendCitySkillMessage[] = [null];
    let datas: FriendCitySkillMessage[] = friend.citySkillList.concat(null);
    this._cityAdapter.setFriendData(datas, this._friendIds[this._index]);

    // 跳到最小的那个并且展开 --START
    let minIndex = 0;
    let minValue = 1000;
    for (let i = 0; i < datas.length; i++) {
      if (datas[i] && datas[i].skillAdd < minValue) {
        minValue = datas[i].skillAdd;
        minIndex = i;
      }
    }
    this.getNode("skill_list").getComponent(ExpandScrollView).expandIndex(minIndex);
    // 跳到最小的那个并且展开 --END
  }

  private refreshHero() {
    this._tabCity.getComponent(FmButton).selected = false;
    this._tabHero.getComponent(FmButton).selected = true;
    this._heroContent.active = true;
    this._heroImprove.active = true;
    this._cityContent.active = false;
    this._cityImprove.active = false;
    let heroIds = FriendModule.data.getFriendHeros(this._friendIds[this._index]);
    let index = 0;
    for (index; index < heroIds.length; index++) {
      this.getNode(`hero_${index + 1}`).getChildByName("hero_mask").active = true;

      this.getNode(`hero_${index + 1}`).getChildByName("not_hero").active = false;
      ResMgr.loadImage(
        `${BundleEnum.BUNDLE_COMMON_ITEM}?autoItem/item_${heroIds[index]}`,
        this.getNode(`hero_${index + 1}`)
          .getChildByPath("hero_mask/hero_img")
          .getComponent(Sprite),
        this
      );

      if (HeroModule.data.getHeroMessage(heroIds[index])) {
        let powerAdd = HeroModule.data.getHeroPowerAddByFriend(heroIds[index], this._friendIds[this._index]);
        this.getNode(`hero_${index + 1}`)
          .getChildByPath("label/hero_add")
          .getComponent(Label).string = `战力+${Formate.format(powerAdd)}`;
        this.getNode(`hero_${index + 1}`).getChildByName("label").active = true;
        this.getNode(`hero_${index + 1}`)
          .getComponentsInChildren(Sprite)
          .forEach((sprite: Sprite) => {
            sprite.color = math.color("#FFFFFF");
          });
      } else {
        this.getNode(`hero_${index + 1}`).getChildByName("label").active = false;
        this.getNode(`hero_${index + 1}`)
          .getComponentsInChildren(Sprite)
          .forEach((sprite: Sprite) => {
            sprite.color = math.color("#6e6e6e");
          });
      }
    }
    for (index; index < 6; index++) {
      this.getNode(`hero_${index + 1}`).getChildByName("label").active = false;
      this.getNode(`hero_${index + 1}`).getChildByName("not_hero").active = true;
      this.getNode(`hero_${index + 1}`).getChildByName("hero_color").active = false;
      this.getNode(`hero_${index + 1}`).getChildByName("hero_mask").active = false;
    }
    let friendInfo = FriendModule.config.getFriendById(this._friendIds[this._index]);
    this._subTitle.string = `战将技能`;
    this.getNode("lbl_dialog_subtitme").getComponent(Label).string = `${friendInfo.name}`;
    let friendMessage = FriendModule.data.getFriendMessage(this._friendIds[this._index]);
    let friendHeroSkill1Cur = FriendModule.config.getFriendHeroSkill(friendMessage.heroSkillList[0]);
    let friendHeroSkill1 = FriendModule.config.getFriendHeroSkill(friendMessage.heroSkillList[0] + 1);
    let friendHeroSkill2Cur = FriendModule.config.getFriendHeroSkill(friendMessage.heroSkillList[1]);
    let friendHeroSkill2 = FriendModule.config.getFriendHeroSkill(friendMessage.heroSkillList[1] + 1);

    let cost1 = friendHeroSkill1?.cost1 ?? 0;
    let cost2 = friendHeroSkill2?.cost2 ?? 0;
    let skill1Add1 = friendHeroSkill1Cur.value1[0][1];
    let skill1Add2 = friendHeroSkill1Cur.value1[1][1];
    let skill1Add3 = friendHeroSkill1Cur.value1[2][1];
    // let skill1NextAdd = friendHeroSkill1.value1[0][1];
    let skill2Add = friendHeroSkill2Cur.value2;
    // let skill2NextAdd = friendHeroSkill2.value2;
    this.getNode("skill1_lv").getComponent(Label).string = `等级：${friendHeroSkill1Cur.level}`;
    this.getNode("skill1_1").getComponent(Label).string = `战将生命 +${Formate.format(skill1Add1)}`;
    this.getNode("skill1_2").getComponent(Label).string = `战将攻击 +${Formate.format(skill1Add2)}`;
    this.getNode("skill1_3").getComponent(Label).string = `战将防御 +${Formate.format(skill1Add3)}`;
    // this.getNode("skill2_1").getComponent(Label).string = `(武将基础属性 +${Formate.format(skill1NextAdd)})`;

    this.getNode("skill2_lv").getComponent(Label).string = `等级：${friendHeroSkill2Cur.level}`;
    this.getNode("skill2_1").getComponent(Label).string = `战将生命 +${divide(skill2Add, 100)}%`;
    this.getNode("skill2_2").getComponent(Label).string = `战将攻击 +${divide(skill2Add, 100)}%`;
    this.getNode("skill2_3").getComponent(Label).string = `战将防御 +${divide(skill2Add, 100)}%`;
    // this._skill2NextLv.string = `(武将基础属性 +${divide(skill2NextAdd, 100)}%)`;

    // this._skill1Cost.setItemId(ItemEnum.缘分点_11, cost1, friendMessage.friendShip);
    // this._skill2Cost.setItemId(ItemEnum.缘分点_11, cost2, friendMessage.friendShip);
    this._skill1Cost.setCustom(ItemEnum.缘分点_11, "%c/%c", friendMessage.friendShip, cost1);
    this._skill2Cost.setCustom(ItemEnum.缘分点_11, "%c/%c", friendMessage.friendShip, cost2);
  }
  private on_click_next_arrow(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this._index = ++this._index % this._friendIds.length;
    log.log(this._index);
    this.getNode("skill_list").getComponent(ScrollableView).scrollToStart(false);
    if (this._heroImprove.active) {
      this.refreshHero();
    } else {
      this._indexSkillItem = 0;
      this._skillContent.destroyAllChildren();
      this.refreshCity();
      this._curPos = -1;
      this._currentExpand == null;
    }
    FriendModule.viewModel.index = this._index;
    this.registerBadgeNode();
  }
  private on_click_last_arrow(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.getNode("skill_list").getComponent(ScrollableView).scrollToStart(false);
    this._index = --this._index;
    if (this._index < 0) {
      this._index = this._friendIds.length - 1;
    }
    log.log(this._index);
    if (this._heroImprove.active) {
      this.refreshHero();
    } else {
      this._indexSkillItem = 0;
      this._skillContent.destroyAllChildren();
      this.refreshCity();
      this._curPos = -1;
      this._currentExpand == null;
    }
    FriendModule.viewModel.index = this._index;
    this.registerBadgeNode();
  }
  private on_click_tab_city(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.refreshCity();
  }
  private on_click_tab_hero(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    this.refreshHero();
  }
  private on_click_skill1_btn_up(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let friendMessage = FriendModule.data.getFriendMessage(this._friendIds[this._index]);
    let friendHeroSkill1 = FriendModule.config.getFriendHeroSkill(friendMessage.heroSkillList[0] + 1);
    let cost1 = friendHeroSkill1?.cost1 ?? 0;
    if (cost1 > friendMessage.friendShip) {
      TipMgr.showTip("缘分值不足");
      this.getNode("guide").active = true;
      TickerMgr.setTimeout(5, () => {
        if (isValid(this.node) == false) {
          return;
        }
        this.getNode("guide").active = false;
      });
      return;
    }

    FriendModule.api.levelUpHeroSkill(this._friendIds[this._index], 0, (data: FriendHeroSkillResponse) => {
      AudioMgr.instance.playEffect(FriendAudioName.Effect.战将技能升级成功);
      this.refreshHero();
    });
  }
  private on_click_skill2_btn_up(e: EventTouch) {
    AudioMgr.instance.playEffect(AudioName.Effect.按钮点击);
    let friendMessage = FriendModule.data.getFriendMessage(this._friendIds[this._index]);
    let friendHeroSkill2 = FriendModule.config.getFriendHeroSkill(friendMessage.heroSkillList[1] + 1);
    let cost2 = friendHeroSkill2?.cost2 ?? 0;
    if (cost2 > friendMessage.friendShip) {
      TipMgr.showTip("缘分值不足");
      return;
    }
    FriendModule.api.levelUpHeroSkill(this._friendIds[this._index], 1, (data: FriendHeroSkillResponse) => {
      AudioMgr.instance.playEffect(FriendAudioName.Effect.战将技能升级成功);
      this.refreshHero();
    });
  }

  private on_click_btn_xiangxixinxi() {
    AudioMgr.instance.playEffect(522);
    UIMgr.instance.showDialog(PlayerRouteName.UIHelpPop, { titleId: -1, desId: 17 });
  }
}
