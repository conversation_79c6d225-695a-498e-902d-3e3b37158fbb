import { Node, _decorator, instantiate, Prefab, sp, v3, UIOpacity, tween } from "cc";
import GameObject from "../../../lib/object/GameObject";
import { GORole } from "../role/GORole";
import { ActionInfo, ActionType, BattlePlayerRoundInfo, PlayerBackInfo, RoundInfo } from "../FightDefine";
import ResMgr from "../../../lib/common/ResMgr";
import MsgEnum from "../../event/MsgEnum";
import MsgMgr from "../../../lib/event/MsgMgr";
import FightManager, { scaleList } from "../manager/FightManager";
import TickerMgr from "../../../lib/ticker/TickerMgr";
import { MovementInfo, STATE } from "../section/StateSection";
import { JsonMgr } from "../../mgr/JsonMgr";
import { TipManager } from "../manager/TipManager";
import { AnimationSection } from "../section/AnimationSection";
import Logger, { LOG_LEVEL } from "db://assets/GameScrpit/lib/utils//Logger";
import { Sleep } from "../../GameDefine";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
const log = Logger.getLoger(LOG_LEVEL.STOP);
const { ccclass, property } = _decorator;

export enum HPMAXSTATE {
  NONE = 0,
  ADD = 1,
  MIN = 2,
}

export enum HPSTATE {
  NONE = 0,
  ADD = 1,
  MIN = 2,
}

export enum BattleSceneEnum {
  NULL = 0,
  CHAPTER = 1,
  COMPETE = 2,
  HUNT_PET = 3,
  HUNT_BOSS = 4,
  CLUB_BOSS = 5,
  FRACTURE_MONSTER = 6,

  WATCH = 101,
}

@ccclass("GamePlay")
export default abstract class GamePlay extends GameObject {
  protected _allRole: Map<number, GORole> = new Map<number, GORole>();
  public get allRole(): Map<number, GORole> {
    return this._allRole;
  }

  protected _fightShowTimeTickerId: number;

  protected _roundIndex: number = 0;
  protected _fightData;
  protected _posMap;

  private _startFight: Node;
  private _startFightPrefab: Prefab;

  private _roundTickerId: number;
  protected override onStart(): void {
    super.onStart();
    MsgMgr.emit(MsgEnum.ON_FIGHT_ROUND_UPDATE, 0);
    this.play();
  }

  protected override onRemove(): void {
    super.onRemove();
    //log.log("清理异常处理定时qi");
    TickerMgr.clearInterval(this._fightShowTimeTickerId);
    TickerMgr.clearTimeout(this._roundTickerId);
    this.exit();
  }

  public abstract play();
  public abstract exit();

  protected newQueueList() {
    this._roundIndex = 0;
    MsgMgr.emit(MsgEnum.ON_FIGHT_ROUND_UPDATE, 0);
    /**挑战者 */
    let challenger = this._fightData.c;
    /**被挑战者 */
    let underChallenger = this._fightData.d;

    let queueList: Array<PlayerBackInfo> = [
      {
        battlePlayerBackInfo: challenger,
        pos: this._posMap.get(challenger.b),
        dir: challenger.b,
        dbId: challenger.d,
        isPlayer: true,
        sceneId: this._fightData.a,
      },
      {
        battlePlayerBackInfo: underChallenger,
        pos: this._posMap.get(underChallenger.b),
        dir: underChallenger.b,
        dbId: underChallenger.d,
        isPlayer: false,
        sceneId: this._fightData.a,
      },
    ];
    return queueList;
  }

  protected async loadRolObject(queueList: Array<PlayerBackInfo>) {}

  protected async startGame() {
    await new Promise(async (res) => {
      let path = "resources?prefab/effect/startFight";
      this._startFightPrefab = await ResMgr.loadPrefabSync(path);
      if (this.isValid == false) {
        return;
      }
      this._startFight = instantiate(this._startFightPrefab);
      FightManager.instance.main.addChild(this._startFight);
      this._startFight.walk((child) => (child.layer = this.layer));
      let skt = this._startFight.getChildByName("skt").getComponent(sp.Skeleton);

      skt.setCompleteListener((trackEntry: sp.spine.TrackEntry) => {
        if ("animation" == trackEntry.animation.name) {
          res(true);
          this.fightShowTimeFun();
        }
      });

      /**动画播放中特殊的消息事件 */
      skt.setEventListener((animation, event) => {
        if (event["data"].name == "appear") {
          this._allRole.forEach((val) => {
            val.fightStartAni();
          });
        }
      });
      // tween(FightManager.instance.main.getChildByName("main"))
      //   .to(0.1, { scale: v3(0.8, 0.8, 1) })
      //   .to(0.2, { scale: v3(1.2, 1.2, 1) })
      //   .to(0.3, { scale: v3(1, 1, 1) })
      //   .start();
      AudioMgr.instance.playEffect(513);
      skt.setAnimation(0, "animation", false);
      await Sleep(0.15);
      FightManager.instance.main.getChildByName("main").setScale(1, 1, 1);
    });
  }

  protected fightShowTimeFun() {
    let index = 0;
    this._allRole.forEach((val) => {
      if (val.fightShowTime == true) {
        index++;
      }
    });

    if (index >= this._allRole.size) {
      this._startFight.removeFromParent();
      this._startFight.destroy();
      this._startFightPrefab.decRef();
      TickerMgr.clearInterval(this._fightShowTimeTickerId);
    }
  }

  protected async roundFight(actionInfoList: Array<RoundInfo>) {
    for (this._roundIndex; this._roundIndex < actionInfoList.length; ) {
      if (FightManager.instance.fightOver == true) {
        return;
      }

      let roundInfo: RoundInfo = actionInfoList[this._roundIndex];
      if (!roundInfo) {
        return;
      }
      //log.error("回合===", roundInfo.a);
      MsgMgr.emit(MsgEnum.ON_FIGHT_ROUND_UPDATE, roundInfo.a);
      await this.upAllRoleState(roundInfo.b);
      await this.roundAtk(roundInfo.c);
      await this._roundIndex++;
    }

    if (this._roundTickerId) {
      TickerMgr.clearTimeout(this._roundTickerId);
      this._roundTickerId = null;
    }
  }

  /**同步更新双方服务端血量 */
  protected async upAllRoleState(battlePlayerRoundInfo: { [key: number]: BattlePlayerRoundInfo }) {
    this._allRole.forEach((val: GORole, key: number) => {
      let data = battlePlayerRoundInfo[key];
      if (!data) return;
      val.roundStartUpDetail(data);
      val.emitMsg("OnUpRound");
    });
  }

  protected async roundAtk(actionInfoList: Array<ActionInfo>) {
    for (let i = 0; i < actionInfoList.length; i++) {
      // 清理 异常卡死强制进入一下一回合定时器
      if (this._roundTickerId) {
        TickerMgr.clearTimeout(this._roundTickerId);
        this._roundTickerId = null;
      }
      ///log.error("动作===", actionInfoList[i]);
      let time = 0.5 / scaleList[FightManager.instance.speed];
      if (i > 0 && actionInfoList[i].a == actionInfoList[i - 1].a) {
        time = 0.1;
      } else if (actionInfoList[i].c != 1) {
        time = 0.3;
      }
      await new Promise((res) => TickerMgr.setTimeout(time, res));

      let info: ActionInfo = actionInfoList[i];
      await this.roundMsg(info);
    }
  }

  protected async roundMsg(info: ActionInfo) {
    await new Promise(async (resolve) => {
      if (FightManager.instance.fightOver == true) {
        resolve(1);
        return;
      }

      if (!this._allRole) {
        resolve(1);
        return;
      }

      switch (info.c) {
        case ActionType.NORMAL_ATTACK:
          await this.atkCommon(info, resolve);
          break;
        case ActionType.COMBO:
          let double_path = "resources?prefab/effectLab/hint_lab_double";
          FightManager.instance.getSection(TipManager).callTip(double_path, this._allRole.get(info.a));
          await this.atkCommon(info, resolve);
          break;
        case ActionType.COUNTER:
          let backpunch_path = "resources?prefab/effectLab/hint_lab_backpunch";
          FightManager.instance.getSection(TipManager).callTip(backpunch_path, this._allRole.get(info.a));
          await this.atkCommon(info, resolve);
          break;
        case ActionType.CRITICAL:
          await this.atkCommon(info, resolve);
          break;
        case ActionType.DOOGE:
          await this.atkDooge(info, resolve);
          break;
        case ActionType.FORBIDON:
          await this.atkForbidon(info, resolve);
          break;

        default:
          //log.log("没有进入case的类型===", info.c);
          //log.log("没有进入case的类型===", info);
          break;
      }
    });
  }

  /**普通攻击 --- 切换攻击角色的状态*/
  protected async atkCommon(info: ActionInfo, resolve: Function) {
    let role1 = this._allRole.get(info.a);
    role1.setSiblingIndex(1);
    let role2 = this._allRole.get(info.b);
    role2.setSiblingIndex(0);

    let spineShowDB = JsonMgr.instance.jsonList.c_spineShow[role1.getSpineId()];
    let skillIdList = spineShowDB.skillIdList;

    let index = Math.floor(Math.random() * skillIdList.length);
    let skillId = skillIdList[index];

    let obj: MovementInfo = {
      hurtRole: role2,
      skillId: skillId,
      actionType: info.c,
      movementInfo: info.d,
      resolveBack: resolve,
    };

    //异常卡死强制进入一下一回合定时器
    let time = 5;
    let db = JsonMgr.instance.jsonList.c_skillShow[skillId];
    if (db) {
      time = role1.getSection(AnimationSection).getAnimationTime(db.actName) + 3;
    } else {
      time = 8;
    }
    this._roundTickerId = TickerMgr.setTimeout(time / scaleList[FightManager.instance.speed], () => {
      log.error("出错了，强制进入下一个动作");
      resolve(1);
    });

    await role1.emitMsg("OnMovementInfo", obj);
    await this.changeRoleAtkState(skillId, role1);
  }

  protected changeRoleAtkState(skillId: number, role: GORole) {
    let db = JsonMgr.instance.jsonList.c_skillShow[skillId];
    if (!db) {
      log.error("技能不存在", skillId);
      return;
    }
    switch (db.actName) {
      case "boss_attack1":
        role.emitMsg("OnSwitchState", STATE.ATK1);
        break;
      case "boss_attack2_1":
        role.emitMsg("OnSwitchState", STATE.ATK2_1);
        break;
      case "boss_attack3":
        role.emitMsg("OnSwitchState", STATE.ATK3);
        break;
    }
  }

  private async atkForbidon(info: ActionInfo, resolve: Function) {
    let role1 = this._allRole.get(info.a);
    let role2 = this._allRole.get(info.b);

    let stunBuffId = JsonMgr.instance.jsonList.c_spineShow[role2.getSpineId()].stunBuffId;
    let actInfo = info.d[role1.getDir()];
    let stun = actInfo.c || 0;
    let time = 0.5 / scaleList[FightManager.instance.speed];
    await new Promise((res) => TickerMgr.setTimeout(time, res));
    role1.emitMsg("OnChangeBuff", { id: stunBuffId, roundCount: stun });
    resolve(true);
  }

  private atkDooge(info: ActionInfo, resolve: Function) {
    let b = info.b;
    let hurtObj = { a: 0, b: 0, c: 0 };
    info.d = Object.create(null);
    info.d[b] = hurtObj;
    this.atkCommon(info, resolve);
  }

  protected async fightSkip() {
    this._roundIndex = this._fightData.e.length;
    this._allRole.forEach((val) => {
      val.renderSection;
      const render = val.renderSection.getRender();
      render.setPosition(v3(0, 0, 0));
      if (render.getComponent(UIOpacity)) {
        const opacityComp = render.getComponent(UIOpacity);
        opacityComp.opacity = 255;
      }
    });

    await new Promise((res) => TickerMgr.setTimeout(0.5 / scaleList[FightManager.instance.speed], res));
    MsgMgr.emit(MsgEnum.ON_FIGHT_END);
  }

  protected upFightSpeed() {
    this._allRole.forEach((val, key) => {
      val.setTimeScale();
    });
  }
}
