import { _decorator, math, Node, UITransform, v4 } from "cc";
import { Rect } from "../../../../lib/utils/Rect";
import { LayoutManager } from "../LayoutManager";
import { ViewHolder } from "../ListAdapter";
const { ccclass, property } = _decorator;
// 扇形布局
@ccclass("FoucsLayoutManager")
export class FoucsLayoutManager extends LayoutManager {
  private _foucsPosition: number = 0;
  private _updateLayout: boolean = true;
  private _lastFoucsNode: Node;
  private _space: number = -80;

  protected onAttach(): void {
    this.viewholders.wrapAroundIndex = true;
  }
  protected onLayout(changed: boolean, lastRect: Rect, offsetX: number, offsetY: number, isFling: boolean): Rect {
    for (let i = 0; i < this.children.size(); i++) {
      let child = this.children.get(i);
      let height = child.getComponent(UITransform).height;
      let anchorY = child.getComponent(UITransform).anchorY;
      let childX = child.position.x + offsetX;
      let y = this.getBorderTop() - height * (1 - anchorY);
      this.updateChildPosition(child, childX, y);
    }
    if (this.children.size() == 0 || this._updateLayout) {
      this._updateLayout = false;
      let newNode = this.addNode(this._foucsPosition);
      if (newNode) {
        let width = newNode.getComponent(UITransform).width;
        let height = newNode.getComponent(UITransform).height;
        let anchorX = newNode.getComponent(UITransform).anchorX;
        let anchorY = newNode.getComponent(UITransform).anchorY;
        let x = 0; //anchorX * width; //this.getBorderLeft() +
        let y = this.getBorderTop() - height * (1 - anchorY);
        this.updateChildPosition(newNode, x, y);
        this._lastFoucsNode = newNode;
      } else {
        return lastRect;
      }
    }
    let left = this.getNodeLeft(this.children.peekFront());
    while (left > this.getBorderLeft()) {
      //
      let newNode = this.addNodeToHeader();
      let width = newNode.getComponent(UITransform).width;
      let height = newNode.getComponent(UITransform).height;
      let anchorX = newNode.getComponent(UITransform).anchorX;
      let anchorY = newNode.getComponent(UITransform).anchorY;
      let x = left - (width * anchorX + this._space);
      let y = this.getBorderTop() - height * (1 - anchorY);
      this.updateChildPosition(newNode, x, y);

      left = this.getNodeLeft(newNode);
    }
    let right = this.getNodeRight(this.children.peekRear());
    while (right < this.getBorderRight()) {
      //
      let newNode = this.addNodeToTail();
      let width = newNode.getComponent(UITransform).width;
      let height = newNode.getComponent(UITransform).height;
      let anchorX = newNode.getComponent(UITransform).anchorX;
      let anchorY = newNode.getComponent(UITransform).anchorY;
      let x = right + (width * (1 - anchorX) + this._space);
      let y = this.getBorderTop() - height * (1 - anchorY);
      this.updateChildPosition(newNode, x, y);
      right = this.getNodeRight(newNode);
    }
    for (let i = 0; i < this.children.size(); i++) {
      let child = this.children.get(i);
      if (child == this._lastFoucsNode) {
        child.setSiblingIndex(this.children.size() - 1);
        continue;
      }
      if (child.position.x < this._lastFoucsNode.position.x) {
        child.setSiblingIndex(i);
      } else {
        child.setSiblingIndex(0);
      }
    }

    return new Rect(
      this.getBorderLeft(),
      this.getNodeTop(this.children.peekFront()),
      this.getBorderRight(),
      this.getNodeBottom(this.children.peekRear())
    );
  }
  protected onUpdateChildPosition(child: Node, x: number, y: number): void {
    let realX = x;
    let realY = y - 160 * Math.tan(Math.abs(x / this.getBorderLeft()));

    // child.getComponent(UITransform).width = 177 * (1 - Math.abs(x / this.getBorderLeft()));
    super.onUpdateChildPosition(child, realX, realY);
    let angle = 60 * (x / this.getBorderLeft());
    child.angle = angle;
    if (!this._lastFoucsNode || Math.abs(child.position.x) < Math.abs(this._lastFoucsNode.position.x)) {
      this._lastFoucsNode = child;
    }
  }
}
