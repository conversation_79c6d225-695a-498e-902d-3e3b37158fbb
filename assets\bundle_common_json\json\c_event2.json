{"101": {"id": 101, "type": 1, "actType": 1, "unlock": [2, 20], "debuff": [1, 5000], "prefabPath": ["bundle_g_gameMap", "prefab/event_base/path_thief_101"], "word": [351, 352, 353, 354, 355, 356, 357, 358, 359, 360], "attackNum": [8, 13], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "102": {"id": 102, "name": "羬羊", "type": 3, "actType": 1, "unlock": [2, 30], "prefabPath": ["bundle_g_event_action", "prefab/components/event_102"], "word02": [650], "attackNum": [5, 6], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "201": {"id": 201, "type": 1, "actType": 2, "coldTime": 86400, "unlock": [2, 50], "debuff": [1, 5000], "prefabPath": ["bundle_g_gameMap", "prefab/event_base/path_thief_201"], "word": [351, 352, 353, 354, 355, 356, 357, 358, 359, 360], "attackNum": [8, 13], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "202": {"id": 202, "name": "羬羊", "type": 3, "actType": 2, "coldTime": 86400, "unlock": [2, 50], "prefabPath": ["bundle_g_event_action", "prefab/components/event_102"], "word02": [650], "attackNum": [5, 10], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "203": {"id": 203, "name": "山膏", "type": 3, "actType": 2, "coldTime": 86400, "unlock": [2, 50], "prefabPath": ["bundle_g_event_action", "prefab/components/event_103"], "word02": [650], "attackNum": [5, 10], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "501": {"id": 501, "type": 5, "actType": 2, "unlock": [3, 101], "attackNum": [1, 1], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "502": {"id": 502, "type": 5, "actType": 2, "unlock": [3, 103], "attackNum": [1, 1], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "503": {"id": 503, "type": 5, "actType": 2, "unlock": [3, 104], "attackNum": [1, 1], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "504": {"id": 504, "type": 5, "actType": 2, "unlock": [3, 205], "attackNum": [1, 1], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "505": {"id": 505, "type": 5, "actType": 2, "unlock": [3, 302], "attackNum": [1, 1], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "506": {"id": 506, "type": 5, "actType": 2, "unlock": [3, 1216], "attackNum": [1, 1], "rewardRateList": [[1075, 1], [1004, 1], [1005, 3], [1069, 1], [1071, 1], [1072, 1], [1015, 1], [1016, 1], [1202, 10]]}, "-1": {"id": -1, "name": "", "type": 0, "actType": 0, "coldTime": 0, "unlock": [], "debuff": [], "prefabPath": [], "spineId": "", "word": [], "word02": [], "attackNum": [], "rewardRateList": [], "time": [10, 18], "max": 4, "cycleOpen": [2, 10]}}