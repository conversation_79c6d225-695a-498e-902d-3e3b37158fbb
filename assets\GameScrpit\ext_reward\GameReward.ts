import { _decorator, Component, Node } from "cc";
import { BaseCtrl } from "../../platform/src/core/BaseCtrl";
import MsgEnum from "../game/event/MsgEnum";
import ToolExt from "../game/common/ToolExt";
import { JsonMgr } from "../game/mgr/JsonMgr";
import { BundleEnum } from "../game/bundleEnum/BundleEnum";
import { TipsMgr } from "../../platform/src/TipsHelper";
import MsgMgr from "../lib/event/MsgMgr";
import { RewardRouteEnum } from "./RewardDefine";
import { SyncQueue } from "../../platform/src/lib/utils/Deque";
import { LeaderFundMessage } from "../game/net/protocol/Activity";
const { ccclass, property } = _decorator;

interface RewardInfo {
  routeName: string;
  itemMap: { id: number; num: number }[];
}

@ccclass("GameReward")
export class GameReward extends BaseCtrl {
  private _popList: RewardInfo[] = [];
  private _shenJitasks = new SyncQueue<Number>(10);
  private _shenjiFinish = new SyncQueue<Number>(1);
  start() {
    super.start();

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopItemAwardPop, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopItemAwardPop",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopHeroAwardPop, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopHeroAwardPop",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopFriendAwardPop, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopFriendAwardPop",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopPetAwardPop, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopPetAwardPop",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopPlayerLevelUpRes, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopPlayerLevelUpRes",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopEnergyUpgradeRes, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopEnergyUpgradeRes",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopShenjiPop, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopShenjiPop",
      parentNode: TipsMgr.topRouteCtrl.nodeHighLayer,
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopSystemOpen, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopSystemOpen",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopJinJieCG, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopJinJieCG",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopItemFlyAni, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopItemFlyAni",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopGetItem, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopGetItem",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopGetRes, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopGetRes",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopItemAwardFly, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopItemAwardFly",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopLevelUp, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopLevelUp",
      nextHop: [],
      exit: "",
    });

    TipsMgr.topRouteCtrl.regRoute(RewardRouteEnum.TopItemDetail, {
      bundle: BundleEnum.BUNDLE_EXT_REWARD,
      url: "prefab/top/TopItemDetail",
      nextHop: [],
      exit: "",
      isSingleton: true,
    });

    MsgMgr.on(MsgEnum.ON_GET_AWARD, this.addReward, this);

    this.consumeShenjiTask();
    MsgMgr.on(MsgEnum.ON_TRIGGER_SHENJI, this.addShenjiPop, this);
  }

  protected onDestroy(): void {
    MsgMgr.off(MsgEnum.ON_GET_AWARD, this.addReward, this);
    MsgMgr.off(MsgEnum.ON_TRIGGER_SHENJI, this.addShenjiPop, this);
    super.onDestroy();
  }

  private async consumeShenjiTask() {
    while (true) {
      let taskId = await this._shenJitasks.dequeue();
      let shenji = JsonMgr.instance.jsonList.c_templeShenji[Number(taskId)];
      TipsMgr.topRouteCtrl.show(RewardRouteEnum.TopShenjiPop, { title: shenji.title, desc: shenji.text }, () => {
        this._shenjiFinish.enqueue(taskId);
      });
      let finish = await this._shenjiFinish.dequeue();
    }
  }

  private addShenjiPop(shenjiId: number) {
    this._shenJitasks.enqueue(shenjiId);
  }

  addReward(data: any) {
    TipsMgr.setEnableTouch(false, 0.3);
    let arr1 = ToolExt.traAwardItemMapList(data.itemList || data.rewardList);
    const transformList = data.transformList || [];

    // 新增转换处理逻辑
    let currentOriginalId = -1;
    for (let i = 0; i < transformList.length; i++) {
      const currentValue = transformList[i];

      // 发现原始ID标记（当前值后面跟着-1）
      if (i + 1 < transformList.length && transformList[i + 1] === -1) {
        // 处理上一个原始ID的后续替换项
        if (currentOriginalId !== -1) {
          this.processReplacements(arr1, currentOriginalId);
        }

        // 设置新的原始ID
        currentOriginalId = currentValue;
        i++; // 跳过-1标记

        // 处理当前原始ID的扣除
        const foundIndex = arr1.findIndex((item) => item.id === currentOriginalId);
        if (foundIndex > -1) {
          arr1[foundIndex].num -= 1; // 固定扣除1个
          if (arr1[foundIndex].num <= 0) {
            arr1.splice(foundIndex, 1);
          }
        }
      }
      // 处理替换项（当存在有效原始ID时）
      else if (currentOriginalId !== -1) {
        // 成对读取替换物品ID和数量
        if (i + 1 < transformList.length) {
          const newId = currentValue;
          const newNum = transformList[i + 1];

          // 合并或添加新物品
          const existItem = arr1.find((item) => item.id === newId);
          if (existItem) {
            existItem.num += newNum;
          } else {
            arr1.push({ id: newId, num: newNum });
          }

          i++; // 跳过数量值
        }
      }
    }

    // 过滤无效道具（数量大于0）
    let arr = arr1.filter((e) => e.num > 0);

    // 后续分类逻辑保持不变
    let heroList = [];
    let friendList = [];
    let petList = [];
    let itemList = [];
    let leaderSkinList = [];

    for (let i = 0; i < arr.length; i++) {
      let cfgItem = JsonMgr.instance.getConfigItem(arr[i].id);
      if (!cfgItem) {
        // 找不到直接放道具里
        itemList.push(arr[i]);
      } else if (cfgItem.goodsType == 3) {
        heroList.push(arr[i]);
      } else if (cfgItem.goodsType == 6) {
        friendList.push(arr[i]);
      } else if (cfgItem.goodsType == 5) {
        petList.push(arr[i]);
      } else if (cfgItem.goodsType == 11) {
        leaderSkinList.push(arr[i]);
      } else {
        itemList.push(arr[i]);
      }
    }

    if (heroList.length > 0) {
      this._popList.push({
        routeName: RewardRouteEnum.TopHeroAwardPop,
        itemMap: heroList,
      });
    }

    if (friendList.length > 0) {
      this._popList.push({
        routeName: RewardRouteEnum.TopFriendAwardPop,
        itemMap: friendList,
      });
    }

    if (petList.length > 0) {
      this._popList.push({
        routeName: RewardRouteEnum.TopPetAwardPop,
        itemMap: petList,
      });
    }

    if (itemList.length > 0) {
      this._popList.push({
        routeName: RewardRouteEnum.TopItemAwardPop,
        itemMap: itemList,
      });
    }

    if (leaderSkinList.length > 0) {
      this._popList.push({
        routeName: RewardRouteEnum.TopJinJieCG,
        itemMap: leaderSkinList,
      });
    }

    this.showNext();
  }

  // 新增处理方法
  private processReplacements(arr: { id: number; num: number }[], originalId: number) {
    // 这里可以添加额外的处理逻辑（如果需要）
  }

  showNext() {
    if (this._popList.length > 0) {
      let popInfo = this._popList[0];

      let oneList: string[] = [
        RewardRouteEnum.TopHeroAwardPop,
        RewardRouteEnum.TopFriendAwardPop,
        RewardRouteEnum.TopPetAwardPop,
        RewardRouteEnum.TopJinJieCG,
      ];

      if (oneList.includes(popInfo.routeName)) {
        if (popInfo.itemMap.length > 0) {
          let itemMap = popInfo.itemMap.shift();
          TipsMgr.topRouteCtrl.show(popInfo.routeName, itemMap.id, () => {
            this.showNext();
          });
        } else {
          this._popList.shift();
          this.showNext();
        }

        return;
      }

      popInfo = this._popList.shift();
      TipsMgr.topRouteCtrl.show(popInfo.routeName, popInfo.itemMap, () => {
        this.showNext();
      });
    } else {
      // 用于引导的消息回调
      MsgMgr.emit(MsgEnum.ON_GUIDE_NEXT, "REWARD_END");
    }
  }
}
