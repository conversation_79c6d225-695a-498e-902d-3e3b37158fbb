import { _decorator, instantiate, isValid, Label, ProgressBar, sp, tween } from "cc";
import { UINode } from "db://assets/GameScrpit/lib/ui/UINode";
import { BundleEnum } from "db://assets/GameScrpit/game/bundleEnum/BundleEnum";
import { LangMgr } from "../../mgr/LangMgr";
import { UIMgr } from "../../../lib/ui/UIMgr";
import FmUtils from "../../../lib/utils/FmUtils";
import { TipsMgr } from "db://assets/platform/src/TipsHelper";
import { SpineUtil } from "../../../../platform/src/lib/utils/SpineUtil";
import { AudioMgr } from "db://assets/platform/src/AudioHelper";
import { MainTaskAudioName } from "../../../module/mainTask/MainTaskConfig";
import { Sleep } from "../../GameDefine";
import GuideMgr from "../../../ext_guide/GuideMgr";
const { ccclass, property } = _decorator;

/**
 * hopewsw
 * Thu Apr 03 2025 15:09:17 GMT+0800 (中国标准时间)
 * db://assets/GameScrpit/game/ui/mainTask/UITaskAutoClickPop.ts
 * https://docs.cocos.com/creator/3.8/manual/zh/
 *
 */

@ccclass("UITaskAutoClickPop")
export class UITaskAutoClickPop extends UINode {
  private _spineBg: sp.Skeleton;

  private _totalSuccessCount = 0;
  private _totalCount = 0;
  private _rewardItemsList: number[] = [];

  protected prefab(): string {
    return `${BundleEnum.BUNDLE_G_MAINTASK}?prefab/ui/UITaskAutoClickPop`;
  }

  //=================================================
  public init(args: any): void {
    super.init(args);
    this._totalSuccessCount = args.totalSuccessCount;
    this._totalCount = args.totalCount;
    this._rewardItemsList = args.rewardItemsList;
  }

  protected onEvtShow(): void {
    TipsMgr.setEnableTouch(false, 3, false);

    // 播放动画
    this._spineBg = this.getNode("spine_tanchuang_action_task").getComponent(sp.Skeleton);
    SpineUtil.playSpine(this._spineBg, "tanchuang_appear", false);

    this._spineBg.setEventListener((animation, event) => {
      if (event["data"].name == "tc_1") {
        this.getNode("node_title").active = true;
      } else if (event["data"].name == "tc_2") {
        this.getNode("bg_zuoqimingzi").active = true;
      } else if (event["data"].name == "tc_3") {
        this.getNode("node_progress").active = true;
      } else if (event["data"].name == "tc_4") {
        this.getNode("TY_bg_9g_tanchuangdi").active = true;
      } else if (event["data"].name == "tc_5") {
        this.getNode("btn_go").active = true;
      }
    });

    const dt = SpineUtil.getSpineDuration(this._spineBg, "tanchuang_appear");
    tween(this.node)
      .delay(dt)
      .call(() => {
        this.getNode("btn_close").active = true;
        TipsMgr.setEnableTouch(true);
      })
      .start();

    this.getNode("dialog_title").getComponent(Label).string = LangMgr.txMsgCode(179);
    this.getNode("lbl_task_title").getComponent(Label).string = LangMgr.txMsgCode(180);
    this.getNode("lbl_task").getComponent(Label).string = LangMgr.txMsgCode(244, [this._totalCount]);
    this.getNode("lbl_go").getComponent(Label).string = LangMgr.txMsgCode(140);

    this.getNode("lbl_progress").getComponent(Label).string = `${this._totalSuccessCount}/${this._totalCount}`;

    this.getNode("ProgressBar").getComponent(ProgressBar).progress = this._totalSuccessCount / this._totalCount;

    for (let i = 0; i < this._rewardItemsList.length; i += 2) {
      let node = instantiate(this.getNode("Item"));
      node.active = true;
      this.getNode("item_lay").addChild(node);
      FmUtils.setItemNode(node, this._rewardItemsList[i], this._rewardItemsList[i + 1]);
    }
  }

  protected async onCloseAct(actEndCall) {
    if (!isValid(this.node)) {
      return;
    }
    this.getNode("btn_close").active = false;

    // 播放动画
    this._spineBg = this.getNode("spine_tanchuang_action_task").getComponent(sp.Skeleton);
    SpineUtil.playSpine(this._spineBg, "tanchuang_disappear", false);

    this._spineBg.setEventListener((animation, event) => {
      if (event["data"].name == "tc_1") {
        this.getNode("node_title").active = false;
      } else if (event["data"].name == "tc_2") {
        this.getNode("bg_zuoqimingzi").active = false;
      } else if (event["data"].name == "tc_3") {
        this.getNode("node_progress").active = false;
      } else if (event["data"].name == "tc_4") {
        this.getNode("TY_bg_9g_tanchuangdi").active = false;
      } else if (event["data"].name == "tc_5") {
        this.getNode("btn_go").active = false;
      }
    });

    const dt = SpineUtil.getSpineDuration(this._spineBg, "tanchuang_disappear");
    await Sleep(dt);
    actEndCall && actEndCall();
  }

  on_click_btn_close() {
    AudioMgr.instance.playEffect(103);
    UIMgr.instance.back();
  }

  on_click_btn_go() {
    AudioMgr.instance.playEffect(MainTaskAudioName.Effect.点击前往按钮);
    UIMgr.instance.back();

    const wPos = this.node.getWorldPosition();
    setTimeout(() => {
      GuideMgr.startGuide({
        stepId: 49,
        startPos: wPos,
      });
    }, 500);
  }
}
